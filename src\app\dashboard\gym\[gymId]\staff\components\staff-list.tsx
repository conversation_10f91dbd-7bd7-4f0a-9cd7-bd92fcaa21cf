'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Plus, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Staff } from '@/types/staff';
import { deleteStaff } from '@/lib/actions/all-actions';
import { StaffForm } from './staff-form';
import { formatCurrency } from '@/lib/utils';

interface StaffListProps {
  staffs: Staff[];
  gymId: string;
}

export function StaffList({ staffs, gymId }: StaffListProps) {
  const [deletingStaffId, setDeletingStaffId] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null);

  const handleDelete = async (staffId: string) => {
    try {
      setDeletingStaffId(staffId);
      const response = await deleteStaff(staffId, gymId);

      if (response.success) {
        toast.success('Personel başarıyla silindi');
        // deleteStaff fonksiyonu revalidatePaths ile cache'i otomatik günceller
      } else {
        toast.error(response.error || 'Personel silinirken bir hata oluştu');
      }
    } catch (error) {
      toast.error('Personel silinirken bir hata oluştu');
    } finally {
      setDeletingStaffId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getStaffTypeLabel = (staffType: string) => {
    const types: Record<string, string> = {
      cleaner: 'Temizlikçi',
      security: 'Güvenlik',
      reception: 'Resepsiyon',
      maintenance: 'Bakım',
      other: 'Diğer',
    };
    return types[staffType] || 'Diğer';
  };

  return (
    <div className="space-y-4">
      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {staffs.length > 0
            ? `Toplam ${staffs.length} personel`
            : 'Personel listesi'}
        </p>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Personel Ekle
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Yeni Personel Ekle</DialogTitle>
            </DialogHeader>
            <StaffForm
              gymId={gymId}
              initialData={null}
              onSuccess={() => {
                setIsAddDialogOpen(false);
                toast.success('Personel başarıyla eklendi');
                // createStaff fonksiyonu revalidatePaths ile cache'i otomatik günceller
              }}
              onCancel={() => setIsAddDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Staff Dialog */}
        <Dialog
          open={!!editingStaff}
          onOpenChange={open => !open && setEditingStaff(null)}
        >
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Personel Düzenle</DialogTitle>
            </DialogHeader>
            {editingStaff && (
              <StaffForm
                gymId={gymId}
                initialData={editingStaff}
                onSuccess={() => {
                  setEditingStaff(null);
                  toast.success('Personel başarıyla güncellendi');
                  // updateStaff fonksiyonu revalidatePaths ile cache'i otomatik günceller
                }}
                onCancel={() => setEditingStaff(null)}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      {/* Staff Table */}
      {staffs.length === 0 ? (
        <div className="py-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Henüz personel eklenmemiş
          </p>
          <p className="mt-2 text-sm text-gray-400 dark:text-gray-500">
            Yukarıdaki &quot;Personel Ekle&quot; butonunu kullanarak ilk
            personeli ekleyebilirsiniz.
          </p>
        </div>
      ) : (
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ad Soyad</TableHead>
                <TableHead>Personel Tipi</TableHead>
                <TableHead>İşe Başlama</TableHead>
                <TableHead>Maaş</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {staffs.map((Staff: Staff) => (
                <TableRow key={Staff.id}>
                  <TableCell className="font-medium">
                    {Staff.name} {Staff.surname}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getStaffTypeLabel(Staff.staff_type)}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(Staff.hire_date)}</TableCell>
                  <TableCell>
                    {Staff.salary_amount
                      ? formatCurrency(Staff.salary_amount)
                      : '-'}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">Aktif</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-muted-foreground hover:text-foreground h-8 w-8 p-0"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Personel işlemleri</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => setEditingStaff(Staff)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Düzenle
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem
                                className="cursor-pointer text-red-600 focus:bg-red-50 focus:text-red-700"
                                onSelect={e => e.preventDefault()}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Personeli Sil
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Personeli Sil
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  {Staff.name} {Staff.surname} adlı personeli
                                  silmek istediğinizden emin misiniz? Bu işlem
                                  geri alınamaz.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>İptal</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(Staff.id)}
                                  disabled={deletingStaffId === Staff.id}
                                >
                                  {deletingStaffId === Staff.id
                                    ? 'Siliniyor...'
                                    : 'Sil'}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
