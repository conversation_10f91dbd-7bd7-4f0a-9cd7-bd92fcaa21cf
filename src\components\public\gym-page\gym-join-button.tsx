"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { User } from "@supabase/supabase-js";
import { memo, useState } from "react";
import { gymToasts } from "@/lib/utils/toast-utils";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UserPlus, Check } from "lucide-react";
import { sendMemberJoinRequest } from "@/lib/actions/gym_invitations/invitation-actions";

interface GymJoinButtonProps {
  membershipStatus: 'none' | 'pending' | 'active'
  gymId: string;
  user: User | null;
  hasMemberRole?: boolean;
}


export const GymJoinButton = memo(function GymJoinButton({
  membershipStatus,
  gymId,
  user,
  hasMemberRole = false,
}: GymJoinButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [localMembershipStatus, setLocalMembershipStatus] = useState(membershipStatus);
  const handleJoinRequest = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      const result = await sendMemberJoinRequest(gymId);

      if (result.success) {
        gymToasts.memberJoinSuccess();
        setLocalMembershipStatus('pending');
      } else {
        gymToasts.memberJoinError();
      }
    } catch (error) {
      console.error('Join request error:', error);
      gymToasts.memberJoinError();
    } finally {
      setIsLoading(false);
    }
  };
  const hasActiveMembership = localMembershipStatus === 'active';
  const requestSent = localMembershipStatus === 'pending';

  if (hasActiveMembership) {
    return (
      <Button
        size="lg"
        disabled
        className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm border border-white/30 duration-300"
      >
        Zaten Üyesiniz
      </Button>
    );
  }

  // Kullanıcı giriş yapmış ama member rolüne sahip değilse
  if (user && !hasMemberRole) {
    return (
      <Button
        size="lg"
        disabled
        className="bg-gray-400 text-gray-600 cursor-not-allowed shadow-xl px-8 py-3 text-lg font-semibold duration-300"
      >
        Üye Rolü Gerekli
      </Button>
    );
  }

  return (
    <Button
      size="lg"
      onClick={handleJoinRequest}
      disabled={!user || requestSent || isLoading}
      className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-xl px-8 py-3 text-lg font-semibold duration-300 transform hover:scale-105 hover:shadow-2xl transition-all"
    >
      {!user ? (
        <>
          <UserPlus className="mr-2 h-4 w-4" />
          Önce Giriş Yap
        </>
      ) : isLoading ? (
        <>
          <LoadingSpinner size="sm" className="mr-2" />
          Gönderiliyor...
        </>
      ) : requestSent ? (
        <>
          <Check className="mr-2 h-4 w-4" />
          İstek Gönderildi
        </>
      ) : (
        <>
          <UserPlus className="mr-2 h-4 w-4" />
          Salona Katıl
        </>
      )}
    </Button>
  );
});
