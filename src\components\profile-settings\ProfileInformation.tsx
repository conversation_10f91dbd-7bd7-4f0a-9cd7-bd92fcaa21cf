'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  updateEmail,
  updateFullname,
  verifyEmailAndUpdate,
  updatePhone,
  verifyPhoneAndUpdate,
  updateProfilePhoto,
} from '@/lib/actions/user/profile-actions';
import {
  CheckCircle,
  User,
  Mail,
  Phone,
  Camera,
  AlertCircle,
  X,
  SwitchCamera,
  Shield,
} from 'lucide-react';
import { Profiles } from '@/types/database/tables';
import { User as AuthUser } from '@supabase/supabase-js';
import Image from 'next/image';

// Types
interface VerificationState {
  sent: boolean;
  pending: string;
  code: string;
}

interface FormState {
  loading: boolean;
  error?: string;
  success?: string;
}

// Constants
const VERIFICATION_CODE_LENGTH = 6;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

// Custom Hooks
const useFormState = () => {
  const [state, setState] = useState<FormState>({ loading: false });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: string | undefined) => {
    setState(prev => ({ ...prev, error, success: undefined }));
  }, []);

  const setSuccess = useCallback((success: string | undefined) => {
    setState(prev => ({ ...prev, success, error: undefined }));
  }, []);

  const reset = useCallback(() => {
    setState({ loading: false });
  }, []);

  return { state, setLoading, setError, setSuccess, reset };
};

const useVerification = () => {
  const [emailVerification, setEmailVerification] = useState<VerificationState>(
    {
      sent: false,
      pending: '',
      code: '',
    }
  );

  const [phoneVerification, setPhoneVerification] = useState<VerificationState>(
    {
      sent: false,
      pending: '',
      code: '',
    }
  );

  const resetEmailVerification = useCallback(() => {
    setEmailVerification({ sent: false, pending: '', code: '' });
  }, []);

  const resetPhoneVerification = useCallback(() => {
    setPhoneVerification({ sent: false, pending: '', code: '' });
  }, []);

  return {
    emailVerification,
    phoneVerification,
    setEmailVerification,
    setPhoneVerification,
    resetEmailVerification,
    resetPhoneVerification,
  };
};

// Utility Functions
const validateFile = (file: File): string | null => {
  if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
    return 'Sadece JPEG, PNG ve WebP formatları desteklenmektedir.';
  }
  if (file.size > MAX_FILE_SIZE) {
    return "Dosya boyutu 5MB'dan büyük olamaz.";
  }
  return null;
};

// Sub-components
const StatusMessage = ({
  error,
  success,
}: {
  error?: string;
  success?: string;
}) => {
  if (error) {
    return (
      <div className="bg-destructive/10 border-destructive/20 text-destructive flex items-center gap-2 rounded-lg border p-4 text-sm">
        <AlertCircle className="h-4 w-4 flex-shrink-0" />
        <span>{error}</span>
      </div>
    );
  }

  if (success) {
    return (
      <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
        <CheckCircle className="h-4 w-4 flex-shrink-0" />
        <span>{success}</span>
      </div>
    );
  }

  return null;
};

const VerificationCodeInput = ({
  value,
  onChange,
  onSubmit,
  onCancel,
  pendingContact,
  type = 'email',
  disabled = false,
}: {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onCancel: () => void;
  pendingContact: string;
  type?: 'email' | 'phone';
  disabled?: boolean;
}) => {
  const isValid = value.length === VERIFICATION_CODE_LENGTH;

  return (
    <div className="space-y-4">
      <div className="bg-muted/50 rounded-lg p-4">
        <p className="text-sm">
          <strong>{pendingContact}</strong>{' '}
          {type === 'email' ? 'adresine' : 'numarasına'} doğrulama kodu
          gönderildi
        </p>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">
          {type === 'email' ? 'Doğrulama Kodu' : 'SMS Doğrulama Kodu'}
        </Label>
        <Input
          type="text"
          value={value}
          onChange={e =>
            onChange(
              e.target.value
                .replace(/\D/g, '')
                .slice(0, VERIFICATION_CODE_LENGTH)
            )
          }
          placeholder={`${VERIFICATION_CODE_LENGTH} haneli ${type === 'email' ? 'doğrulama' : 'SMS'} kodunu girin`}
          className="h-11 text-center font-mono text-lg tracking-widest"
          maxLength={VERIFICATION_CODE_LENGTH}
          disabled={disabled}
        />
      </div>

      <div className="flex gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={disabled}
          className="min-w-[100px]"
        >
          <X className="mr-2 h-4 w-4" />
          İptal
        </Button>
        <Button
          type="button"
          onClick={onSubmit}
          disabled={!isValid || disabled}
          className="flex-1"
        >
          {type === 'email' ? 'E-postayı' : 'Telefonu'} Doğrula
        </Button>
      </div>
    </div>
  );
};

export const ProfileInformation = ({
  profile,
  user,
}: {
  profile: Profiles | null;
  user: AuthUser | null;
}) => {
  const { state, setLoading, setError, setSuccess, reset } = useFormState();
  const {
    emailVerification,
    phoneVerification,
    setEmailVerification,
    setPhoneVerification,
    resetEmailVerification,
    resetPhoneVerification,
  } = useVerification();

  const [photoUploading, setPhotoUploading] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);

  // Form input states for change detection
  const [emailInput, setEmailInput] = useState(profile?.email || '');
  const [phoneInput, setPhoneInput] = useState(profile?.phone_number || '');

  // Check if user signed in with Google OAuth
  const isGoogleUser = useMemo(() => {
    return user?.app_metadata?.provider === 'google' || 
           user?.identities?.some(identity => identity.provider === 'google');
  }, [user]);

  // Memoized values
  const hasProfileData = useMemo(
    () =>
      profile &&
      (profile.full_name ||
        profile.email ||
        profile.phone_number ||
        profile.avatar_url),
    [profile]
  );

  // Check if inputs have changed from original values
  const emailChanged = useMemo(
    () => emailInput.trim() !== (profile?.email || ''),
    [emailInput, profile?.email]
  );

  const phoneChanged = useMemo(
    () => phoneInput.trim() !== (profile?.phone_number || ''),
    [phoneInput, profile?.phone_number]
  );

  // Update input states when profile changes
  useEffect(() => {
    setEmailInput(profile?.email || '');
    setPhoneInput(profile?.phone_number || '');
  }, [profile?.email, profile?.phone_number]);

  // Form handlers
  const handleFullnameSubmit = useCallback(
    async (formData: FormData) => {
      setLoading(true);
      reset();

      try {
        const result = await updateFullname(formData);
        if (result.error) {
          setError(result.error);
        } else {
          setSuccess('Ad Soyad başarıyla güncellendi');
        }
      } catch (error) {
        setError('Beklenmeyen bir hata oluştu');
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setSuccess, reset]
  );

  const handleEmailSubmit = useCallback(
    async (formData: FormData) => {
      setLoading(true);
      reset();

      try {
        const email = formData.get('email') as string;
        const result = await updateEmail(formData);

        if (result.error) {
          setError(result.error);
        } else if (result.data?.requiresVerification) {
          setEmailVerification({
            sent: true,
            pending: email,
            code: '',
          });
          setSuccess(result.data.message || 'Doğrulama kodu gönderildi');
        } else {
          setSuccess('Email başarıyla güncellendi');
        }
      } catch (error) {
        setError('Beklenmeyen bir hata oluştu');
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setSuccess, reset, setEmailVerification]
  );

  const handleVerifyEmail = useCallback(async () => {
    setLoading(true);
    reset();

    try {
      const formData = new FormData();
      formData.append('email', emailVerification.pending);
      formData.append('token', emailVerification.code);

      const result = await verifyEmailAndUpdate(formData);

      if (result.error) {
        setError(result.error);
      } else {
        setSuccess(result.data?.message || 'E-posta başarıyla doğrulandı');
        resetEmailVerification();
      }
    } catch (error) {
      setError('Beklenmeyen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [
    emailVerification,
    setLoading,
    setError,
    setSuccess,
    reset,
    resetEmailVerification,
  ]);

  const handlePhoneSubmit = useCallback(
    async (formData: FormData) => {
      setLoading(true);
      reset();

      try {
        const phone = formData.get('phone') as string;
        const result = await updatePhone(formData);

        if (result.error) {
          setError(result.error);
        } else if (result.data?.requiresVerification) {
          setPhoneVerification({
            sent: true,
            pending: phone,
            code: '',
          });
          setSuccess(result.data.message || 'Doğrulama kodu gönderildi');
        } else {
          setSuccess('Telefon numarası başarıyla güncellendi');
        }
      } catch (error) {
        setError('Beklenmeyen bir hata oluştu');
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setSuccess, reset, setPhoneVerification]
  );

  const handleVerifyPhone = useCallback(async () => {
    setLoading(true);
    reset();

    try {
      const formData = new FormData();
      formData.append('phone', phoneVerification.pending);
      formData.append('token', phoneVerification.code);

      const result = await verifyPhoneAndUpdate(formData);

      if (result.error) {
        setError(result.error);
      } else {
        setSuccess(
          result.data?.message || 'Telefon numarası başarıyla doğrulandı'
        );
        resetPhoneVerification();
      }
    } catch (error) {
      setError('Beklenmeyen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [
    phoneVerification,
    setLoading,
    setError,
    setSuccess,
    reset,
    resetPhoneVerification,
  ]);

  const handlePhotoUpload = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      reset();
      setPhotoUploading(true);

      const previewUrl = URL.createObjectURL(file);
      setPhotoPreview(previewUrl);

      try {
        const formData = new FormData();
        formData.append('file', file);

        const result = await updateProfilePhoto(formData);

        if (result.error) {
          setError(result.error);
          setPhotoPreview(null);
        } else {
          setSuccess(
            result.data?.message || 'Profil fotoğrafı başarıyla güncellendi'
          );
          setTimeout(() => {
            setPhotoPreview(null);
            URL.revokeObjectURL(previewUrl);
          }, 1000);
        }
      } catch (error) {
        setError('Fotoğraf yüklenirken beklenmeyen bir hata oluştu');
        setPhotoPreview(null);
      } finally {
        setPhotoUploading(false);
        // Reset input
        e.target.value = '';
      }
    },
    [setError, setSuccess, reset]
  );

  return (
    <div className="space-y-8">
      {/* Current Profile Info Display */}
      {hasProfileData && (
        <Card className="from-primary/5 bg-gradient-to-r to-transparent">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <User className="text-primary h-5 w-5" />
              Profil Bilgileri
            </CardTitle>
            <CardDescription>
              Mevcut profil bilgileriniz ve fotoğrafınız
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start gap-6">
              {/* Profile Photo with Upload Button */}
              <div className="relative flex-shrink-0">
                <div className="relative">
                  {photoPreview ? (
                    <Image
                      src={photoPreview}
                      alt="Preview"
                      width={96}
                      height={96}
                      className="h-24 w-24 rounded-full border-4 border-white object-cover shadow-lg dark:border-gray-700"
                    />
                  ) : profile?.avatar_url ? (
                    <Image
                      src={profile.avatar_url}
                      alt="Profil fotoğrafı"
                      width={96}
                      height={96}
                      className="h-24 w-24 rounded-full border-4 border-white object-cover shadow-lg dark:border-gray-700"
                    />
                  ) : (
                    <div className="bg-muted flex h-24 w-24 items-center justify-center rounded-full border-4 border-dashed">
                      <Camera className="text-muted-foreground h-8 w-8" />
                    </div>
                  )}

                  {photoUploading && (
                    <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    </div>
                  )}
                </div>

                {/* Upload Button - Bottom Right Corner */}
                <Label
                  htmlFor="photo-upload"
                  className="absolute -bottom-1 -right-1 cursor-pointer"
                >
                  <div className="bg-primary hover:bg-primary/90 flex h-8 w-8 items-center justify-center rounded-full border-2 border-white shadow-lg transition-colors dark:border-gray-700">
                    <SwitchCamera className="h-4 w-4 text-white" />
                  </div>
                </Label>
                <input
                  id="photo-upload"
                  type="file"
                  accept={ACCEPTED_IMAGE_TYPES.join(',')}
                  onChange={handlePhotoUpload}
                  disabled={photoUploading}
                  className="hidden"
                />
              </div>

              {/* Profile Details */}
              <div className="flex-1 space-y-3">
                <div>
                  <h3 className="text-xl font-semibold">
                    {profile?.full_name || 'Ad Soyad Belirtilmemiş'}
                  </h3>
                  <div className="mt-2 space-y-2">
                    {profile?.email && (
                      <div className="text-muted-foreground flex items-center gap-2 text-sm">
                        <Mail className="h-4 w-4" />
                        <span>{profile.email}</span>
                      </div>
                    )}
                    {profile?.phone_number && (
                      <div className="text-muted-foreground flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4" />
                        <span>{profile.phone_number}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Messages */}
      <StatusMessage error={state.error} success={state.success} />

      {/* Full Name Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Ad Soyad
          </CardTitle>
          <CardDescription>Profil adınızı güncelleyin</CardDescription>
        </CardHeader>
        <CardContent>
          <form action={handleFullnameSubmit}>
            <div className="space-y-3">
              <Label htmlFor="fullName" className="text-sm font-medium">
                Ad Soyad
              </Label>
              <div className="flex gap-3">
                <Input
                  type="text"
                  name="full_name"
                  id="fullName"
                  defaultValue={profile?.full_name || ''}
                  placeholder="Ad Soyad giriniz"
                  className="h-11 flex-1"
                  disabled={state.loading}
                />
                <Button
                  type="submit"
                  disabled={state.loading}
                  className="min-w-[140px]"
                >
                  {state.loading ? 'Güncelleniyor...' : 'Güncelle'}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Email Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            E-posta Adresi
            {isGoogleUser && (
              <Shield className="h-4 w-4 text-blue-600" />
            )}
          </CardTitle>
          <CardDescription>
            {isGoogleUser 
              ? 'Google hesabınızla giriş yaptığınız için e-posta adresiniz değiştirilemez'
              : 'E-posta adresinizi güncelleyin'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isGoogleUser ? (
            <div className="space-y-3">
              <Label htmlFor="email-readonly" className="text-sm font-medium">
                E-posta Adresi
              </Label>
              <div className="flex gap-3">
                <Input
                  type="email"
                  id="email-readonly"
                  value={profile?.email || ''}
                  className="h-11 flex-1 bg-muted"
                  disabled
                  readOnly
                />
                <Button
                  type="button"
                  disabled
                  className="min-w-[160px] opacity-50"
                >
                  Değiştirilemez
                </Button>
              </div>
              <div className="flex items-start gap-2 rounded-md bg-blue-50 p-3 dark:bg-blue-950/20">
                <Shield className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200">
                    Google hesabı koruması
                  </p>
                  <p className="text-blue-700 dark:text-blue-300 mt-1">
                    Google ile giriş yaptığınız için e-posta adresiniz Google hesabınızla senkronize edilir ve değiştirilemez. 
                    E-posta adresinizi değiştirmek için Google hesap ayarlarınızı kullanın.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            !emailVerification.sent ? (
              <form action={handleEmailSubmit}>
                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-medium">
                    E-posta Adresi
                  </Label>
                  <div className="flex gap-3">
                    <Input
                      type="email"
                      name="email"
                      id="email"
                      value={emailInput}
                      onChange={e => setEmailInput(e.target.value)}
                      placeholder="E-posta adresinizi giriniz"
                      className="h-11 flex-1"
                      disabled={state.loading}
                    />
                    <Button
                      type="submit"
                      disabled={state.loading || !emailChanged}
                      className="min-w-[160px]"
                    >
                      {state.loading ? 'Gönderiliyor...' : 'Kod Gönder'}
                    </Button>
                  </div>
                  {!emailChanged && (
                    <p className="text-muted-foreground text-xs">
                      E-posta adresinizi değiştirin ve kod gönderin
                    </p>
                  )}
                </div>
              </form>
            ) : (
              <VerificationCodeInput
                value={emailVerification.code}
                onChange={code =>
                  setEmailVerification(prev => ({ ...prev, code }))
                }
                onSubmit={handleVerifyEmail}
                onCancel={resetEmailVerification}
                pendingContact={emailVerification.pending}
                type="email"
                disabled={state.loading}
              />
            )
          )}
        </CardContent>
      </Card>

      {/* Phone Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Telefon Numarası
          </CardTitle>
          <CardDescription>Telefon numaranızı güncelleyin</CardDescription>
        </CardHeader>
        <CardContent>
          {!phoneVerification.sent ? (
            <form action={handlePhoneSubmit}>
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm font-medium">
                    Telefon Numarası
                  </Label>
                  <p className="text-muted-foreground text-xs">
                    Türkiye telefon formatında giriniz (örn: 05551234567)
                  </p>
                </div>
                <div className="flex gap-3">
                  <Input
                    type="tel"
                    name="phone"
                    id="phone"
                    value={phoneInput}
                    onChange={e => setPhoneInput(e.target.value)}
                    placeholder="05551234567"
                    className="h-11 flex-1"
                    disabled={state.loading}
                  />
                  <Button
                    type="submit"
                    disabled={state.loading || !phoneChanged}
                    className="min-w-[160px]"
                  >
                    {state.loading ? 'Gönderiliyor...' : 'Kod Gönder'}
                  </Button>
                </div>
                {!phoneChanged && (
                  <p className="text-muted-foreground text-xs">
                    Telefon numaranızı değiştirin ve kod gönderin
                  </p>
                )}
              </div>
            </form>
          ) : (
            <VerificationCodeInput
              value={phoneVerification.code}
              onChange={code =>
                setPhoneVerification(prev => ({ ...prev, code }))
              }
              onSubmit={handleVerifyPhone}
              onCancel={resetPhoneVerification}
              pendingContact={phoneVerification.pending}
              type="phone"
              disabled={state.loading}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};
