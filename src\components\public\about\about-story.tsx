import { Calendar, Users, TrendingUp, Award } from "lucide-react";

export function AboutStory() {
  const timeline = [
    {
      year: "2024",
      title: "<PERSON><PERSON><PERSON><PERSON>",
      description: "Spor salonu işletmecilerinin ihtiyaçlarını karşılamak için Sportiva kuruldu.",
      icon: Calendar
    },
    {
      year: "2024 Q2",
      title: "<PERSON>lk Müşteriler",
      description: "İlk 50 spor salonu platformumuzu kullanmaya başladı.",
      icon: Users
    },
    {
      year: "2024 Q3",
      title: "Hızlı Büyüme",
      description: "500+ aktif salon ve 50K+ mutlu üye sayısına ulaştık.",
      icon: TrendingUp
    },
    {
      year: "2024 Q4",
      title: "Sekt<PERSON>r Lideri",
      description: "Türkiye'nin en güvenilir spor salonu yönetim platformu olduk.",
      icon: Award
    }
  ];

  return (
    <section id="about-content" className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Hikayemiz
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Sportiva&apos;nın kuruluşundan bugüne kadar olan yolculuğu ve gelecek vizyonumuz
            </p>
          </div>

          {/* Story Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-primary">
                Nasıl Başladık?
              </h3>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  Sportiva&apos;nın hikayesi, spor salonu işletmecilerinin günlük operasyonlarında
                  yaşadığı zorlukları çözme isteğiyle başladı. Kurucu ekibimiz, hem teknoloji
                  hem de spor endüstrisi deneyimine sahip profesyonellerden oluşuyor.
                </p>
                <p>
                  Geleneksel yöntemlerle salon yönetiminin ne kadar zor olduğunu bizzat
                  deneyimledik. Üye takibi, paket satışları, ödeme yönetimi ve raporlama
                  süreçlerinin dijitalleştirilmesi gerektiğini fark ettik.
                </p>
                <p>
                  Bu ihtiyaçtan yola çıkarak, spor salonu sahiplerinin işlerini kolaylaştıracak
                  ve üyelerin deneyimini iyileştirecek bir platform geliştirme kararı aldık.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-primary">
                Bugün Neredeyiz?
              </h3>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  Bugün, Türkiye genelinde 500&apos;den fazla spor salonu tarafından güvenilen
                  bir platform haline geldik. 50.000&apos;den fazla üyenin deneyimini iyileştirdik
                  ve salon sahiplerinin verimliliğini ortalama %40 artırdık.
                </p>
                <p>
                  Sürekli gelişen teknolojimiz ve müşteri odaklı yaklaşımımızla, spor
                  endüstrisinde dijital dönüşümün öncüsü olmaya devam ediyoruz.
                </p>
                <p>
                  Gelecekte, sadece Türkiye&apos;de değil, bölgesel bir lider olarak spor
                  teknolojileri alanında yenilikçi çözümler sunmaya devam edeceğiz.
                </p>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-primary text-center mb-12">
              Yolculuğumuz
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {timeline.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className="relative">
                    <div className="bg-background rounded-xl p-6 border border-border hover:shadow-lg transition-all duration-300 text-center">
                      <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4">
                        <IconComponent className="h-8 w-8 text-primary" />
                      </div>

                      <div className="text-lg font-bold text-primary mb-2">
                        {item.year}
                      </div>

                      <h4 className="text-base font-semibold text-foreground mb-3">
                        {item.title}
                      </h4>

                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
