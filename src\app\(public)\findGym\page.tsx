import { Metadata } from 'next';
import { GymGrid } from './GymGrid';
import { NextSearchParams } from '@/types/global/navigation';
import { searchGyms } from '@/lib/actions/all-actions';
import { FilterSidebar } from './FilterSidebar';

// Force dynamic rendering due to auth checks in header


export const metadata: Metadata = {
  title: 'Spor Salonu Bul | Sportiva',
  description:
    'Size en yakın spor salonlarını bulun. Gelişmiş filtreleme özellikleriyle aradığınız kriterlere uygun salonları keşfedin.',
  keywords: [
    'spor salonu',
    'gym',
    'fitness',
    'salon bul',
    'spor merkezi',
    'antrenman',
  ],
  openGraph: {
    title: 'Spor Salonu Bul | Sportiva',
    description:
      'Size en yakın spor salonlarını bulun. Gelişmiş filtreleme özellikleriyle aradığınız kriterlere uygun salonları keşfedin.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Spor Salonu Bul | Sportiva',
    description:
      'Size en yakın spor salonlarını bulun. Gelişmiş filtreleme özellikleriyle aradığınız kriterlere uygun salonları keşfedin.',
  },
};

// Helper function to extract and validate search parameters
function extractSearchParams(params: {
  [key: string]: string | string[] | undefined;
}) {
  const query = typeof params.q === 'string' ? params.q.slice(0, 100) : '';
  const city = typeof params.city === 'string' ? params.city : 'all';
  const district =
    typeof params.district === 'string' ? params.district : 'all';
  const features =
    typeof params.features === 'string'
      ? params.features.split(',').filter(Boolean).slice(0, 10)
      : [];
  // SSR Load More: limit parametresi (20-100 arası)
  const rawLimit = typeof params.limit === 'string' ? parseInt(params.limit, 10) : 20;
  const limit = Number.isFinite(rawLimit) ? Math.min(Math.max(rawLimit, 20), 100) : 20;

  return { query, city, district, features, limit };
}

// Main page component - Pure Server Component
export default async function FindGymPage({
  searchParams,
}: {
  searchParams: NextSearchParams;
}) {
  // Await search params in Next.js 15
  const params = await searchParams;

  // Extract search parameters
  const { query, city, district, features, limit } = extractSearchParams(params);

  // Server-side gym search with filters
  const gymsResponse = await searchGyms({
    query,
    city,
    district,
    features,
    page: 1,
    limit,
  });

  const gyms = gymsResponse.success ? gymsResponse.data?.gyms || [] : [];
  const hasActiveFilters = !!(
    query ||
    (city && city !== 'all') ||
    (district && district !== 'all') ||
    features.length > 0
  );

  return (
    <div className="bg-background relative min-h-screen">
      <FilterSidebar />
      <div className="container mx-auto py-4">
        <GymGrid
          gyms={gyms}
          hasActiveFilters={hasActiveFilters}
          searchCriteria={{ query, city, district, features }}
        />
        <div className="mt-8 flex justify-center">
          {/* SSR Load More - Heuristik: 20'nin katı kadar öğe geldiyse daha fazlası olabilir */}
          {gyms.length > 0 && gyms.length % 20 === 0 && (
            <LoadMoreLink
              currentLimit={limit}
              search={{ query, city, district, features }}
            />
          )}
        </div>
      </div>
    </div>
  );
}

// Basit SSR "Daha Fazla" Link bileşeni (aynı dosyada küçük yardımcı)
function LoadMoreLink({
  currentLimit,
  search,
}: {
  currentLimit: number;
  search: { query: string; city: string; district: string; features: string[] };
}) {
  const nextLimit = Math.min(currentLimit + 20, 100);
  const params = new URLSearchParams();
  if (search.query) params.set('q', search.query);
  if (search.city && search.city !== 'all') params.set('city', search.city);
  if (search.district && search.district !== 'all') params.set('district', search.district);
  if (search.features?.length) params.set('features', search.features.join(','));
  params.set('limit', String(nextLimit));

  // next/link’i server component içinde kullanmak mümkün; basit anchor ile SSR navigation
  return (
    <a
      href={`/findGym?${params.toString()}`}
      className="inline-flex items-center justify-center rounded-md border px-5 py-2 text-sm font-medium transition-colors hover:bg-accent"
      aria-label="Daha Fazla Sonuç Yükle"
    >
      Daha Fazla
    </a>
  );
}
