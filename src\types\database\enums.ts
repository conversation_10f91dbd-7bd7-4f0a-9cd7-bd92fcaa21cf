export type InvitationStatus = 'pending' | 'accepted' | 'rejected' | 'expired';
export type InvitationType =
  | 'join_request'
  | 'gym_invite';

export type InvitationRole =
  | 'member'
  | 'trainer';

export type GymManagerInvitationStatus = 'pending' | 'accepted' | 'expired' | 'cancelled';

// Membership related enums
export type MembershipPackageStatus =
  | 'active'
  | 'expired'
  | 'cancelled'
  | 'suspended'
  | 'pending';

// Attendance related enums
export type AttendanceStatus =
  | 'present'
  | 'absent'
  | 'late'
  | 'half_day'
  | 'sick_leave'
  | 'vacation';

// Leave related enums
export type LeaveStatus = 'pending' | 'approved' | 'rejected' | 'cancelled';

// Review related enums
export type ReviewStatus = 'draft' | 'completed' | 'approved';

// Staff related enums
export type SalaryType = 'monthly' | 'hourly' | 'commission' | 'contract';
export type StaffRoleType =
  | 'trainer'
  | 'receptionist'
  | 'cleaner'
  | 'security'
  | 'manager_assistant'
  | 'maintenance'
  | 'nutritionist'
  | 'physiotherapist'
  | 'custom';
export type StaffStatus = 'active' | 'inactive' | 'terminated' | 'on_leave';

// Appointment related enums
export type AppointmentStatus =
  | 'scheduled'
  | 'completed'
  | 'cancelled'
export type AppointmentType = 'personal' | 'group';
export type ParticipantStatus =
  | 'confirmed'
  | 'attended'
  | 'no_show'
  | 'cancelled';

// Package related enums
export type PackageType = 'appointment_standard' | 'appointment_vip' | 'daily';

// User settings enums
export type FontSize = 'small' | 'normal' | 'large';
export type ProfileVisibility = 'public' | 'private' | 'friends';

// Platform roles
export type PlatformRoles = 'member' | 'trainer' | 'company_manager' | 'gym_manager';

// Notification settings
export type NotificationCategories = {
  appointments: boolean;
  payments: boolean;
  announcements: boolean;
  reminders: boolean;
};
