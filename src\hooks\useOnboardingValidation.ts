import { VALIDATION_RULES } from '@/lib/actions/shared/constants';
import { PlatformRoles } from '@/types/database/enums';
import type { PersonalData, MemberData, TrainerData, ManagerData } from '@/types/onboarding';

/**
 * Onboarding validation hook
 * Following Clean Code principles - single responsibility for validation logic
 */


export function useOnboardingValidation(
  selectedRoles: PlatformRoles[],
  hasFullName: boolean
) {
  /**
   * Validates personal data with early returns
   * Clean validation following KISS principle
   */
  const validatePersonalData = (data: PersonalData): string | null => {
    // Early return if user already has full name
    if (hasFullName) return null;

    const firstName = data.first_name.trim();
    const lastName = data.last_name.trim();

    if (!firstName) return 'Ad alanı zorunludur.';
    if (!lastName) return 'Soyad alanı zorunludur.';
    if (firstName.length < VALIDATION_RULES.FULL_NAME_MIN_LENGTH) {
      return `Ad en az ${VALIDATION_RULES.FULL_NAME_MIN_LENGTH} karakter olmalıdır.`;
    }
    if (lastName.length < VALIDATION_RULES.FULL_NAME_MIN_LENGTH) {
      return `Soyad en az ${VALIDATION_RULES.FULL_NAME_MIN_LENGTH} karakter olmalıdır.`;
    }

    return null;
  };

  /**
   * Validates member data with meaningful constants
   * Single responsibility: only validates member-specific data
   */
  const validateMemberData = (data: MemberData): string | null => {
    if (!selectedRoles.includes('member')) return null;

    if (!data.age || !data.gender) {
      return 'Yaş ve cinsiyet alanları zorunludur.';
    }

    const age = parseInt(data.age);
    if (
      isNaN(age) ||
      age < VALIDATION_RULES.ONBOARDING_AGE_MIN ||
      age > VALIDATION_RULES.AGE_MAX
    ) {
      return `Yaş ${VALIDATION_RULES.ONBOARDING_AGE_MIN}-${VALIDATION_RULES.AGE_MAX} arasında olmalıdır.`;
    }

    if (data.height_cm) {
      const height = parseInt(data.height_cm);
      if (
        isNaN(height) ||
        height < VALIDATION_RULES.HEIGHT_MIN ||
        height > VALIDATION_RULES.HEIGHT_MAX
      ) {
        return `Boy ${VALIDATION_RULES.HEIGHT_MIN}-${VALIDATION_RULES.HEIGHT_MAX} cm arasında olmalıdır.`;
      }
    }

    if (data.weight_kg) {
      const weight = parseInt(data.weight_kg);
      if (
        isNaN(weight) ||
        weight < VALIDATION_RULES.WEIGHT_MIN ||
        weight > VALIDATION_RULES.WEIGHT_MAX
      ) {
        return `Kilo ${VALIDATION_RULES.WEIGHT_MIN}-${VALIDATION_RULES.WEIGHT_MAX} kg arasında olmalıdır.`;
      }
    }

    return null;
  };

  /**
   * Validates trainer data with constants and early returns
   * Clean validation following single responsibility principle
   */
  const validateTrainerData = (data: TrainerData): string | null => {
    if (!selectedRoles.includes('trainer')) return null;

    if (!data.specialization || !data.certification_level) {
      return 'Uzmanlık alanı ve sertifika seviyesi zorunludur.';
    }

    if (data.experience_years) {
      const experience = parseInt(data.experience_years);
      if (
        isNaN(experience) ||
        experience < 0 ||
        experience > VALIDATION_RULES.EXPERIENCE_MAX
      ) {
        return `Deneyim yılı 0-${VALIDATION_RULES.EXPERIENCE_MAX} arasında olmalıdır.`;
      }
    }

    if (data.bio && data.bio.length > VALIDATION_RULES.BIO_MAX_LENGTH) {
      return `Biyografi en fazla ${VALIDATION_RULES.BIO_MAX_LENGTH} karakter olabilir.`;
    }

    return null;
  };

  /**
   * Validates manager data with early returns
   * Single responsibility: only validates manager-specific data
   */
  const validateManagerData = (data: ManagerData): string | null => {
    if (!selectedRoles.includes('company_manager')) return null;

    if (!data.companyName.trim()) {
      return 'Şirket adı zorunludur.';
    }

    if (data.companyName.trim().length < 2) {
      return 'Şirket adı en az 2 karakter olmalıdır.';
    }

    if (data.companyEmail && data.companyEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.companyEmail.trim())) {
        return 'Geçerli bir e-posta adresi giriniz.';
      }
    }

    return null;
  };

  /**
   * Validates all forms and returns first error found
   */
  const validateAllForms = (
    personalData: PersonalData,
    memberData: MemberData,
    trainerData: TrainerData,
    managerData: ManagerData
  ): string | null => {
    const personalError = validatePersonalData(personalData);
    const memberError = validateMemberData(memberData);
    const trainerError = validateTrainerData(trainerData);
    const managerError = validateManagerData(managerData);

    return personalError || memberError || trainerError || managerError;
  };

  /**
   * Checks if all forms are complete and valid
   */
  const isFormComplete = (
    personalData: PersonalData,
    memberData: MemberData,
    trainerData: TrainerData,
    managerData: ManagerData
  ): boolean => {
    const personalError = validatePersonalData(personalData);
    const memberError = validateMemberData(memberData);
    const trainerError = validateTrainerData(trainerData);
    const managerError = validateManagerData(managerData);

    return !personalError && !memberError && !trainerError && !managerError;
  };

  return {
    validatePersonalData,
    validateMemberData,
    validateTrainerData,
    validateManagerData,
    validateAllForms,
    isFormComplete,
  };
}
