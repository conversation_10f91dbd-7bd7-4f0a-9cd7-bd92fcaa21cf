'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

function readFlashCookie(): { type?: 'success' | 'error' | 'info'; message: string } | null {
  try {
    const name = 'flash_toast=';
    const decodedCookie = decodeURIComponent(document.cookie || '');
    const ca = decodedCookie.split(';');
    for (let c of ca) {
      c = c.trim();
      if (c.startsWith(name)) {
        const value = c.substring(name.length, c.length);
        return JSON.parse(value);
      }
    }
    return null;
  } catch {
    return null;
  }
}

function clearFlashCookie() {
  // Cookie'yi geçmiş bir tarihe ayarlayarak temizle
  document.cookie = 'flash_toast=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
}

export function GlobalToastHandler() {
  const router = useRouter();

  useEffect(() => {
    // 1) URL parametrelerinden (message, toast) göster
    const url = new URL(window.location.href);
    const message = url.searchParams.get('message');
    const toastType = url.searchParams.get('toast'); // optional: success | error | info

    if (message) {
      if (toastType === 'error') toast.error(message);
      else if (toastType === 'success') toast.success(message);
      else if (toastType === 'info') toast.info(message);
      else toast(message);

      url.searchParams.delete('message');
      url.searchParams.delete('toast');
      // Toast'ın ekranda görünmesini garanti etmek için biraz daha uzun bir gecikme ile URL temizle
      setTimeout(() => {
        router.replace(url.pathname + url.search, { scroll: false });
      }, 400);
      return; // URL parametresiyle gösterildiyse cookie yolunu çalıştırma
    }

    // 2) Flash cookie'den göster (server-actions → cookie)
    const flash = readFlashCookie();
    if (flash?.message) {
      if (flash.type === 'error') toast.error(flash.message);
      else if (flash.type === 'success') toast.success(flash.message);
      else if (flash.type === 'info') toast.info(flash.message);
      else toast(flash.message);

      clearFlashCookie();
    }
  }, [router]);

  return null;
}
