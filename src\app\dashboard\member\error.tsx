'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';
import { RefreshCw } from 'lucide-react';
import { AuthErrorInline } from '@/components/auth/auth-error-handler';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    logger.error('Member dashboard error', error, {
      digest: error.digest,
      component: 'MemberDashboard',
    });
  }, [error]);

  return (
    <div className="space-y-8">
      <section>
        <h1 className="text-3xl font-bold tracking-tight">Hoş Geldiniz</h1>
        <p className="text-muted-foreground mt-1">
          Üyeliklerinizi ve fitness yolculuğunuzu takip edin
        </p>
      </section>

      <section>
        <AuthErrorInline error="server_error" className="mb-4" />
        <div className="flex justify-center">
          <Button variant="outline" size="sm" onClick={reset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Tekrar Dene
          </Button>
        </div>
      </section>
    </div>
  );
}
