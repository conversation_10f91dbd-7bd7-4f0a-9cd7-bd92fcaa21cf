'use client';

import {
  <PERSON><PERSON>,
  Check<PERSON><PERSON>cle,
  AlertTriangle,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { EquipmentAnalytics } from '@/types/database/equipment-inventory';

interface EquipmentStatsCardsProps {
  analytics: EquipmentAnalytics;
}

export function EquipmentStatsCards({ analytics }: EquipmentStatsCardsProps) {

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Equipment */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Ekipman</CardTitle>
          <Wrench className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{analytics.total_equipment}</div>
          <div className="flex gap-2 mt-2">
            <Badge variant="secondary" className="text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              {analytics.active_equipment} Aktif
            </Badge>
            {analytics.maintenance_equipment > 0 && (
              <Badge variant="outline" className="text-xs text-yellow-600">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {analytics.maintenance_equipment} Bakımda
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
