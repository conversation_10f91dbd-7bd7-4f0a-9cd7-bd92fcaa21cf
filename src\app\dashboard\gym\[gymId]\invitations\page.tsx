import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Mail,
  Clock,
  CheckCircle,
  Send,
  Users,
  TrendingUp,
  Calendar,
  MessageSquare,
  X,
} from 'lucide-react';

import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';

import { cn } from '@/lib/utils';
import AcceptRejectButtons from './components/accept-reject-buttons';
import { getGymIncomingRequests, getGymOutgoingInvites } from '@/lib/actions/gym_invitations/invitation-actions';


interface InvitationsPageProps {
  params: Promise<{ gymId: string }>;
}

export default async function InvitationsPage({
  params,
}: InvitationsPageProps) {
  const { gymId } = await params;

  const incoming = await getGymIncomingRequests(gymId);
  const outgoing = await getGymOutgoingInvites(gymId);

  if (!incoming.success || !outgoing.success) {
    throw new Error('Davetler yüklenemedi');
  }

  const incomingData = incoming.data || [];
  const outgoingData = outgoing.data || [];


  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: {
        icon: Clock,
        label: 'Bekliyor',
        className:
          'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
        iconClass: 'text-yellow-600 dark:text-yellow-400',
      },
      accepted: {
        icon: CheckCircle,
        label: 'Kabul Edildi',
        className:
          'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
        iconClass: 'text-green-600 dark:text-green-400',
      },
      rejected: {
        icon: X,
        label: 'Reddedildi',
        className:
          'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
        iconClass: 'text-red-600 dark:text-red-400',
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return <Badge variant="outline">{status}</Badge>;

    const Icon = config.icon;
    return (
      <Badge
        className={cn(
          'inline-flex items-center gap-1.5 rounded-full border px-2.5 py-1 text-xs font-medium',
          config.className
        )}
      >
        <Icon className={cn('h-3.5 w-3.5', config.iconClass)} />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: tr,
    });
  };



  const totalInvitations = incomingData.length + outgoingData.length;
  const pendingInvitations = [...incomingData, ...outgoingData].filter(
    inv => inv.status === 'pending'
  ).length;
  const acceptedInvitations = [...incomingData, ...outgoingData].filter(
    inv => inv.status === 'accepted'
  ).length;

  return (
    <div className="mx-auto max-w-7xl space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden rounded-2xl border border-gray-200/50 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8 dark:border-gray-800/50 dark:from-gray-900/50 dark:via-gray-800/50 dark:to-gray-900/50">
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-3xl font-bold text-transparent dark:from-white dark:to-gray-300">
                Davet Yönetimi
              </h1>
              <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
                Salon davetlerini yönetin ve takip edin
              </p>
            </div>
            <div className="hidden sm:block">
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <Calendar className="h-4 w-4" />
                <span>
                  {new Date().toLocaleDateString('tr-TR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-100/20 via-purple-100/20 to-pink-100/20 dark:from-blue-900/10 dark:via-purple-900/10 dark:to-pink-900/10" />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="group relative overflow-hidden border-0 transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 opacity-5 transition-opacity group-hover:opacity-10" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Toplam Davet
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {totalInvitations}
                </p>
              </div>
              <div className="rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-3 text-white shadow-lg">
                <Mail className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-0 transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500 to-orange-500 opacity-5 transition-opacity group-hover:opacity-10" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Bekleyen
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {pendingInvitations}
                </p>
              </div>
              <div className="rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500 p-3 text-white shadow-lg">
                <Clock className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-0 transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-emerald-500 opacity-5 transition-opacity group-hover:opacity-10" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Kabul Edilen
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {acceptedInvitations}
                </p>
              </div>
              <div className="rounded-xl bg-gradient-to-br from-green-500 to-emerald-500 p-3 text-white shadow-lg">
                <CheckCircle className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-0 transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-500 opacity-5 transition-opacity group-hover:opacity-10" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Aktivite Oranı
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {totalInvitations > 0
                    ? Math.round((acceptedInvitations / totalInvitations) * 100)
                    : 0}
                  %
                </p>
              </div>
              <div className="rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 p-3 text-white shadow-lg">
                <TrendingUp className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="incoming" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:max-w-md">
          <TabsTrigger value="incoming">Gelen İstekler</TabsTrigger>
          <TabsTrigger value="outgoing">Giden Davetler</TabsTrigger>
        </TabsList>

        <TabsContent value="incoming" className="space-y-6">
          {/* Gelen Davetler (Üye + Antrenör) */}
          <Card className="overflow-hidden border-0 shadow-lg">
            <CardHeader className="border-b border-gray-100 dark:border-gray-800">
              <CardTitle className="flex items-center gap-3">
                <div className="rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-2.5 text-white shadow-lg">
                  <Users className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Gelen Davetler</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Üyelerin ve antrenörlerin salonunuza gönderdiği talepler
                  </p>
                </div>
                <Badge variant="secondary" className="ml-auto text-lg">
                  {incomingData.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {incomingData.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="mb-4 rounded-full bg-gray-100 p-4 dark:bg-gray-800">
                    <Users className="h-12 w-12 text-gray-400" />
                  </div>
                  <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                    Henüz gelen davet yok
                  </h4>
                  <p className="max-w-sm text-gray-500 dark:text-gray-400">
                    Üyelerden ve antrenörlerden gelen talepler burada görünecek
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100 dark:divide-gray-800">
                  {incomingData.map((invitation: any) => (
                    <div
                      key={invitation.id}
                      className="p-6 transition-all hover:bg-gray-50/50 dark:hover:bg-gray-800/50"
                    >
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-600 font-semibold text-white">
                            {invitation.profile?.full_name
                              ?.split(' ')
                              .map((n: string) => n[0])
                              .join('')
                              .toUpperCase() || 'U'}
                          </div>
                          <div className="absolute -right-1 -bottom-1 h-4 w-4 rounded-full border-2 border-white bg-green-500 dark:border-gray-900" />
                        </div>
                        <div className="flex-1">
                          <div className="mb-2 flex items-start justify-between">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {invitation.profile?.full_name}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {invitation.profile?.email}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(invitation.status)}
                              {/* Tür rozeti */}
                              <Badge variant="outline" className="text-xs">
                                {invitation.role === 'member'
                                  ? 'Üye'
                                  : 'Antrenör'}
                              </Badge>
                            </div>
                          </div>
                          {invitation.message && (
                            <div className="mb-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50">
                              <div className="flex items-start gap-2">
                                <MessageSquare className="mt-0.5 h-4 w-4 text-gray-400" />
                                <p className="text-sm text-gray-600 italic dark:text-gray-300">
                                  &quot;{invitation.message}&quot;
                                </p>
                              </div>
                            </div>
                          )}
                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1.5 text-gray-500">
                              <Clock className="h-4 w-4" />
                              <span>{formatDate(invitation.created_at)}</span>
                            </div>
                            <div className="flex items-center gap-1.5 text-gray-500">
                              <Calendar className="h-4 w-4" />
                              <span>
                                {new Date(
                                  invitation.created_at
                                ).toLocaleDateString('tr-TR')}
                              </span>
                            </div>
                          </div>
                          <div className="mt-4">
                            <AcceptRejectButtons
                              invitationId={invitation.id}
                              status={invitation.status}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="outgoing" className="space-y-6">
          {/* Giden Davetler (Üye + Antrenör) */}
          <Card className="overflow-hidden border-0 shadow-lg">
            <CardHeader className="border-b border-gray-100 dark:border-gray-800">
              <CardTitle className="flex items-center gap-3">
                <div className="rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 p-2.5 text-white shadow-lg">
                  <Send className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Giden Davetler</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Üye ve antrenörlere gönderdiğiniz davetler
                  </p>
                </div>
                <Badge variant="secondary" className="ml-auto text-lg">
                  {outgoingData.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {outgoingData.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="mb-4 rounded-full bg-gray-100 p-4 dark:bg-gray-800">
                    <Send className="h-12 w-12 text-gray-400" />
                  </div>
                  <h4 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                    Henüz davet göndermediniz
                  </h4>
                  <p className="max-w-sm text-gray-500 dark:text-gray-400">
                    Gönderdiğiniz davetler burada görünecek
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100 dark:divide-gray-800">
                  {outgoingData.map((invitation: any) => (
                    <div
                      key={invitation.id}
                      className="p-6 transition-all hover:bg-gray-50/50 dark:hover:bg-gray-800/50"
                    >
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-indigo-500 to-indigo-600 font-semibold text-white">
                            {invitation.profile?.full_name
                              ?.split(' ')
                              .map((n: string) => n[0])
                              .join('')
                              .toUpperCase() || 'U'}
                          </div>
                          <div className="absolute -right-1 -bottom-1 h-4 w-4 rounded-full border-2 border-white bg-indigo-500 dark:border-gray-900" />
                        </div>
                        <div className="flex-1">
                          <div className="mb-2 flex items-start justify-between">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                                {invitation.profile?.full_name}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {invitation.profile?.email}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(invitation.status)}
                              {/* Tür rozeti */}
                              <Badge variant="outline" className="text-xs">
                                {invitation.role === 'member'
                                  ? 'Üye'
                                  : 'Antrenör'}
                              </Badge>
                            </div>
                          </div>
                          {invitation.message && (
                            <div className="mb-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50">
                              <div className="flex items-start gap-2">
                                <MessageSquare className="mt-0.5 h-4 w-4 text-gray-400" />
                                <p className="text-sm text-gray-600 italic dark:text-gray-300">
                                  &quot;{invitation.message}&quot;
                                </p>
                              </div>
                            </div>
                          )}
                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1.5 text-gray-500">
                              <Clock className="h-4 w-4" />
                              <span>{formatDate(invitation.created_at)}</span>
                            </div>
                            <div className="flex items-center gap-1.5 text-gray-500">
                              <Calendar className="h-4 w-4" />
                              <span>
                                {new Date(
                                  invitation.created_at
                                ).toLocaleDateString('tr-TR')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
