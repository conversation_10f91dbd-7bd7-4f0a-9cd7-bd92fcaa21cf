import { Suspense } from 'react';

import { GymSettingsForm } from './gym-settings-form';
import { Settings } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { PhotoUpload } from '@/components/shared/PhotoUpload';
import { Gyms } from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import { 
  getGymById,
  updateGymSettings,
} from '@/lib/actions/dashboard/company/gym-actions';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';
import { UnauthorizedAccess } from '@/components/dashboard/common/UnauthorizedAccess';

interface GymSettingsPageProps {
  params: Promise<{ gymId: string }>;
}

// Wrapper action for useActionState compatibility
async function updateGymSettingsAction(
  _prevState: ApiResponse<Gyms>,
  formData: FormData
): Promise<ApiResponse<Gyms>> {
  'use server';
  return await updateGymSettings(formData);
}

async function GymSettingsContent({ gymId }: { gymId: string }) {
  const { data: gym, error } = await getGymById(gymId);

  if (error || !gym) {
    throw new Error('Salon bulunamadı');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mx-auto max-w-4xl space-y-6">
        {/* Header */}
        <div>
          <h1 className="flex items-center gap-2 text-3xl font-bold tracking-tight">
            <Settings className="h-8 w-8" />
            Salon Ayarları
          </h1>
          <p className="text-muted-foreground">
            {gym.name} salonunun ayarlarını yönetin
          </p>
        </div>

        <Separator />

        {/* Photo Upload Section */}
        <PhotoUpload
          title="Salon Kapak Görseli"
          description="Salonunuzun kapak görselini güncelleyin (Logo şirket ayarlarından gelir)"
          currentCoverUrl={gym.cover_image_url}
          gymId={gym.id}
          showCurrentImages={true}
        />

        {/* Settings Form */}
        <GymSettingsForm gym={gym} updateAction={updateGymSettingsAction} />
      </div>
    </div>
  );
}

function LoadingState() {
  return (
    <div className="container mx-auto py-8">
      <div className="mx-auto max-w-4xl">
        <div className="animate-pulse space-y-6">
          <div className="h-8 w-1/3 rounded bg-gray-200"></div>
          <div className="space-y-4">
            <div className="h-4 w-full rounded bg-gray-200"></div>
            <div className="h-4 w-3/4 rounded bg-gray-200"></div>
            <div className="h-4 w-1/2 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default async function GymSettingsPage({
  params,
}: GymSettingsPageProps) {
 const { gymId } = await params;

 // Dashboard context üzerinden yetki kontrolü
 const ctx = await getDashboardContext(gymId);
 if (!ctx.gymAccess?.hasAccess) {
   return (
     <UnauthorizedAccess reason="Bu salona erişim yetkiniz bulunmuyor." />
   );
 }
 if (ctx.gymAccess?.restrictedPages?.includes('settings')) {
   return (
     <UnauthorizedAccess reason="Bu salonun ayarlarını düzenleme yetkiniz bulunmuyor." />
   );
 }



  return (
    <Suspense fallback={<LoadingState />}>
      <GymSettingsContent gymId={gymId} />
    </Suspense>
  );
}
