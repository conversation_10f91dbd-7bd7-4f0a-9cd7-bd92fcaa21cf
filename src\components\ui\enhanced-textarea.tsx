'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { ValidationState, getValidationState } from '@/lib/utils/form-validation';

export interface EnhancedTextareaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  validator?: (value: string) => boolean;
  formatter?: (value: string) => string;
  validationMessage?: string;
  required?: boolean;
  maxLength?: number;
  characterCount?: boolean;
}

const EnhancedTextarea = React.forwardRef<HTMLTextAreaElement, EnhancedTextareaProps>(
  (
    {
      className,
      value,
      onChange,
      validator,
      formatter,
      validationMessage,
      required = false,
      maxLength,
      characterCount = true,
      ...props
    },
    ref
  ) => {
    const [validationState, setValidationState] = React.useState<ValidationState>('idle');
    const [displayValue, setDisplayValue] = React.useState(value);

    // Update validation state when value changes
    React.useEffect(() => {
      if (validator) {
        const state = getValidationState(value, validator, required);
        setValidationState(state);
      }
    }, [value, validator, required]);

    // Handle input change with formatting
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      let newValue = e.target.value;

      // Apply formatter if provided
      if (formatter) {
        newValue = formatter(newValue);
      }

      // Apply maxLength if provided
      if (maxLength && newValue.length > maxLength) {
        newValue = newValue.slice(0, maxLength);
      }

      setDisplayValue(newValue);
      onChange(newValue);
    };

    // Sync display value with prop value
    React.useEffect(() => {
      setDisplayValue(value);
    }, [value]);

    const getTextareaClassName = () => {
      switch (validationState) {
        case 'valid':
          return 'border-green-500 focus:border-green-500 focus:ring-green-500';
        case 'invalid':
          return 'border-red-500 focus:border-red-500 focus:ring-red-500';
        case 'warning':
          return 'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500';
        default:
          return '';
      }
    };

    return (
      <div className="relative">
        <Textarea
          className={cn(getTextareaClassName(), className)}
          ref={ref}
          value={displayValue}
          onChange={handleChange}
          {...props}
        />

        {/* Character Count */}
        {characterCount && maxLength && (
          <div className="mt-1 text-xs text-gray-500 text-right">
            {displayValue.length}/{maxLength}
          </div>
        )}

        {/* Validation Message */}
        {validationMessage && validationState === 'invalid' && (
          <div className="mt-1 text-xs text-red-600 dark:text-red-400">
            {validationMessage}
          </div>
        )}
      </div>
    );
  }
);

EnhancedTextarea.displayName = 'EnhancedTextarea';

export { EnhancedTextarea };
