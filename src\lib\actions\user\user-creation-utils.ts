/**
 * Utility functions for user creation operations
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import { getSupabaseAdmin } from '@/lib/supabase/admin';
import {
  generateSportivaEmail,
  generateMemorablePassword,
} from '@/lib/utils/guest-utils';
import {
  PASSWORD_CONFIG,
  TRAINER_CODE_CONFIG,
  MEMBER_CODE_CONFIG,
  ERROR_MESSAGES,
  DATABASE_DEFAULTS,
} from './constants';
import { createClient } from '@/lib/supabase/server';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface CreateAuthUserParams {
  email: string | null;
  password: string;
  fullName: string;
  isGuest?: boolean;
  phoneNumber?: string | null;
}

export interface CreateUserProfileParams {
  userId: string;
  email: string | null;
  fullName: string;
  isGuest?: boolean;
  avatarUrl?: string | null;
  phoneNumber?: string | null;
}

export interface MemberDetailsData {
  age: number;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  height_cm?: number;
  weight_kg?: number;
  fitness_goal?: string;
}

export interface RollbackOptions {
  deleteProfile?: boolean;
  deleteMemberDetails?: boolean;
  context?: string;
}

// ============================================================================
// PASSWORD GENERATION
// ============================================================================

/**
 * Generate memorable password for users
 * Uses age if provided, otherwise generates random suffix
 */
export async function generateUserPassword(
  fullName: string,
  age?: number
): Promise<string> {
  const suffix =
    age ||
    Math.floor(
      PASSWORD_CONFIG.MIN_RANDOM_SUFFIX +
      Math.random() *
      (PASSWORD_CONFIG.MAX_RANDOM_SUFFIX -
        PASSWORD_CONFIG.MIN_RANDOM_SUFFIX +
        1)
    );

  return generateMemorablePassword(fullName, suffix);
}

// ============================================================================
// CODE GENERATION
// ============================================================================
/**
 * Generate unique trainer code
 * Format: TR + timestamp(8 digits) + random(5 digits)
 */
export async function generateTrainerCode(): Promise<string> {
  const timestamp = Date.now()
    .toString()
    .slice(-TRAINER_CODE_CONFIG.TIMESTAMP_SLICE_LENGTH);

  const random = Math.floor(Math.random() * TRAINER_CODE_CONFIG.RANDOM_MAX)
    .toString()
    .padStart(TRAINER_CODE_CONFIG.RANDOM_PADDING_LENGTH, '0');

  return TRAINER_CODE_CONFIG.PREFIX + timestamp + random;
}

/**
 * Generate unique member code
 * Format: MB + timestamp(8 digits) + random(5 digits)
 */
export async function generateMemberCode(): Promise<string> {
  const timestamp = Date.now()
    .toString()
    .slice(-MEMBER_CODE_CONFIG.TIMESTAMP_SLICE_LENGTH);

  const random = Math.floor(Math.random() * MEMBER_CODE_CONFIG.RANDOM_MAX)
    .toString()
    .padStart(MEMBER_CODE_CONFIG.RANDOM_PADDING_LENGTH, '0');

  return MEMBER_CODE_CONFIG.PREFIX + timestamp + random;
}


// ============================================================================
// EMAIL GENERATION
// ============================================================================

/**
 * Generate guest email for guest members
 */
export function generateGuestEmail(fullName: string, age: number): string {
  return generateSportivaEmail(fullName, age, 'guest');
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validate user creation data
 */
export function validateUserCreationData(userData: {
  email?: string | null;
  phone_number?: string | null;
}): { hasEmail: boolean; hasPhone: boolean; isValid: boolean } {
  const hasEmail = userData.email && userData.email.trim() !== '';
  const hasPhone = userData.phone_number && userData.phone_number.trim() !== '';

  return {
    hasEmail: Boolean(hasEmail),
    hasPhone: Boolean(hasPhone),
    isValid: Boolean(hasEmail || hasPhone),
  };
}

// ============================================================================
// ROLLBACK OPERATIONS
// ============================================================================

/**
 * Rollback helper for user creation operations
 * Handles cleanup when user creation fails at any step
 */
export async function rollbackUserCreation(
  adminClient: ReturnType<typeof getSupabaseAdmin>,
  userId: string,
  options: RollbackOptions = {}
): Promise<void> {
  const {
    deleteProfile = false,
    deleteMemberDetails = false,
    context = ERROR_MESSAGES.ROLLBACK_USER_CREATION,
  } = options;

  try {
    // Always delete auth user first
    await adminClient.auth.admin.deleteUser(userId);

    if (deleteProfile) {
      await adminClient.from('profiles').delete().eq('id', userId);
    }

    if (deleteMemberDetails) {
      await adminClient
        .from('member_details')
        .delete()
        .eq('profile_id', userId);
    }
  } catch (rollbackError) {
    console.error(`Rollback error during ${context}:`, rollbackError);
    // Log rollback failure but don't throw - we want the original error to be thrown
    console.warn(`${ERROR_MESSAGES.ROLLBACK_NOTIFICATION} - ${context}`);
  }
}

// ============================================================================
// AUTH USER CREATION
// ============================================================================

/**
 * Create authentication user with email or phone
 * Handles both email-based and phone-based user creation
 */
export async function createAuthUser(
  adminClient: ReturnType<typeof getSupabaseAdmin>,
  params: CreateAuthUserParams
): Promise<{ user: { id: string } }> {
  const { email, password, fullName, isGuest = false, phoneNumber } = params;

  const createUserData: any = {
    password,
    email_confirm: true, // Skip email verification for manager/trainer-created accounts
    user_metadata: {
      full_name: fullName,
      is_guest_account: isGuest,
    },
  };

  if (email) {
    createUserData.email = email;
  } else if (phoneNumber) {
    createUserData.phone = phoneNumber;
    createUserData.phone_confirm = true; // Phone was already verified
  } else {
    throw new Error(ERROR_MESSAGES.EMAIL_OR_PHONE_REQUIRED);
  }

  const { data: authData, error: authError } =
    await adminClient.auth.admin.createUser(createUserData);

  if (authError || !authData.user) {
    throw new Error(
      `${ERROR_MESSAGES.USER_CREATION_FAILED}: ${authError?.message}`
    );
  }

  return authData;
}

// ============================================================================
// PROFILE CREATION
// ============================================================================

/**
 * Centralized profile creation utility
 * Handles profile creation for all authentication flows
 */
export async function createUserProfile(
  params: {
    userId?: string;
    email?: string | null;
    phoneNumber?: string | null;
    fullName?: string | null;
    avatarUrl?: string | null;
    isGuest?: boolean;
  } = {}
): Promise<{
  success: boolean;
  profileId: string;
  isNewProfile: boolean;
  error?: string;
}> {
  try {
    // Determine user ID - either provided or from authenticated user
    let userId = params.userId;
    let userData = null;

    if (!userId) {

      const supabase = await createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return {
          success: false,
          profileId: '',
          isNewProfile: false,
          error: 'No authenticated user found',
        };
      }

      userId = user.id;
      userData = user;
    }

    if (!userId) {
      return {
        success: false,
        profileId: '',
        isNewProfile: false,
        error: 'User ID is required',
      };
    }

    const admin = getSupabaseAdmin();

    // Check if profile already exists
    const { data: existingProfile } = await admin
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (existingProfile) {
      return {
        success: true,
        profileId: userId,
        isNewProfile: false,
      };
    }

    // Prepare profile data
    let email = params.email;
    let phoneNumber = params.phoneNumber;
    let fullName = params.fullName;
    let avatarUrl = params.avatarUrl;

    // If we have userData from auth, use it to fill missing fields
    if (userData) {
      email = email ?? userData.email;
      phoneNumber = phoneNumber ?? userData.phone;

      // Extract name from user metadata
      const userMetadata = userData.user_metadata || {};
      fullName = fullName ??
        (userMetadata.full_name ||
          userMetadata.name ||
          (email ? email.split('@')[0] : 'Kullanıcı'));

      avatarUrl = avatarUrl ??
        (userMetadata.avatar_url ||
          userMetadata.picture ||
          null);
    }

    // Create profile
    const { error: profileError } = await admin.from('profiles').insert({
      id: userId,
      email: email || DATABASE_DEFAULTS.NULL_VALUE,
      full_name: fullName || 'Kullanıcı',
      phone_number: phoneNumber || DATABASE_DEFAULTS.NULL_VALUE,
      avatar_url: avatarUrl || DATABASE_DEFAULTS.NULL_VALUE,
      is_guest_account: params.isGuest ?? false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (profileError) {
      console.error('Profile creation error:', profileError);
      return {
        success: false,
        profileId: userId,
        isNewProfile: false,
        error: profileError.message,
      };
    }

    return {
      success: true,
      profileId: userId,
      isNewProfile: true,
    };

  } catch (error) {
    console.error('Unexpected error in profile creation:', error);
    return {
      success: false,
      profileId: '',
      isNewProfile: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Check if profile exists for a user
 */
export async function checkProfileExists(userId: string): Promise<boolean> {
  try {
    const admin = getSupabaseAdmin();
    const { data } = await admin
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    return !!data;
  } catch {
    return false;
  }
}

/**
 * Get or create profile for authenticated user
 */
export async function getOrCreateProfile(params: Parameters<typeof createUserProfile>[0] = {}) {
  return createUserProfile(params);
}

// ============================================================================
// MEMBER DETAILS CREATION
// ============================================================================

/**
 * Create member details record
 */
export async function createMemberDetailsRecord(
  adminClient: ReturnType<typeof getSupabaseAdmin>,
  userId: string,
  memberData: MemberDetailsData
): Promise<void> {
  // Generate unique member code
  const inviteCode = await generateMemberCode();

  const { error: memberDetailsError } = await adminClient
    .from('member_details')
    .insert({
      profile_id: userId,
      age: memberData.age,
      gender: memberData.gender,
      height_cm: memberData.height_cm || DATABASE_DEFAULTS.NULL_VALUE,
      weight_kg: memberData.weight_kg || DATABASE_DEFAULTS.NULL_VALUE,
      fitness_goal: memberData.fitness_goal || DATABASE_DEFAULTS.NULL_VALUE,
      invite_code: inviteCode,
    });

  if (memberDetailsError) {
    throw new Error(
      `${ERROR_MESSAGES.MEMBER_DETAILS_CREATION_FAILED}: ${memberDetailsError.message}`
    );
  }
}
