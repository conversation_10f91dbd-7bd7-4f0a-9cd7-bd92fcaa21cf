/**
 * Utility functions for gym page operations
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import { 
  RATING_VALIDATION, 
  REVIEW_VALIDATION, 
  GYM_ERROR_MESSAGES 
} from './gym-constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface ReviewValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ReviewData {
  gym_id: string;
  rating: number;
  comment?: string;
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
}

export interface ReviewWithUser {
  id: string;
  rating: number | null;
  comment: string | null;
  created_at: string | null;
  updated_at: string | null;
  profile_id: string;
  gym_id: string;
  user: {
    full_name: string;
  } | null;
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================



/**
 * Validate rating value
 */
export function validateRating(rating: number): ReviewValidationResult {
  if (!rating) {
    return { isValid: false, error: GYM_ERROR_MESSAGES.RATING_REQUIRED };
  }
  
  if (rating < RATING_VALIDATION.MIN_RATING || rating > RATING_VALIDATION.MAX_RATING) {
    return { isValid: false, error: GYM_ERROR_MESSAGES.INVALID_RATING };
  }
  
  return { isValid: true };
}

/**
 * Validate review comment
 */
export function validateComment(comment?: string): ReviewValidationResult {
  if (!comment) {
    return { isValid: true }; // Comment is optional
  }
  
  const trimmedComment = comment.trim();
  
  if (trimmedComment.length < REVIEW_VALIDATION.MIN_COMMENT_LENGTH) {
    return { isValid: false, error: GYM_ERROR_MESSAGES.COMMENT_TOO_SHORT };
  }
  
  if (trimmedComment.length > REVIEW_VALIDATION.MAX_COMMENT_LENGTH) {
    return { isValid: false, error: GYM_ERROR_MESSAGES.COMMENT_TOO_LONG };
  }
  
  return { isValid: true };
}

/**
 * Validate complete review data
 */
export function validateReviewData(reviewData: ReviewData): ReviewValidationResult {
  // Validate rating
  const ratingValidation = validateRating(reviewData.rating);
  if (!ratingValidation.isValid) {
    return ratingValidation;
  }
  
  // Validate comment
  const commentValidation = validateComment(reviewData.comment);
  if (!commentValidation.isValid) {
    return commentValidation;
  }
  
  return { isValid: true };
}

// ============================================================================
// DATA TRANSFORMATION FUNCTIONS
// ============================================================================

/**
 * Create review record data
 * Following Clean Code principles - clear data structure creation
 */
export function createReviewRecordData(
  userId: string,
  reviewData: ReviewData
): any {
  const now = new Date().toISOString();
  
  return {
    profile_id: userId,
    gym_id: reviewData.gym_id,
    rating: reviewData.rating,
    comment: reviewData.comment || null,
    created_at: now,
    updated_at: now,
  };
}

/**
 * Transform review data for frontend
 */
export function transformReviewData(review: any): ReviewWithUser {
  return {
    id: review.id,
    rating: review.rating,
    comment: review.comment,
    created_at: review.created_at,
    updated_at: review.updated_at,
    profile_id: review.profile_id,
    gym_id: review.gym_id,
    user: review.profiles ? {
      full_name: review.profiles.full_name || 'Anonim Kullanıcı'
    } : null,
  };
}

// ============================================================================
// STATISTICS CALCULATION FUNCTIONS
// ============================================================================

/**
 * Calculate review statistics
 * Following Clean Code principles - focused calculation with clear purpose
 */
export function calculateReviewStats(reviews: any[]): ReviewStats {
  if (!Array.isArray(reviews) || reviews.length === 0) {
    return {
      averageRating: 0,
      totalReviews: 0,
    };
  }
  
  const validReviews = reviews.filter(review => 
    review.rating && 
    typeof review.rating === 'number' && 
    review.rating >= RATING_VALIDATION.MIN_RATING && 
    review.rating <= RATING_VALIDATION.MAX_RATING
  );
  
  if (validReviews.length === 0) {
    return {
      averageRating: 0,
      totalReviews: reviews.length,
    };
  }
  
  const totalRating = validReviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / validReviews.length;
  
  return {
    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
    totalReviews: reviews.length,
  };
}

/**
 * Calculate rating distribution
 */
export function calculateRatingDistribution(reviews: any[]): Record<number, number> {
  const distribution: Record<number, number> = {
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0,
  };
  
  if (!Array.isArray(reviews)) {
    return distribution;
  }
  
  reviews.forEach(review => {
    if (review.rating && 
        typeof review.rating === 'number' && 
        review.rating >= RATING_VALIDATION.MIN_RATING && 
        review.rating <= RATING_VALIDATION.MAX_RATING) {
      distribution[review.rating]++;
    }
  });
  
  return distribution;
}

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Create gym error with context
 * Following Clean Code principles - consistent error handling
 */
export function createGymError(baseMessage: string, details?: string): Error {
  const message = details ? `${baseMessage}: ${details}` : baseMessage;
  return new Error(message);
}

/**
 * Handle database error for gym operations
 */
export function handleGymDatabaseError(error: any, operation: string): never {
  const message = error?.message || 'Unknown database error';
  throw createGymError(
    GYM_ERROR_MESSAGES.REVIEW_SAVE_FAILED,
    `${operation} - ${message}`
  );
}

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

/**
 * Format date for display
 */
export function formatReviewDate(dateString: string): string {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Format rating for display
 */
export function formatRating(rating: number): string {
  if (!rating || rating < RATING_VALIDATION.MIN_RATING || rating > RATING_VALIDATION.MAX_RATING) {
    return '0.0';
  }
  
  return rating.toFixed(1);
}
