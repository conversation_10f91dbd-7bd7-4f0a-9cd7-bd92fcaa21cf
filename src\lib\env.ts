import { z, ZodError } from 'zod';

/**
 * Environment Variables Type Definitions
 * Provides type safety for environment variables used throughout the application
 */
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Next.js
      NEXT_PUBLIC_APP_URL: string;

      // Supabase
      SUPABASE_URL: string;
      SUPABASE_ANON_KEY: string;
      SUPABASE_SERVICE_ROLE_KEY: string;

      // Google OAuth (configured in Supabase dashboard)
      // These are not needed in code as Supabase handles OAuth internally
      // but documented here for reference
      // GOOGLE_CLIENT_ID: string;
      // GOOGLE_CLIENT_SECRET: string;
    }
  }
}

// Environment variables schema
const envSchema = z.object({
  // Supabase
  SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  SUPABASE_SERVICE_ROLE_KEY: z
    .string()
    .min(1, 'Supabase service role key is required')
    .optional(),

  // App
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  NEXT_PUBLIC_APP_URL: z
    .string()
    .url('Invalid app URL')
    .default('https://sportiva-ashen.vercel.app'),

  // Optional
  VERCEL_URL: z.string().optional(),
  VERCEL_ENV: z.string().optional(),
});

// Validate environment variables
function validateEnv() {
  // Skip validation on client side
  if (typeof window !== 'undefined') {
    return {
      SUPABASE_URL: process.env.SUPABASE_URL || '',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || '',
      NODE_ENV: process.env.NODE_ENV || 'development',
      NEXT_PUBLIC_APP_URL:
        process.env.NEXT_PUBLIC_APP_URL || 'https://sportiva-ashen.vercel.app',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      VERCEL_URL: process.env.VERCEL_URL || '',
      VERCEL_ENV: process.env.VERCEL_ENV || '',
    };
  }

  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof ZodError) {
      const missingVars = error.issues
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join('\n');
      throw new Error(`❌ Invalid environment variables:\n${missingVars}`);
    }
    throw error;
  }
}

// Export validated environment variables
export const env = validateEnv();

// Type-safe environment variables
export type Env = z.infer<typeof envSchema>;

// Helper to check if we're in production
export const isProduction = env.NODE_ENV === 'production';
export const isDevelopment = env.NODE_ENV === 'development';
export const isTest = env.NODE_ENV === 'test';

// Get app URL with fallback
export function getAppUrl() {
  // Development ortamında localhost kullan
  if (isDevelopment) {
    return 'http://localhost:3000';
  }

  // Production ortamında Vercel URL veya NEXT_PUBLIC_APP_URL kullan
  if (env.VERCEL_URL) {
    return `https://${env.VERCEL_URL}`;
  }
  return env.NEXT_PUBLIC_APP_URL;
}

export function getAuthCallbackUrl() {
  // Development
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3000/auth/callback';
  }

  // Production - hardcode
  return 'https://sportiva-ashen.vercel.app/auth/callback';
}
