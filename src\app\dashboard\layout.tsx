import { Metadata } from 'next';
import { Suspense } from 'react';
import DashboardShell from '@/components/dashboard/DashboardShell';
import DashboardContextBoundary from '@/components/dashboard/DashboardContextBoundary';

// Dashboard segmentinde PPR'ı tamamen kapat
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const metadata: Metadata = {
  title: 'Panel | Sportiva',
  description:
    'Sportiva kullanıcı paneli. Üyeliklerinizi yönetin, salonlarınızı takip edin.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<DashboardShell /> }>
      {/* Dinamik dashboard context ve header stream edilir */}
      <DashboardContextBoundary>
        {children}
      </DashboardContextBoundary>
    </Suspense>
  );
}
