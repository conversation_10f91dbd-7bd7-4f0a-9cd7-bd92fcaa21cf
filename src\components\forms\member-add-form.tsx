'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  UserPlus,
  AlertCircle,
  Loader2,
  ArrowLeft,
 
} from 'lucide-react';
import Link from 'next/link';
import { FITNESS_GOAL_STRINGS } from '@/lib/constants';
import { createMemberFromForm } from '@/lib/actions/user/create-user-actions';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  memberFormSchema,
  memberFormDefaults,
  convertToFormData,
  type MemberFormData,
} from '@/lib/validations/member-form-validation';
import { GuestMemberCredentialsDialog } from '@/components/dialogs/guest-member-credentials-dialog';

interface MemberAddFormProps {
  gymId: string;
  variant: 'trainer' | 'company';
  backUrl: string;
  successUrl: string;
}

export function MemberAddForm({
  gymId,
  variant,
  backUrl,
  successUrl
}: MemberAddFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCredentialsDialog, setShowCredentialsDialog] = useState(false);
  const [guestCredentials, setGuestCredentials] = useState<{
    memberName: string;
    email: string;
    password: string;
  } | null>(null);
  const router = useRouter();

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<MemberFormData>({
    resolver: zodResolver(memberFormSchema) as any,
    defaultValues: {
      ...memberFormDefaults,
      gymId,
      is_guest_mode: false,
    },
    mode: 'onChange',
  });

  // Watch guest mode değişikliği
  const isGuestMode = watch('is_guest_mode');

  // Misafir mode değiştiğinde email/telefon alanlarını temizle
  useEffect(() => {
    if (isGuestMode) {
      setValue('email', '');
      setValue('phone_number', '');
    }
  }, [isGuestMode, setValue]);

  // Form submit handler
  const onSubmit = async (data: MemberFormData) => {
    setIsSubmitting(true);

    try {
      const formData = convertToFormData(data);
      const result = await createMemberFromForm(formData);

      if (result.success && result.data) {
        if (result.data.guestEmail) {
          // Misafir üye için dialog göster
          setGuestCredentials({
            memberName: data.full_name,
            email: result.data.guestEmail,
            password: result.data.generatedPassword,
          });
          setShowCredentialsDialog(true);
          reset();
        } else {
          // Normal üye için toast göster
          toast.success(
            `Üye oluşturuldu! Şifre: ${result.data.generatedPassword}`
          );
          reset();
          router.push(successUrl);
        }
      } else {
        toast.error(result.error || 'Üye oluşturulurken bir hata oluştu');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Dialog kapatma handler'ı
  const handleCloseCredentialsDialog = () => {
    setShowCredentialsDialog(false);
    setGuestCredentials(null);
    router.push(successUrl);
  };

  const genderOptions = [
    { value: 'male', label: 'Erkek' },
    { value: 'female', label: 'Kadın' },
    { value: 'other', label: 'Diğer' },
    { value: 'prefer_not_to_say', label: 'Belirtmek istemiyorum' },
  ];

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button asChild variant="outline">
        <Link href={backUrl}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {variant === 'trainer' ? 'Üye Listesine Dön' : 'Geri'}
        </Link>
      </Button>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            {variant === 'trainer' ? 'Yeni Üye Ekle' : 'Üye Bilgileri'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-6"
            noValidate
            aria-label="Üye ekleme formu"
          >
            {/* Global form error */}
            {errors.email?.message && !isGuestMode && (
              <div
                className="flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200"
                role="alert"
                aria-live="polite"
              >
                <AlertCircle className="h-4 w-4" aria-hidden="true" />
                {errors.email.message}
              </div>
            )}

            {/* Misafir Mode Checkbox */}
            <div className="flex items-start space-x-3 rounded-md border p-4">
              <Checkbox
                id="is_guest_mode"
                checked={isGuestMode}
                onCheckedChange={checked =>
                  setValue('is_guest_mode', checked === true)
                }
                {...register('is_guest_mode')}
              />
              <div className="space-y-1 leading-none">
                <Label
                  htmlFor="is_guest_mode"
                  className="cursor-pointer text-sm font-medium"
                >
                  {variant === 'trainer'
                    ? 'Misafir üye (E-posta olmayan müşteriler için)'
                    : 'Misafir hesap oluştur'}
                </Label>
                <p className="text-muted-foreground text-xs">
                  {variant === 'trainer'
                    ? 'E-posta olmayan müşteriler için bu seçeneği işaretleyin.'
                    : 'Müşterinin e-posta ve telefonu yoksa bu seçeneği işaretleyin. Otomatik email adresi oluşturulur.'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Full Name */}
              <div className="space-y-2">
                <Label htmlFor="full_name">Ad Soyad *</Label>
                <Input
                  id="full_name"
                  placeholder="Ahmet Yılmaz"
                  {...register('full_name')}
                  className={errors.full_name ? 'border-red-500' : ''}
                  aria-invalid={errors.full_name ? 'true' : 'false'}
                  aria-describedby={
                    errors.full_name ? 'full_name-error' : 'full_name-help'
                  }
                />
                {errors.full_name && (
                  <p
                    id="full_name-error"
                    className="text-sm text-red-600 dark:text-red-400"
                    role="alert"
                  >
                    {errors.full_name.message}
                  </p>
                )}
                <p
                  id="full_name-help"
                  className="text-muted-foreground text-xs"
                >
                  Kullanıcının tam adı
                </p>
              </div>

              {/* Age */}
              <div className="space-y-2">
                <Label htmlFor="age">Yaş *</Label>
                <Input
                  id="age"
                  type="number"
                  placeholder="25"
                  {...register('age', { valueAsNumber: true })}
                  className={errors.age ? 'border-red-500' : ''}
                />
                {errors.age && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.age.message}
                  </p>
                )}
                <p className="text-muted-foreground text-xs">
                  Kullanıcının yaşı (16-100)
                </p>
              </div>

              {/* Phone Number */}
              <div className="space-y-2">
                <Label htmlFor="phone_number">Telefon Numarası</Label>
                <Input
                  id="phone_number"
                  type="tel"
                  placeholder="0532 123 45 67"
                  disabled={isGuestMode}
                  {...register('phone_number')}
                  className={errors.phone_number ? 'border-red-500' : ''}
                />
                {errors.phone_number && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.phone_number.message}
                  </p>
                )}
                <p className="text-muted-foreground text-xs">
                  {isGuestMode
                    ? 'Misafir modda telefon numarası gerekli değil'
                    : 'Opsiyonel - Kullanıcı bu telefon ile giriş yapabilir'}
                </p>
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email">E-posta Adresi</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={isGuestMode}
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.email.message}
                  </p>
                )}
                <p className="text-muted-foreground text-xs">
                  {isGuestMode
                    ? 'Misafir modda otomatik e-posta adresi oluşturulur'
                    : 'Opsiyonel - Kullanıcı bu e-posta ile giriş yapabilir'}
                </p>
              </div>
              {/* Weight */}
              <div className="space-y-2">
                <Label htmlFor="weight_kg">Kilo (kg)</Label>
                <Input
                  id="weight_kg"
                  type="number"
                  placeholder="70"
                  {...register('weight_kg', { valueAsNumber: true })}
                  className={errors.weight_kg ? 'border-red-500' : ''}
                />
                {errors.weight_kg && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.weight_kg.message}
                  </p>
                )}
                <p className="text-muted-foreground text-xs">
                  Opsiyonel (30-300 kg)
                </p>
              </div>
              {/* Height */}
              <div className="space-y-2">
                <Label htmlFor="height_cm">Boy (cm)</Label>
                <Input
                  id="height_cm"
                  type="number"
                  placeholder="175"
                  {...register('height_cm', { valueAsNumber: true })}
                  className={errors.height_cm ? 'border-red-500' : ''}
                />
                {errors.height_cm && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.height_cm.message}
                  </p>
                )}
                <p className="text-muted-foreground text-xs">
                  Opsiyonel (100-250 cm)
                </p>
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Label htmlFor="gender">Cinsiyet *</Label>
                <select
                  id="gender"
                  {...register('gender')}
                  className={`border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${
                    errors.gender ? 'border-red-500' : ''
                  }`}
                >
                  <option value="">Cinsiyet seçin</option>
                  {genderOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.gender && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.gender.message}
                  </p>
                )}
              </div>

              {/* Fitness Goal */}
              <div className="space-y-2">
                <Label htmlFor="fitness_goal">Fitness Hedefi</Label>
                <select
                  id="fitness_goal"
                  {...register('fitness_goal')}
                  className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Fitness hedefi seçin (opsiyonel)</option>
                  {FITNESS_GOAL_STRINGS.map(goal => (
                    <option key={goal} value={goal}>
                      {goal}
                    </option>
                  ))}
                </select>
                <p className="text-muted-foreground text-xs">
                  Kullanıcının fitness hedefi (opsiyonel)
                </p>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end gap-3 border-t pt-4">
              <Button type="button" variant="outline" asChild>
                <Link href={backUrl}>İptal</Link>
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                aria-describedby="submit-help"
              >
                {isSubmitting ? (
                  <>
                    <Loader2
                      className="mr-2 h-4 w-4 animate-spin"
                      aria-hidden="true"
                    />
                    Oluşturuluyor...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" aria-hidden="true" />
                    Üye Oluştur
                  </>
                )}
              </Button>
              <p id="submit-help" className="sr-only">
                Formu göndermek için bu butona tıklayın
              </p>
            </div>
          </form>
        </CardContent>

        {/* Misafir Üye Bilgileri Dialog'u */}
        {guestCredentials && (
          <GuestMemberCredentialsDialog
            isOpen={showCredentialsDialog}
            onClose={handleCloseCredentialsDialog}
            memberName={guestCredentials.memberName}
            email={guestCredentials.email}
            password={guestCredentials.password}
          />
        )}
      </Card>
    </div>
  );
}
