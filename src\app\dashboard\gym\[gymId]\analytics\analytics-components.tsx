'use client';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltipContent } from '@/components/ui/chart';
import { GymAnalyticsData } from '@/lib/actions/dashboard/company/dashboard-types';
import {
  DollarSign,
  TrendingUp,
  Users,
  UserCheck,
  ArrowUpIcon,
  ArrowDownIcon,
  BarChart3,
  PieChart,
} from 'lucide-react';
import dynamic from 'next/dynamic';
import { formatCurrency } from '@/lib/utils';
import { useMemo } from 'react';

// Dinamik Recharts importları (SSR kapalı) – ilk bundle boyutunu küçültür
const ResponsiveContainer = dynamic(
  () => import('recharts').then(m => m.ResponsiveContainer),
  { ssr: false }
);
const BarChart = dynamic(() => import('recharts').then(m => m.<PERSON>), {
  ssr: false,
});
const Bar = dynamic(() => import('recharts').then(m => m.Bar), { ssr: false });
const CartesianGrid = dynamic(
  () => import('recharts').then(m => m.CartesianGrid),
  { ssr: false }
);
const XAxis = dynamic(() => import('recharts').then(m => m.XAxis), {
  ssr: false,
});
const YAxis = dynamic(() => import('recharts').then(m => m.YAxis), {
  ssr: false,
});
const Tooltip = dynamic(() => import('recharts').then(m => m.Tooltip), {
  ssr: false,
});
const LineChart = dynamic(
  () => import('recharts').then(m => m.LineChart),
  { ssr: false }
);
const Line = dynamic(() => import('recharts').then(m => m.Line), {
  ssr: false,
});

interface RevenueCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: 'DollarSign' | 'TrendingUp' | 'Users' | 'UserCheck';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export function RevenueCard({
  title,
  value,
  description,
  icon,
  trend,
}: RevenueCardProps) {
  const IconComponent = {
    DollarSign,
    TrendingUp,
    Users,
    UserCheck,
  }[icon];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <IconComponent className="text-muted-foreground h-4 w-4" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend ? (
          <p className="text-muted-foreground flex items-center gap-1 text-xs">
            {trend.isPositive ? (
              <ArrowUpIcon className="h-3 w-3 text-green-500" />
            ) : (
              <ArrowDownIcon className="h-3 w-3 text-red-500" />
            )}
            <span
              className={trend.isPositive ? 'text-green-500' : 'text-red-500'}
            >
              {Math.abs(trend.value).toFixed(1)}%
            </span>
            <span>geçen aydan</span>
          </p>
        ) : (
          <p className="text-muted-foreground text-xs">{description}</p>
        )}
      </CardContent>
    </Card>
  );
}

interface RevenueChartProps {
  data: GymAnalyticsData['revenueData'];
}

export function RevenueChart({ data }: RevenueChartProps) {
  const chartConfigRevenue = useMemo(() => ({
    revenue: { label: 'Gelir', color: 'var(--primary)' },
  }), []);

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Aylık Gelir Trendi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            <BarChart3 className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Henüz gelir verisi yok</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Aylık Gelir Trendi
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfigRevenue} className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="month"
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#374151' }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#374151' }}
                tickFormatter={value => `₺${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip
                content={<ChartTooltipContent />}
                formatter={value => [formatCurrency(Number(value)), 'Gelir']}
              />
              <Bar
                dataKey="revenue"
                fill="var(--primary)"
                radius={[4, 4, 0, 0]}
                maxBarSize={60}
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

interface MemberGrowthChartProps {
  data: GymAnalyticsData['memberGrowthData'];
}

export function MemberGrowthChart({ data }: MemberGrowthChartProps) {
  const chartConfigMembers = useMemo(() => ({
    memberCount: { label: 'Toplam Üye', color: 'var(--primary)' },
  }), []);

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Üye Büyümesi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            <TrendingUp className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Henüz üye büyüme verisi yok</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Üye Büyümesi
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfigMembers} className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="month"
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#374151' }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#374151' }}
                domain={[0, 'dataMax + 1']}
              />
              <Tooltip content={<ChartTooltipContent />} />
              <Line
                type="monotone"
                dataKey="members"
                stroke="var(--primary)"
                strokeWidth={3}
                dot={{ fill: 'var(--primary)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--primary)', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

interface PackageRevenueListProps {
  data: GymAnalyticsData['topPackages'];
}

export function PackageRevenueList({ data }: PackageRevenueListProps) {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Paket Bazında Gelir
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            <PieChart className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Henüz paket gelir verisi yok</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChart className="h-5 w-5" />
          Paket Bazında Gelir
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((pkg, index) => (
            <div
              key={pkg.name}
              className="flex items-center justify-between rounded-lg border p-3"
            >
              <div className="flex items-center gap-3">
                <div
                  className="h-4 w-4 rounded-full"
                  style={{
                    backgroundColor: `hsl(${index * 60}, 70%, 50%)`,
                  }}
                />
                <div>
                  <p className="font-medium">{pkg.name}</p>
                  <p className="text-muted-foreground text-sm">
                    {pkg.sales} satış
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold">{formatCurrency(pkg.revenue)}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
