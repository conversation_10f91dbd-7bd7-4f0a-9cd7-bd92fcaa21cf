// Gym türleri
export const GYM_TYPES = [
  { value: 'fitness', label: 'Fitness' },
  { value: 'crossfit', label: 'CrossFit' },
  { value: 'yoga', label: 'Yoga' },
  { value: 'pilates', label: 'Pilates' },
  { value: 'martial_arts', label: 'Dövüş Sanatları' },
  { value: 'swimming', label: 'Yüzme' },
  { value: 'reformer', label: 'Reformer' },
  { value: 'boxing', label: 'Boks Salonu' },
  { value: 'functional_training', label: 'Fonksiyonel Antrenman' },
  { value: 'outdoor', label: 'Açık Hava Spor Alanı' },
  { value: 'rehabilitation', label: 'Rehabilitasyon Merkezi' },
  { value: 'other', label: '<PERSON><PERSON><PERSON>' },
];

// Gruplandırılmış salon özellikleri
export const FEATURE_GROUPS = {
  temel_ozellikler: {
    label: 'Temel Özellikler',
    features: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      'Grup Dersleri',
      '<PERSON><PERSON><PERSON><PERSON>ö<PERSON>',
      'Soyunma Odası',
      '<PERSON><PERSON>',
      'Otopark',
      'WiFi',
      '<PERSON><PERSON><PERSON>',
      'Müzik Sistemi',
    ],
  },
  antrenman_alanlari: {
    label: 'Antrenman Alanları',
    features: [
      'Serbest Ağırlık Alanı',
      'Squat Rack',
      'Fonksiyonel Antrenman Alanı',
      'TRX Sistemi',
      'Kardiyo Sinema',
      'HIIT Alanı',
      'Reformer Pilates Aletleri',
      'Egzersiz Bisikleti',
      'Rowing Machine',
      'Kettlebell Alanı',
    ],
  },
  wellness: {
    label: 'Wellness & Spa',
    features: [
      'Sauna',
      'Buhar Odası',
      'Yüzme Havuzu',
      'Masaj Hizmeti',
      'Spa Hizmeti',
      'Infraruj Terapi',
      'Buz Banyosu',
    ],
  },
  saglik_hizmetleri: {
    label: 'Sağlık Hizmetleri',
    features: [
      'Fizyoterapi',
      'Diyetisyen Danışmanlığı',
      'Online Dersler',
      'Beslenme Seminerleri',
      'Eğitmen Sertifika Programları',
    ],
  },
  sosyal_alanlar: {
    label: 'Sosyal Alanlar & Yaşam',
    features: [
      'Kafeterya',
      'Vitamin Bar',
      'Dinlenme Alanı',
      'Açık Hava Spor Alanı',
      'Teras',
      'Üye Etkinlikleri',
    ],
  },
  aile_dostu: {
    label: 'Aile & Çocuk',
    features: [
      'Çocuk Oyun Alanı',
      'Çocuk Bakım Hizmeti',
      'Gençlere Özel Programlar',
      'Aile Üyeliği Seçenekleri',
    ],
  },
  teknoloji: {
    label: 'Teknoloji',
    features: [
      'Mobil Uygulama Desteği',
      'QR Kod ile Giriş',
      'Akıllı Ekipmanlar',
      'VR Destekli Egzersizler',
      'Spor Takip Cihazlarıyla Uyumluluk',
    ],
  },
  guvenlik_erisim: {
    label: 'Güvenlik & Erişim',
    features: [
      'Güvenlik Kamerası',
      'Kartlı Giriş Sistemi',
      'Kişisel Dolaplar',
      'Havlu Servisi',
      '24 Saat Açık',
      'Kadın-Erkek Ayrı Alanlar',
      'Engelli Erişimi',
    ],
  },
};

export const CITIES = [
  {
    id: 1,
    name: 'Adana',
    districts: [
      'Aladağ',
      'Ceyhan',
      'Çukurova',
      'Feke',
      'İmamoğlu',
      'Karaisalı',
      'Karataş',
      'Kozan',
      'Pozantı',
      'Saimbeyli',
      'Sarıçam',
      'Seyhan',
      'Tufanbeyli',
      'Yumurtalık',
      'Yüreğir',
    ],
  },
  {
    id: 2,
    name: 'Adıyaman',
    districts: [
      'Besni',
      'Çelikhan',
      'Gerger',
      'Gölbaşı',
      'Kahta',
      'Merkez',
      'Samsat',
      'Sincik',
      'Tut',
    ],
  },
  {
    id: 3,
    name: 'Afyonkarahisar',
    districts: [
      'Başmakçı',
      'Bayat',
      'Bolvadin',
      'Çay',
      'Çobanlar',
      'Dazkırı',
      'Dinar',
      'Emirdağ',
      'Evciler',
      'Hocalar',
      'İhsaniye',
      'İscehisar',
      'Kızılören',
      'Merkez',
      'Sandıklı',
      'Sinanpaşa',
      'Sultandağı',
      'Şuhut',
    ],
  },
  {
    id: 4,
    name: 'Ağrı',
    districts: [
      'Diyadin',
      'Doğubayazıt',
      'Eleşkirt',
      'Hamur',
      'Merkez',
      'Patnos',
      'Taşlıçay',
      'Tutak',
    ],
  },
  {
    id: 5,
    name: 'Amasya',
    districts: [
      'Göynücek',
      'Gümüşhacıköy',
      'Hamamözü',
      'Merkez',
      'Merzifon',
      'Suluova',
      'Taşova',
    ],
  },
  {
    id: 6,
    name: 'Ankara',
    districts: [
      'Akyurt',
      'Altındağ',
      'Ayaş',
      'Bala',
      'Beypazarı',
      'Çamlıdere',
      'Çankaya',
      'Çubuk',
      'Elmadağ',
      'Etimesgut',
      'Evren',
      'Gölbaşı',
      'Güdül',
      'Haymana',
      'Kahramankazan',
      'Kalecik',
      'Keçiören',
      'Kızılcahamam',
      'Mamak',
      'Nallıhan',
      'Polatlı',
      'Pursaklar',
      'Sincan',
      'Şereflikoçhisar',
      'Yenimahalle',
    ],
  },
  {
    id: 7,
    name: 'Antalya',
    districts: [
      'Akseki',
      'Aksu',
      'Alanya',
      'Demre',
      'Döşemealtı',
      'Elmalı',
      'Finike',
      'Gazipaşa',
      'Gündoğmuş',
      'İbradı',
      'Kaş',
      'Kemer',
      'Kepez',
      'Konyaaltı',
      'Korkuteli',
      'Kumluca',
      'Manavgat',
      'Muratpaşa',
      'Serik',
    ],
  },
  {
    id: 8,
    name: 'Artvin',
    districts: [
      'Ardanuç',
      'Arhavi',
      'Borçka',
      'Hopa',
      'Kemalpaşa',
      'Merkez',
      'Murgul',
      'Şavşat',
      'Yusufeli',
    ],
  },
  {
    id: 9,
    name: 'Aydın',
    districts: [
      'Bozdoğan',
      'Buharkent',
      'Çine',
      'Didim',
      'Efeler',
      'Germencik',
      'İncirliova',
      'Karacasu',
      'Karpuzlu',
      'Koçarlı',
      'Köşk',
      'Kuşadası',
      'Kuyucak',
      'Nazilli',
      'Söke',
      'Sultanhisar',
      'Yenipazar',
    ],
  },
  {
    id: 10,
    name: 'Balıkesir',
    districts: [
      'Altıeylül',
      'Ayvalık',
      'Balya',
      'Bandırma',
      'Bigadiç',
      'Burhaniye',
      'Dursunbey',
      'Edremit',
      'Erdek',
      'Gömeç',
      'Gönen',
      'Havran',
      'İvrindi',
      'Karesi',
      'Kepsut',
      'Manyas',
      'Marmara',
      'Savaştepe',
      'Sındırgı',
      'Susurluk',
    ],
  },
  {
    id: 11,
    name: 'Bilecik',
    districts: [
      'Bozüyük',
      'Gölpazarı',
      'İnhisar',
      'Merkez',
      'Osmaneli',
      'Pazaryeri',
      'Söğüt',
      'Yenipazar',
    ],
  },
  {
    id: 12,
    name: 'Bingöl',
    districts: [
      'Adaklı',
      'Genç',
      'Karlıova',
      'Kiğı',
      'Merkez',
      'Solhan',
      'Yayladere',
      'Yedisu',
    ],
  },
  {
    id: 13,
    name: 'Bitlis',
    districts: [
      'Adilcevaz',
      'Ahlat',
      'Güroymak',
      'Hizan',
      'Merkez',
      'Mutki',
      'Tatvan',
    ],
  },
  {
    id: 14,
    name: 'Bolu',
    districts: [
      'Dörtdivan',
      'Gerede',
      'Göynük',
      'Kıbrıscık',
      'Mengen',
      'Merkez',
      'Mudurnu',
      'Seben',
      'Yeniçağa',
    ],
  },
  {
    id: 15,
    name: 'Burdur',
    districts: [
      'Ağlasun',
      'Altınyayla',
      'Bucak',
      'Çavdır',
      'Çeltikçi',
      'Gölhisar',
      'Karamanlı',
      'Kemer',
      'Merkez',
      'Tefenni',
      'Yeşilova',
    ],
  },
  {
    id: 16,
    name: 'Bursa',
    districts: [
      'Büyükorhan',
      'Gemlik',
      'Gürsu',
      'Harmancık',
      'İnegöl',
      'İznik',
      'Karacabey',
      'Keles',
      'Kestel',
      'Mudanya',
      'Mustafakemalpaşa',
      'Nilüfer',
      'Orhaneli',
      'Orhangazi',
      'Osmangazi',
      'Yenişehir',
      'Yıldırım',
    ],
  },
  {
    id: 17,
    name: 'Çanakkale',
    districts: [
      'Ayvacık',
      'Bayramiç',
      'Biga',
      'Bozcaada',
      'Çan',
      'Eceabat',
      'Ezine',
      'Gelibolu',
      'Gökçeada',
      'Lapseki',
      'Merkez',
      'Yenice',
    ],
  },
  {
    id: 18,
    name: 'Çankırı',
    districts: [
      'Atkaracalar',
      'Bayramören',
      'Çerkeş',
      'Eldivan',
      'Ilgaz',
      'Kızılırmak',
      'Korgun',
      'Kurşunlu',
      'Merkez',
      'Orta',
      'Şabanözü',
      'Yapraklı',
    ],
  },
  {
    id: 19,
    name: 'Çorum',
    districts: [
      'Alaca',
      'Bayat',
      'Boğazkale',
      'Dodurga',
      'İskilip',
      'Kargı',
      'Laçin',
      'Mecitözü',
      'Merkez',
      'Oğuzlar',
      'Ortaköy',
      'Osmancık',
      'Sungurlu',
      'Uğurludağ',
    ],
  },
  {
    id: 20,
    name: 'Denizli',
    districts: [
      'Acıpayam',
      'Babadağ',
      'Baklan',
      'Bekilli',
      'Beyağaç',
      'Bozkurt',
      'Buldan',
      'Çal',
      'Çameli',
      'Çardak',
      'Çivril',
      'Güney',
      'Honaz',
      'Kale',
      'Merkezefendi',
      'Pamukkale',
      'Sarayköy',
      'Serinhisar',
      'Tavas',
    ],
  },
  {
    id: 21,
    name: 'Diyarbakır',
    districts: [
      'Bağlar',
      'Bismil',
      'Çermik',
      'Çınar',
      'Çüngüş',
      'Dicle',
      'Eğil',
      'Ergani',
      'Hani',
      'Hazro',
      'Kayapınar',
      'Kocaköy',
      'Kulp',
      'Lice',
      'Silvan',
      'Sur',
      'Yenişehir',
    ],
  },
  {
    id: 22,
    name: 'Edirne',
    districts: [
      'Enez',
      'Havsa',
      'İpsala',
      'Keşan',
      'Lalapaşa',
      'Meriç',
      'Merkez',
      'Süloğlu',
      'Uzunköprü',
    ],
  },
  {
    id: 23,
    name: 'Elazığ',
    districts: [
      'Ağın',
      'Alacakaya',
      'Arıcak',
      'Baskil',
      'Karakoçan',
      'Keban',
      'Kovancılar',
      'Maden',
      'Merkez',
      'Palu',
      'Sivrice',
    ],
  },
  {
    id: 24,
    name: 'Erzincan',
    districts: [
      'Çayırlı',
      'İliç',
      'Kemah',
      'Kemaliye',
      'Merkez',
      'Otlukbeli',
      'Refahiye',
      'Tercan',
      'Üzümlü',
    ],
  },
  {
    id: 25,
    name: 'Erzurum',
    districts: [
      'Aşkale',
      'Aziziye',
      'Çat',
      'Hınıs',
      'Horasan',
      'İspir',
      'Karaçoban',
      'Karayazı',
      'Köprüköy',
      'Narman',
      'Oltu',
      'Olur',
      'Palandöken',
      'Pasinler',
      'Pazaryolu',
      'Şenkaya',
      'Tekman',
      'Tortum',
      'Uzundere',
      'Yakutiye',
    ],
  },
  {
    id: 26,
    name: 'Eskişehir',
    districts: [
      'Alpu',
      'Beylikova',
      'Çifteler',
      'Günyüzü',
      'Han',
      'İnönü',
      'Mahmudiye',
      'Mihalgazi',
      'Mihalıççık',
      'Odunpazarı',
      'Sarıcakaya',
      'Seyitgazi',
      'Sivrihisar',
      'Tepebaşı',
    ],
  },
  {
    id: 27,
    name: 'Gaziantep',
    districts: [
      'Araban',
      'İslahiye',
      'Karkamış',
      'Nizip',
      'Nurdağı',
      'Oğuzeli',
      'Şahinbey',
      'Şehitkamil',
      'Yavuzeli',
    ],
  },
  {
    id: 28,
    name: 'Giresun',
    districts: [
      'Alucra',
      'Bulancak',
      'Çamoluk',
      'Çanakçı',
      'Dereli',
      'Doğankent',
      'Espiye',
      'Eynesil',
      'Görele',
      'Güce',
      'Keşap',
      'Merkez',
      'Piraziz',
      'Şebinkarahisar',
      'Tirebolu',
      'Yağlıdere',
    ],
  },
  {
    id: 29,
    name: 'Gümüşhane',
    districts: ['Kelkit', 'Köse', 'Kürtün', 'Merkez', 'Şiran', 'Torul'],
  },
  {
    id: 30,
    name: 'Hakkâri',
    districts: ['Çukurca', 'Derecik', 'Merkez', 'Şemdinli', 'Yüksekova'],
  },
  {
    id: 31,
    name: 'Hatay',
    districts: [
      'Altınözü',
      'Antakya',
      'Arsuz',
      'Belen',
      'Defne',
      'Dörtyol',
      'Erzin',
      'Hassa',
      'İskenderun',
      'Kırıkhan',
      'Kumlu',
      'Payas',
      'Reyhanlı',
      'Samandağ',
      'Yayladağı',
    ],
  },
  {
    id: 32,
    name: 'Isparta',
    districts: [
      'Aksu',
      'Atabey',
      'Eğirdir',
      'Gelendost',
      'Gönen',
      'Keçiborlu',
      'Merkez',
      'Senirkent',
      'Sütçüler',
      'Şarkikaraağaç',
      'Uluborlu',
      'Yalvaç',
      'Yenişarbademli',
    ],
  },
  {
    id: 33,
    name: 'Mersin',
    districts: [
      'Akdeniz',
      'Anamur',
      'Aydıncık',
      'Bozyazı',
      'Çamlıyayla',
      'Erdemli',
      'Gülnar',
      'Mezitli',
      'Mut',
      'Silifke',
      'Tarsus',
      'Toroslar',
      'Yenişehir',
    ],
  },
  {
    id: 34,
    name: 'İstanbul',
    districts: [
      'Adalar',
      'Arnavutköy',
      'Ataşehir',
      'Avcılar',
      'Bağcılar',
      'Bahçelievler',
      'Bakırköy',
      'Başakşehir',
      'Bayrampaşa',
      'Beşiktaş',
      'Beykoz',
      'Beylikdüzü',
      'Beyoğlu',
      'Büyükçekmece',
      'Çatalca',
      'Çekmeköy',
      'Esenler',
      'Esenyurt',
      'Eyüpsultan',
      'Fatih',
      'Gaziosmanpaşa',
      'Güngören',
      'Kadıköy',
      'Kağıthane',
      'Kartal',
      'Küçükçekmece',
      'Maltepe',
      'Pendik',
      'Sancaktepe',
      'Sarıyer',
      'Silivri',
      'Sultanbeyli',
      'Sultangazi',
      'Şile',
      'Şişli',
      'Tuzla',
      'Ümraniye',
      'Üsküdar',
      'Zeytinburnu',
    ],
  },
  {
    id: 35,
    name: 'İzmir',
    districts: [
      'Aliağa',
      'Balçova',
      'Bayındır',
      'Bayraklı',
      'Bergama',
      'Beydağ',
      'Bornova',
      'Buca',
      'Çeşme',
      'Çiğli',
      'Dikili',
      'Foça',
      'Gaziemir',
      'Güzelbahçe',
      'Karabağlar',
      'Karaburun',
      'Karşıyaka',
      'Kemalpaşa',
      'Kınık',
      'Kiraz',
      'Konak',
      'Menderes',
      'Menemen',
      'Narlıdere',
      'Ödemiş',
      'Seferihisar',
      'Selçuk',
      'Tire',
      'Torbalı',
      'Urla',
    ],
  },
  {
    id: 36,
    name: 'Kars',
    districts: [
      'Akyaka',
      'Arpaçay',
      'Digor',
      'Kağızman',
      'Merkez',
      'Sarıkamış',
      'Selim',
      'Susuz',
    ],
  },
  {
    id: 37,
    name: 'Kastamonu',
    districts: [
      'Abana',
      'Ağlı',
      'Araç',
      'Azdavay',
      'Bozkurt',
      'Cide',
      'Çatalzeytin',
      'Daday',
      'Devrekani',
      'Doğanyurt',
      'Hanönü',
      'İhsangazi',
      'İnebolu',
      'Küre',
      'Merkez',
      'Pınarbaşı',
      'Seydiler',
      'Şenpazar',
      'Taşköprü',
      'Tosya',
    ],
  },
  {
    id: 38,
    name: 'Kayseri',
    districts: [
      'Akkışla',
      'Bünyan',
      'Develi',
      'Felahiye',
      'Hacılar',
      'İncesu',
      'Kocasinan',
      'Melikgazi',
      'Özvatan',
      'Pınarbaşı',
      'Sarıoğlan',
      'Sarız',
      'Talas',
      'Tomarza',
      'Yahyalı',
      'Yeşilhisar',
    ],
  },
  {
    id: 39,
    name: 'Kırklareli',
    districts: [
      'Babaeski',
      'Demirköy',
      'Kofçaz',
      'Lüleburgaz',
      'Merkez',
      'Pehlivanköy',
      'Pınarhisar',
      'Vize',
    ],
  },
  {
    id: 40,
    name: 'Kırşehir',
    districts: [
      'Akçakent',
      'Akpınar',
      'Boztepe',
      'Çiçekdağı',
      'Kaman',
      'Merkez',
      'Mucur',
    ],
  },
  {
    id: 41,
    name: 'Kocaeli',
    districts: [
      'Başiskele',
      'Çayırova',
      'Darıca',
      'Derince',
      'Dilovası',
      'Gebze',
      'Gölcük',
      'İzmit',
      'Kandıra',
      'Karamürsel',
      'Kartepe',
      'Körfez',
    ],
  },
  {
    id: 42,
    name: 'Konya',
    districts: [
      'Ahırlı',
      'Akören',
      'Akşehir',
      'Altınekin',
      'Beyşehir',
      'Bozkır',
      'Cihanbeyli',
      'Çeltik',
      'Çumra',
      'Derbent',
      'Derebucak',
      'Doğanhisar',
      'Emirgazi',
      'Ereğli',
      'Güneysınır',
      'Hadim',
      'Halkapınar',
      'Hüyük',
      'Ilgın',
      'Kadınhanı',
      'Karapınar',
      'Karatay',
      'Kulu',
      'Meram',
      'Sarayönü',
      'Selçuklu',
      'Seydişehir',
      'Taşkent',
      'Tuzlukçu',
      'Yalıhüyük',
      'Yunak',
    ],
  },
  {
    id: 43,
    name: 'Kütahya',
    districts: [
      'Altıntaş',
      'Aslanapa',
      'Çavdarhisar',
      'Domaniç',
      'Dumlupınar',
      'Emet',
      'Gediz',
      'Hisarcık',
      'Merkez',
      'Pazarlar',
      'Simav',
      'Şaphane',
      'Tavşanlı',
    ],
  },
  {
    id: 44,
    name: 'Malatya',
    districts: [
      'Akçadağ',
      'Arapgir',
      'Arguvan',
      'Battalgazi',
      'Darende',
      'Doğanşehir',
      'Doğanyol',
      'Hekimhan',
      'Kale',
      'Kuluncak',
      'Pütürge',
      'Yazıhan',
      'Yeşilyurt',
    ],
  },
  {
    id: 45,
    name: 'Manisa',
    districts: [
      'Ahmetli',
      'Akhisar',
      'Alaşehir',
      'Demirci',
      'Gölmarmara',
      'Gördes',
      'Kırkağaç',
      'Köprübaşı',
      'Kula',
      'Salihli',
      'Sarıgöl',
      'Saruhanlı',
      'Selendi',
      'Soma',
      'Şehzadeler',
      'Turgutlu',
      'Yunusemre',
    ],
  },
  {
    id: 46,
    name: 'Kahramanmaraş',
    districts: [
      'Afşin',
      'Andırın',
      'Çağlayancerit',
      'Dulkadiroğlu',
      'Ekinözü',
      'Elbistan',
      'Göksun',
      'Nurhak',
      'Onikişubat',
      'Pazarcık',
      'Türkoğlu',
    ],
  },
  {
    id: 47,
    name: 'Mardin',
    districts: [
      'Artuklu',
      'Dargeçit',
      'Derik',
      'Kızıltepe',
      'Mazıdağı',
      'Midyat',
      'Nusaybin',
      'Ömerli',
      'Savur',
      'Yeşilli',
    ],
  },
  {
    id: 48,
    name: 'Muğla',
    districts: [
      'Bodrum',
      'Dalaman',
      'Datça',
      'Fethiye',
      'Kavaklıdere',
      'Köyceğiz',
      'Marmaris',
      'Menteşe',
      'Milas',
      'Ortaca',
      'Seydikemer',
      'Ula',
      'Yatağan',
    ],
  },
  {
    id: 49,
    name: 'Muş',
    districts: ['Bulanık', 'Hasköy', 'Korkut', 'Malazgirt', 'Merkez', 'Varto'],
  },
  {
    id: 50,
    name: 'Nevşehir',
    districts: [
      'Acıgöl',
      'Avanos',
      'Derinkuyu',
      'Gülşehir',
      'Hacıbektaş',
      'Kozaklı',
      'Merkez',
      'Ürgüp',
    ],
  },
  {
    id: 51,
    name: 'Niğde',
    districts: [
      'Altunhisar',
      'Bor',
      'Çamardı',
      'Çiftlik',
      'Merkez',
      'Ulukışla',
    ],
  },
  {
    id: 52,
    name: 'Ordu',
    districts: [
      'Akkuş',
      'Altınordu',
      'Aybastı',
      'Çamaş',
      'Çatalpınar',
      'Çaybaşı',
      'Fatsa',
      'Gölköy',
      'Gülyalı',
      'Gürgentepe',
      'İkizce',
      'Kabadüz',
      'Kabataş',
      'Korgan',
      'Kumru',
      'Mesudiye',
      'Perşembe',
      'Ulubey',
      'Ünye',
    ],
  },
  {
    id: 53,
    name: 'Rize',
    districts: [
      'Ardeşen',
      'Çamlıhemşin',
      'Çayeli',
      'Derepazarı',
      'Fındıklı',
      'Güneysu',
      'Hemşin',
      'İkizdere',
      'İyidere',
      'Kalkandere',
      'Merkez',
      'Pazar',
    ],
  },
  {
    id: 54,
    name: 'Sakarya',
    districts: [
      'Adapazarı',
      'Akyazı',
      'Arifiye',
      'Erenler',
      'Ferizli',
      'Geyve',
      'Hendek',
      'Karapürçek',
      'Karasu',
      'Kaynarca',
      'Kocaali',
      'Pamukova',
      'Sapanca',
      'Serdivan',
      'Söğütlü',
      'Taraklı',
    ],
  },
  {
    id: 55,
    name: 'Samsun',
    districts: [
      '19 Mayıs',
      'Alaçam',
      'Asarcık',
      'Atakum',
      'Ayvacık',
      'Bafra',
      'Canik',
      'Çarşamba',
      'Havza',
      'İlkadım',
      'Kavak',
      'Ladik',
      'Salıpazarı',
      'Tekkeköy',
      'Terme',
      'Vezirköprü',
      'Yakakent',
    ],
  },
  {
    id: 56,
    name: 'Siirt',
    districts: [
      'Baykan',
      'Eruh',
      'Kurtalan',
      'Merkez',
      'Pervari',
      'Şirvan',
      'Tillo',
    ],
  },
  {
    id: 57,
    name: 'Sinop',
    districts: [
      'Ayancık',
      'Boyabat',
      'Dikmen',
      'Durağan',
      'Erfelek',
      'Gerze',
      'Merkez',
      'Saraydüzü',
      'Türkeli',
    ],
  },
  {
    id: 58,
    name: 'Sivas',
    districts: [
      'Akıncılar',
      'Altınyayla',
      'Divriği',
      'Doğanşar',
      'Gemerek',
      'Gölova',
      'Hafik',
      'İmranlı',
      'Kangal',
      'Koyulhisar',
      'Merkez',
      'Suşehri',
      'Şarkışla',
      'Ulaş',
      'Yıldızeli',
      'Zara',
      'Gürün',
    ],
  },
  {
    id: 59,
    name: 'Tekirdağ',
    districts: [
      'Çerkezköy',
      'Çorlu',
      'Ergene',
      'Hayrabolu',
      'Kapaklı',
      'Malkara',
      'Muratlı',
      'Saray',
      'Süleymanpaşa',
      'Şarköy',
      'Marmaraereğlisi',
    ],
  },
  {
    id: 60,
    name: 'Tokat',
    districts: [
      'Almus',
      'Artova',
      'Başçiftlik',
      'Erbaa',
      'Merkez',
      'Niksar',
      'Pazar',
      'Reşadiye',
      'Sulusaray',
      'Turhal',
      'Yeşilyurt',
      'Zile',
    ],
  },
  {
    id: 61,
    name: 'Trabzon',
    districts: [
      'Akçaabat',
      'Araklı',
      'Arsin',
      'Beşikdüzü',
      'Çarşıbaşı',
      'Çaykara',
      'Dernekpazarı',
      'Düzköy',
      'Hayrat',
      'Köprübaşı',
      'Maçka',
      'Of',
      'Ortahisar',
      'Sürmene',
      'Şalpazarı',
      'Tonya',
      'Vakfıkebir',
      'Yomra',
    ],
  },
  {
    id: 62,
    name: 'Tunceli',
    districts: [
      'Çemişgezek',
      'Hozat',
      'Mazgirt',
      'Merkez',
      'Nazımiye',
      'Ovacık',
      'Pertek',
      'Pülümür',
    ],
  },
  {
    id: 63,
    name: 'Şanlıurfa',
    districts: [
      'Akçakale',
      'Birecik',
      'Bozova',
      'Ceylanpınar',
      'Eyyübiye',
      'Halfeti',
      'Haliliye',
      'Harran',
      'Hilvan',
      'Karaköprü',
      'Siverek',
      'Suruç',
      'Viranşehir',
    ],
  },
  {
    id: 64,
    name: 'Uşak',
    districts: ['Banaz', 'Eşme', 'Karahallı', 'Merkez', 'Sivaslı', 'Ulubey'],
  },
  {
    id: 65,
    name: 'Van',
    districts: [
      'Bahçesaray',
      'Başkale',
      'Çaldıran',
      'Çatak',
      'Edremit',
      'Erciş',
      'Gevaş',
      'Gürpınar',
      'İpekyolu',
      'Muradiye',
      'Özalp',
      'Saray',
      'Tuşba',
    ],
  },
  {
    id: 66,
    name: 'Yozgat',
    districts: [
      'Akdağmadeni',
      'Aydıncık',
      'Boğazlıyan',
      'Çandır',
      'Çayıralan',
      'Çekerek',
      'Kadışehri',
      'Merkez',
      'Saraykent',
      'Sarıkaya',
      'Sorgun',
      'Şefaatli',
      'Yenifakılı',
      'Yerköy',
    ],
  },
  {
    id: 67,
    name: 'Zonguldak',
    districts: [
      'Alaplı',
      'Çaycuma',
      'Devrek',
      'Ereğli',
      'Gökçebey',
      'Kilimli',
      'Kozlu',
      'Merkez',
    ],
  },
  {
    id: 68,
    name: 'Aksaray',
    districts: [
      'Ağaçören',
      'Eskil',
      'Gülağaç',
      'Güzelyurt',
      'Merkez',
      'Ortaköy',
      'Sarıyahşi',
      'Sultanhanı',
    ],
  },
  {
    id: 69,
    name: 'Bayburt',
    districts: ['Aydıntepe', 'Demirözü', 'Merkez'],
  },
  {
    id: 70,
    name: 'Karaman',
    districts: [
      'Ayrancı',
      'Başyayla',
      'Ermenek',
      'Kazımkarabekir',
      'Merkez',
      'Sarıveliler',
    ],
  },
  {
    id: 71,
    name: 'Kırıkkale',
    districts: [
      'Bahşılı',
      'Balışeyh',
      'Çelebi',
      'Delice',
      'Karakeçili',
      'Keskin',
      'Merkez',
      'Sulakyurt',
      'Yahşihan',
    ],
  },
  {
    id: 72,
    name: 'Batman',
    districts: ['Beşiri', 'Gercüş', 'Hasankeyf', 'Kozluk', 'Merkez', 'Sason'],
  },
  {
    id: 73,
    name: 'Şırnak',
    districts: [
      'Beytüşşebap',
      'Cizre',
      'Güçlükonak',
      'İdil',
      'Merkez',
      'Silopi',
      'Uludere',
    ],
  },
  {
    id: 74,
    name: 'Bartın',
    districts: ['Amasra', 'Kurucaşile', 'Merkez', 'Ulus'],
  },
  {
    id: 75,
    name: 'Ardahan',
    districts: ['Çıldır', 'Damal', 'Göle', 'Hanak', 'Merkez', 'Posof'],
  },
  {
    id: 76,
    name: 'Iğdır',
    districts: ['Aralık', 'Karakoyunlu', 'Merkez', 'Tuzluca'],
  },
  {
    id: 77,
    name: 'Yalova',
    districts: [
      'Altınova',
      'Armutlu',
      'Çınarcık',
      'Çiftlikköy',
      'Merkez',
      'Termal',
    ],
  },
  {
    id: 78,
    name: 'Karabük',
    districts: [
      'Eflani',
      'Eskipazar',
      'Merkez',
      'Ovacık',
      'Safranbolu',
      'Yenice',
    ],
  },
  {
    id: 79,
    name: 'Kilis',
    districts: ['Elbeyli', 'Merkez', 'Musabeyli', 'Polateli'],
  },
  {
    id: 80,
    name: 'Osmaniye',
    districts: [
      'Bahçe',
      'Düziçi',
      'Hasanbeyli',
      'Kadirli',
      'Merkez',
      'Sumbas',
      'Toprakkale',
    ],
  },
  {
    id: 81,
    name: 'Düzce',
    districts: [
      'Akçakoca',
      'Çilimli',
      'Cumayeri',
      'Gölyaka',
      'Gümüşova',
      'Kaynaşlı',
      'Merkez',
      'Yığılca',
    ],
  },
];

// Şehir slug mapping'i oluşturma fonksiyonu
function generateCitySlug(cityName: string): string {
  return cityName
    .toLowerCase()
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

// Şehir slug mapping'leri
export const CITY_SLUG_MAP = (() => {
  const slugToCity: Record<string, (typeof CITIES)[0]> = {};
  const cityToSlug: Record<string, string> = {};

  CITIES.forEach(city => {
    const slug = generateCitySlug(city.name);
    slugToCity[slug] = city;
    cityToSlug[city.id.toString()] = slug;
  });

  return { slugToCity, cityToSlug };
})();

/**
 * Şehir ID'sini şehir adına dönüştürür
 * @param cityId - Şehir ID'si (number veya string)
 * @returns Şehir adı veya null (bulunamazsa)
 */
export function getCityNameById(cityId: number | string): string | null {
  const id = typeof cityId === 'string' ? parseInt(cityId, 10) : cityId;

  if (isNaN(id)) {
    return null;
  }

  const city = CITIES.find(city => city.id === id);
  return city ? city.name : null;
}

/**
 * Şehir slug'ından şehir bilgisini getirir
 * @param slug - Şehir slug'ı
 * @returns Şehir bilgisi veya null
 */
export function getCityBySlug(slug: string) {
  return CITY_SLUG_MAP.slugToCity[slug] || null;
}

/**
 * Şehir ID'sinden slug getirir
 * @param cityId - Şehir ID'si
 * @returns Şehir slug'ı veya null
 */
export function getCitySlugById(cityId: number | string): string | null {
  return CITY_SLUG_MAP.cityToSlug[cityId.toString()] || null;
}
