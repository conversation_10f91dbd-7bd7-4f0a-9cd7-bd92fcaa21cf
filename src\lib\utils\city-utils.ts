import { CITIES } from '../constants';

// Şehir slug'ı oluşturma fonksiyonu
export function generateCitySlug(cityName: string): string {
  return cityName
    .replace(/İ/g, 'I') // Önce büyük İ'yi I'ya çevir
    .replace(/ı/g, 'i') // Küçük ı'yı i'ya çevir
    .replace(/ğ/g, 'g')
    .replace(/Ğ/g, 'G')
    .replace(/ü/g, 'u')
    .replace(/Ü/g, 'U')
    .replace(/ş/g, 's')
    .replace(/Ş/g, 'S')
    .replace(/ö/g, 'o')
    .replace(/Ö/g, 'O')
    .replace(/ç/g, 'c')
    .replace(/Ç/g, 'C')
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

// İlçe slug'ı oluşturma fonksiyonu
export function generateDistrictSlug(districtName: string): string {
  return districtName
    .toLowerCase()
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

// Slug'dan şehir bulma fonksiyonu
export function getCityBySlug(slug: string) {
  return CITIES.find(city => generateCitySlug(city.name) === slug);
}

// Şehir ID'sinden şehir bulma fonksiyonu
export function getCityById(cityId: string | number) {
  const id = typeof cityId === 'string' ? parseInt(cityId) : cityId;
  return CITIES.find(city => city.id === id);
}

// Şehir slug'ından ID bulma fonksiyonu
export function getCityIdBySlug(slug: string): string | null {
  const city = getCityBySlug(slug);
  return city ? city.id.toString() : null;
}

// Şehir ID'sinden slug bulma fonksiyonu
export function getCitySlugById(cityId: string | number): string | null {
  const city = getCityById(cityId);
  return city ? generateCitySlug(city.name) : null;
}

// Şehre ait ilçeleri getirme fonksiyonu
export function getDistrictsByCity(cityId: string | number) {
  const city = getCityById(cityId);
  return city ? city.districts : [];
}

// Şehir ve ilçe kombinasyonunu doğrulama fonksiyonu
export function validateCityDistrict(
  citySlug: string,
  districtSlug?: string
): {
  isValid: boolean;
  city?: (typeof CITIES)[0];
  district?: string;
  cityId?: string;
} {
  const city = getCityBySlug(citySlug);

  if (!city) {
    return { isValid: false };
  }

  if (!districtSlug) {
    return {
      isValid: true,
      city,
      cityId: city.id.toString(),
    };
  }

  // İlçe slug'ını normal isme çevir
  const district = city.districts.find(
    d => generateDistrictSlug(d) === districtSlug
  );

  if (!district) {
    return { isValid: false };
  }

  return {
    isValid: true,
    city,
    district,
    cityId: city.id.toString(),
  };
}

// Tüm şehirler için slug mapping'i oluşturma
export function createCitySlugMap() {
  const slugToCity: Record<string, (typeof CITIES)[0]> = {};
  const cityToSlug: Record<string, string> = {};

  CITIES.forEach(city => {
    const slug = generateCitySlug(city.name);
    slugToCity[slug] = city;
    cityToSlug[city.id.toString()] = slug;
  });

  return { slugToCity, cityToSlug };
}

// Şehir ve ilçe için breadcrumb verisi oluşturma
export function generateBreadcrumbs(citySlug: string, districtSlug?: string) {
  const validation = validateCityDistrict(citySlug, districtSlug);

  if (!validation.isValid || !validation.city) {
    return [];
  }

  const breadcrumbs = [
    { name: 'Ana Sayfa', href: '/' },
    { name: 'Spor Salonu Bul', href: '/findGym' },
    {
      name: validation.city.name,
      href: `/findGym/${citySlug}`,
    },
  ];

  if (validation.district) {
    breadcrumbs.push({
      name: validation.district,
      href: `/findGym/${citySlug}/${districtSlug}`,
    });
  }

  return breadcrumbs;
}
