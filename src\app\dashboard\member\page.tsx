import { MemberStats } from './invitations/components/memberStats';
import { MemberMemberships } from './invitations/components/memberMemberships';
import {
  getMemberDashboardStats,
  getMemberMemberships,
} from '@/lib/actions/all-actions';
import { DashboardHeader } from '@/components/dashboard/shared/dashboard-header';

export default async function MemberDashboardPage() {

  const [statsResult, membershipsResult] = await Promise.all([
    getMemberDashboardStats(),
    getMemberMemberships(),
  ]);

  const stats = statsResult.success ? statsResult.data : null;
  const memberships = membershipsResult.success
    ? membershipsResult.data || []
    : [];

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <DashboardHeader mode="member" />

      {/* Stats Section */}
      {stats && (
        <section>
          <MemberStats stats={stats} />
        </section>
      )}

      {/* Memberships Section */}
      <section>
        <MemberMemberships memberships={memberships} />
      </section>
    </div>
  );
}
