import { Metadata } from 'next';
import { AuthBrandPanel } from '@/components/auth/auth-brand-panel';

export const metadata: Metadata = {
  title: '<PERSON>iri<PERSON> | Sportiva',
  description: 'Sportiva spor salonu yönetim sistemi giriş sayfası.',
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col lg:flex-row">
      {/* Mobile Header - Brand (shown on mobile only) */}
      <div className="from-primary to-secondary flex items-center justify-center bg-gradient-to-r p-6 lg:hidden">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
            <span className="text-sm font-bold text-white">S</span>
          </div>
          <h1 className="text-xl font-bold text-white">SPORTIVA</h1>
        </div>
      </div>

      {/* Left Panel - Brand (hidden on mobile, shown on desktop) */}
      <div className="hidden w-1/2 lg:flex">
        <AuthBrandPanel />
      </div>

      {/* Right Panel - Form */}
      <div className="bg-background flex w-1/2 items-center justify-center p-4 lg:p-10">
        <div className="w-full max-w-md">{children}</div>
      </div>
    </div>
  );
}
