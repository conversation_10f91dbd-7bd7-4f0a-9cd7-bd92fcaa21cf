import { ArrowLeft, Check } from 'lucide-react';
import Link from 'next/link';
import { ThemeToggle } from '@/components/ui/theme-toggle';

export function AuthBrandPanel() {
  return (
    <div className="from-primary via-primary/90 to-secondary relative flex h-full flex-1 flex-col justify-center overflow-hidden bg-gradient-to-br p-8 lg:w-1/2 lg:p-12">
      {/* Background Pattern */}
      <div
        className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grid' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 20 0 L 0 0 0 20' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E")`,
        }}
      />

      {/* Back to Home Link */}
      <Link
        href="/"
        className="absolute left-6 top-6 inline-flex items-center gap-2 rounded-lg border border-white/20 bg-white/10 px-3 py-2 text-sm text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/20"
      >
        <ArrowLeft className="h-4 w-4" />
        Ana Sayfa
      </Link>

      {/* Theme Toggle */}
      <ThemeToggle variant="auth" />

      {/* Brand Content */}
      <div className="relative z-10 max-w-md text-white">
        {/* Logo */}
        <div className="mb-8 flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
            <span className="text-lg font-bold text-white">S</span>
          </div>
          <h1 className="text-2xl font-bold text-white">SPORTIVA</h1>
        </div>

        {/* Main Title */}
        <h2 className="mb-6 text-3xl font-bold leading-tight text-white lg:text-4xl">
          Spor salonunuzu dijitalleştirin
        </h2>

        {/* Description */}
        <p className="mb-8 text-lg leading-relaxed text-white/80">
          Modern yönetim sistemi ile üyelerinizi, antrenörlerinizi ve
          işlemlerinizi kolayca yönetin. Sportiva ile spor salonunuz artık daha
          verimli!
        </p>

        {/* Features List */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="flex h-5 w-5 items-center justify-center rounded-full bg-green-500">
              <Check className="h-3 w-3 text-white" />
            </div>
            <span className="text-white/90">Sınırsız üye yönetimi</span>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex h-5 w-5 items-center justify-center rounded-full bg-green-500">
              <Check className="h-3 w-3 text-white" />
            </div>
            <span className="text-white/90">Antrenör ve program takibi</span>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex h-5 w-5 items-center justify-center rounded-full bg-green-500">
              <Check className="h-3 w-3 text-white" />
            </div>
            <span className="text-white/90">Gelişmiş raporlama sistemi</span>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute -left-20 -top-20 h-40 w-40 rounded-full bg-blue-500/10 blur-3xl"></div>
      <div className="absolute -bottom-20 -right-20 h-40 w-40 rounded-full bg-purple-500/10 blur-3xl"></div>
    </div>
  );
}
