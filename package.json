{"name": "sportiva", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "tbuild": "next build --turbopack", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint --max-warnings 0", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write .", "format:check": "prettier --check .", "code-quality": "pnpm run type-check && pnpm run lint:strict && pnpm run format:check", "pre-commit": "pnpm run code-quality", "analyze": "ANALYZE=true pnpm run build", "build:analyze": "ANALYZE=true pnpm run build", "clean": "rm -rf .next dist build coverage", "clean:deps": "rm -rf node_modules pnpm-lock.yaml && pnpm install", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-brands-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "babel-plugin-react-compiler": "19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.12.1", "input-otp": "^1.4.2", "jest": "^30.0.4", "lucide-react": "^0.525.0", "next": "15.4.2-canary.53", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^3.1.0", "shadcn": "^2.5.0", "sharp": "^0.34.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "twilio": "^5.7.3", "vaul": "^1.1.2", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.2", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@types/node": "^24.1.0", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.32.0", "eslint-config-next": "^15.4.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "jsdom": "^26.1.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}