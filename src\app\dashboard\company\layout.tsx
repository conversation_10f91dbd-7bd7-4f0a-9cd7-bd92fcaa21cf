import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';

export const metadata: Metadata = {
  title: 'Şirket Paneli | Sportiva',
  description: 'Sportiva spor salonu yönetim sistemi şirket paneli.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function ManagerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Company Sidebar */}
      <DashboardSidebar mode='company_manager' />
      {/* Main Content Area */}
      {children}
    </>
  );
}
