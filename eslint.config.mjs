import { FlatCompat } from '@eslint/eslintrc';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  {
    ignores: [
      '.next/**/*',
      'node_modules/**/*',
      'dist/**/*',
      'build/**/*',
      '*.config.js',
      '*.config.ts',
      'coverage/**/*',
    ],
  },
  ...compat.extends('next/core-web-vitals'),
  {
    rules: {
      // SOLID prensipleri için kurallar
      //'max-lines-per-function': ['warn', { max: 200 }],
      //'max-params': ['warn', { max: 5 }],
      //complexity: ['warn', { max: 20 }],
      //'max-depth': ['warn', { max: 5 }],
      //'max-lines': ['warn', { max: 500 }],

      // Clean code kuralları
      'prefer-const': 'error',
      'no-var': 'error',
      'no-console': ['warn', { allow: ['warn', 'error'] }],
      'no-debugger': 'error',
      'no-alert': 'error',

      // React hooks kuralı - dependency array'leri kontrol eder
      'react-hooks/exhaustive-deps': 'warn',
    },
  },
];

export default eslintConfig;
