/**
 * Member dashboard types
 *
 * Extracted from member dashboard components
 */

/**
 * Member dashboard overview data
 */
export interface MemberDashboardOverview {
  activeMemberships: number;
  upcomingAppointments: number;
  totalWorkouts: number;
  memberSince: string;
  recentActivities: {
    id: string;
    type: 'workout' | 'appointment' | 'payment' | 'achievement';
    description: string;
    timestamp: string;
    gymName?: string;
  }[];
}

/**
 * Member membership data
 */
export interface MemberMembershipData {
  id: string;
  gymId: string;
  gymName: string;
  gymAddress: string;
  packageName: string;
  status: 'active' | 'passive';
  startDate: string;
  endDate?: string;
  remainingDays?: number;
  features: string[];
  canRenew: boolean;
}

/**
 * Member appointment data
 */
export interface MemberAppointmentData {
  id: string;
  trainerName: string;
  trainerId: string;
  gymName: string;
  date: string;
  startTime: string;
  endTime: string;
  type: 'personal' | 'group';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  canCancel: boolean;
  canReschedule: boolean;
}

/**
 * Member workout history
 */
export interface MemberWorkoutHistory {
  id: string;
  date: string;
  gymName: string;
  duration: number; // in minutes
  exercises: {
    name: string;
    sets: number;
    reps: number;
    weight?: number;
  }[];
  notes?: string;
  trainerId?: string;
  trainerName?: string;
}

/**
 * Member fitness goals
 */
export interface MemberFitnessGoals {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  status: 'active' | 'completed' | 'paused';
  progress: number; // percentage
  milestones: {
    id: string;
    title: string;
    isCompleted: boolean;
    completedAt?: string;
  }[];
}

/**
 * Member progress tracking
 */
export interface MemberProgress {
  weight: {
    current: number;
    target: number;
    history: {
      date: string;
      value: number;
    }[];
  };
  bodyFat?: {
    current: number;
    target: number;
    history: {
      date: string;
      value: number;
    }[];
  };
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    arms?: number;
    thighs?: number;
  };
  photos: {
    date: string;
    url: string;
    type: 'front' | 'side' | 'back';
  }[];
}
