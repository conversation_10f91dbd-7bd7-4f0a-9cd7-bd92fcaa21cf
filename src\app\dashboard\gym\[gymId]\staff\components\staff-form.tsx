'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { toast } from 'sonner';
import { createStaffSchema } from '@/lib/actions/core/schemas';
import { Staff } from '@/types/staff';
import { createStaff, updateStaff } from '@/lib/actions/all-actions';

interface StaffFormProps {
  gymId: string;
  initialData?: Staff | null;
  onSuccess: () => void;
  onCancel: () => void;
}

// Form schema and types
type FormData = z.infer<typeof createStaffSchema>;

export function StaffForm({
  gymId,
  initialData,
  onSuccess,
  onCancel,
}: StaffFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!initialData;

  const form = useForm<FormData>({
    resolver: zodResolver(createStaffSchema),
    defaultValues: {
      name: initialData?.name || '',
      surname: initialData?.surname || '',
      hire_date:
        initialData?.hire_date || new Date().toISOString().split('T')[0],
      salary_amount: initialData?.salary_amount || undefined,
      staff_type:
        (initialData?.staff_type as
          | 'cleaner'
          | 'security'
          | 'reception'
          | 'maintenance'
          | 'other') || 'other',
    },
  });

  async function onSubmit(data: FormData) {
    try {
      setIsSubmitting(true);

      let response;
      if (isEditing && initialData) {
        response = await updateStaff(initialData.id, gymId, data);
      } else {
        response = await createStaff(gymId, data);
      }

      if (response.success) {
        onSuccess();
      } else {
        toast.error(response.error || 'İşlem başarısız oldu');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
      // Güvenlik için detaylı error bilgisi loglanmıyor
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Name Field */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Ad *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Personelin adı"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Surname Field */}
          <FormField
            control={form.control}
            name="surname"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Soyad *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Personelin soyadı"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Staff Type Field */}
          <FormField
            control={form.control}
            name="staff_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Personel Tipi *</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isSubmitting}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Personel tipi seçiniz" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="cleaner">Temizlikçi</SelectItem>
                    <SelectItem value="security">Güvenlik</SelectItem>
                    <SelectItem value="reception">Resepsiyon</SelectItem>
                    <SelectItem value="maintenance">Bakım</SelectItem>
                    <SelectItem value="other">Diğer</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Hire Date Field */}
          <FormField
            control={form.control}
            name="hire_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>İşe Başlama Tarihi *</FormLabel>
                <FormControl>
                  <Input type="date" {...field} disabled={isSubmitting} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Salary Field */}
          <FormField
            control={form.control}
            name="salary_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maaş (TL)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    max="999999.99"
                    {...field}
                    value={field.value || ''}
                    onChange={e => {
                      const value = e.target.value;
                      field.onChange(
                        value === '' ? undefined : parseFloat(value)
                      );
                    }}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            İptal
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? isEditing
                ? 'Güncelleniyor...'
                : 'Ekleniyor...'
              : isEditing
                ? 'Güncelle'
                : 'Ekle'}
          </Button>
        </div>

        {/* Required Fields Note */}
        <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          * işaretli alanlar zorunludur
        </p>
      </form>
    </Form>
  );
}
