import { Check, X } from "lucide-react";
import type { PlatformPackages } from "@/types/database/tables";

type PricingComparisonProps = {
  packages: PlatformPackages[];
};

export function PricingComparison({ packages }: PricingComparisonProps) {
  // feature parsing similar to PricingModernClient
  const formatFeatures = (features: unknown): string[] => {
    if (Array.isArray(features)) {
      return features.filter((item): item is string => typeof item === "string");
    }
    if (typeof features === "string") {
      try {
        const parsed = JSON.parse(features);
        return formatFeatures(parsed);
      } catch {
        return [];
      }
    }
    if (typeof features === "object" && features !== null) {
      const obj = features as Record<string, unknown>;
      if (Array.isArray(obj.perks)) {
        return (obj.perks as unknown[]).filter((v): v is string => typeof v === "string");
      }
      const directStrings = Object.values(obj).filter((v): v is string => typeof v === "string");
      if (directStrings.length) return directStrings;
      const nested = Object.values(obj)
        .flatMap((v) => (Array.isArray(v) ? v : []))
        .filter((v): v is string => typeof v === "string");
      return nested;
    }
    return [];
  };

  // feature text prettifier for better readability
  const prettifyFeature = (text: string): string => {
    try {
      let t = (text || "").toString().trim();
      if (!t) return t;
      t = t.replace(/^Maks\s/i, "Maksimum ");
      t = t.replace(/\bmax\b/i, "Maksimum");
      t = t.replace(/uye/gi, "üye");
      t = t.charAt(0).toUpperCase() + t.slice(1);
      return t;
    } catch {
      return text as string;
    }
  };

  // prefer yearly, fallback lifetime (duration null) per tier
  const pickDisplayPackage = (tier: string): PlatformPackages | undefined => {
    const byTier = packages.filter((p) => p.tier === tier);
    const yearly = byTier.find((p) => p.duration === "yearly");
    const lifetime = byTier.find((p) => !p.duration || p.duration === "lifetime");
    // For free tier, prefer lifetime to show "ömür boyu"
    if (tier === "free") return lifetime ?? yearly ?? byTier[0];
    return yearly ?? lifetime ?? byTier[0];
  };

  const starter = pickDisplayPackage("starter");
  const pro = pickDisplayPackage("professional");
  const free = pickDisplayPackage("free");

  const tl = (n: number) => n.toLocaleString("tr-TR");

  const starterFeatures = formatFeatures(starter?.features ?? []);
  const proFeatures = formatFeatures(pro?.features ?? []);
  const freeFeatures = formatFeatures(free?.features ?? []);
  const allFeatures = Array.from(new Set([
    ...starterFeatures,
    ...proFeatures,
    ...freeFeatures,
  ])).sort((a, b) => a.localeCompare(b, "tr"));

  const priceDisplay = (pkg?: PlatformPackages) => {
    if (!pkg) return { price: "-", suffix: "" };
    const price = typeof (pkg as any).price === "number" ? (pkg as any).price : Number((pkg as any).price);
    const suffix = pkg.duration === "yearly" ? "/yıl" : "ömür boyu";
    return { price: `₺${tl(price)}`, suffix };
  };

  const starterPrice = priceDisplay(starter);
  const proPrice = priceDisplay(pro);
  const freePrice = priceDisplay(free);

  return (
    <section className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">Plan Karşılaştırması</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Hangi planın size uygun olduğunu görmek için detaylı özellik karşılaştırması
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-background rounded-xl border border-border overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left p-6 font-semibold text-foreground">Özellikler</th>
                    {/* Free - left */}
                    <th className="text-center p-6 font-semibold text-foreground">
                      <div className="space-y-2">
                        <div className="text-lg">{free?.name ?? "Free"}</div>
                        <div className="text-2xl font-bold text-primary">{freePrice.price}</div>
                        <div className="text-sm text-muted-foreground">{freePrice.suffix}</div>
                      </div>
                    </th>
                    {/* Starter - middle (emphasized) */}
                    <th className="text-center p-6 font-semibold text-foreground bg-primary/5">
                      <div className="space-y-2">
                        <div className="text-lg flex items-center justify-center gap-2">
                          {starter?.name ?? "Starter"}
                          <span className="rounded-full bg-primary/10 px-2 py-0.5 text-[10px] font-medium text-primary">En Popüler</span>
                        </div>
                        <div className="text-2xl font-bold text-primary">{starterPrice.price}</div>
                        <div className="text-sm text-muted-foreground">{starterPrice.suffix}</div>
                      </div>
                    </th>
                    {/* Professional - right */}
                    <th className="text-center p-6 font-semibold text-foreground">
                      <div className="space-y-2">
                        <div className="text-lg">{pro?.name ?? "Pro"}</div>
                        <div className="text-2xl font-bold text-primary">{proPrice.price}</div>
                        <div className="text-sm text-muted-foreground">{proPrice.suffix}</div>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {allFeatures.length === 0 ? (
                    <tr>
                      <td colSpan={4} className="p-6 text-center text-muted-foreground">
                        Görüntülenecek özellik bulunamadı.
                      </td>
                    </tr>
                  ) : (
                    allFeatures.map((feature, idx) => {
                      const hasStarter = starterFeatures.includes(feature);
                      const hasPro = proFeatures.includes(feature);
                      const hasFree = freeFeatures.includes(feature);
                      return (
                        <tr key={idx} className="border-b border-border hover:bg-muted/20 transition-colors">
                          <td className="p-4 text-foreground">{prettifyFeature(feature)}</td>
                          {/* Free - left */}
                          <td className="p-4 text-center">
                            {hasFree ? (
                              <Check className="h-5 w-5 text-primary mx-auto" />
                            ) : (
                              <X className="h-5 w-5 text-muted-foreground mx-auto" />
                            )}
                          </td>
                          {/* Starter - middle (emphasized) */}
                          <td className="p-4 text-center bg-primary/5">
                            {hasStarter ? (
                              <Check className="h-5 w-5 text-primary mx-auto" />
                            ) : (
                              <X className="h-5 w-5 text-muted-foreground mx-auto" />
                            )}
                          </td>
                          {/* Professional - right */}
                          <td className="p-4 text-center">
                            {hasPro ? (
                              <Check className="h-5 w-5 text-primary mx-auto" />
                            ) : (
                              <X className="h-5 w-5 text-muted-foreground mx-auto" />
                            )}
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Note */}
          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              * Tüm planlar 14 gün ücretsiz deneme ile başlar. İstediğiniz zaman iptal edebilirsiniz.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
