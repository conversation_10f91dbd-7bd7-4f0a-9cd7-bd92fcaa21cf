import Link from 'next/link';
import {
  Home,
  LayoutDashboard,
  <PERSON><PERSON><PERSON>,
  ArrowRight,
  MapPin,
  Users,
  Calendar,
  TrendingUp,
  Zap,
} from 'lucide-react';

// Floating fitness icons animation component
function FloatingIcon({
  icon: Icon,
  delay = 0,
  duration = 3000,
  className = '',
}: {
  icon: any;
  delay?: number;
  duration?: number;
  className?: string;
}) {
  return (
    <div
      className={`animate-float absolute opacity-10 ${className}`}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`,
      }}
    >
      <Icon className="text-primary h-8 w-8" />
    </div>
  );
}

// Glitch effect component for 404 text
function GlitchText({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative inline-block h-full">
      <span className="from-primary via-primary/80 to-primary relative z-10 bg-gradient-to-r bg-clip-text text-transparent">
        {children}
      </span>
      <span
        className="animate-glitch-1 absolute top-0 left-0 text-red-500/30"
        aria-hidden="true"
      >
        {children}
      </span>
      <span
        className="animate-glitch-2 absolute top-0 left-0 text-blue-500/30"
        aria-hidden="true"
      >
        {children}
      </span>
    </div>
  );
}

export default function NotFound() {
  const quickLinks = [
    {
      href: '/',
      label: 'Ana Sayfa',
      icon: Home,
    },
    {
      href: '/dashboard',
      label: 'Panel',
      icon: LayoutDashboard,
    },
  ];

  return (
    <div className="from-background via-background to-muted/30 relative my-auto overflow-hidden bg-gradient-to-br">
      {/* Animated background elements */}
      <div className="pointer-events-none absolute inset-0">
        <FloatingIcon icon={Dumbbell} delay={0} className="top-20 left-10" />
        <FloatingIcon icon={Users} delay={500} className="top-32 right-20" />
        <FloatingIcon
          icon={Calendar}
          delay={1000}
          className="bottom-40 left-20"
        />
        <FloatingIcon
          icon={TrendingUp}
          delay={1500}
          className="right-10 bottom-20"
        />
        <FloatingIcon icon={Zap} delay={2000} className="top-1/2 left-1/4" />
        <FloatingIcon
          icon={MapPin}
          delay={2500}
          className="top-1/3 right-1/3"
        />
      </div>

      {/* Grid pattern overlay */}
      <div className="bg-grid-pattern pointer-events-none absolute inset-0 opacity-5" />

      <div className="relative z-10 flex flex-col items-center justify-center px-4 text-center">
        {/* Main 404 Section */}
        <div
          className={`translate-y-0 opacity-100 transition-all duration-1000 ease-out`}
        >
          {/* 404 Number with glitch effect */}
          <div className="mb-8">
            <h1 className="text-8xl leading-none font-black md:text-9xl">
              <GlitchText>404</GlitchText>
            </h1>
            <div className="via-primary mx-auto mt-2 h-1 w-32 animate-pulse rounded-full bg-gradient-to-r from-transparent to-transparent" />
          </div>

          {/* Error message */}
          <div className="mb-8 space-y-4">
            <h2 className="text-foreground text-2xl font-bold md:text-3xl">
              Sayfa Bulunamadı
            </h2>
            <p className="text-muted-foreground mx-auto max-w-md text-lg leading-relaxed">
              Aradığınız sayfa mevcut değil, taşınmış veya geçici olarak
              erişilemez durumda olabilir.
            </p>
          </div>
        </div>

        {/* Quick Navigation Links */}
        <div
          className={`mb-12 translate-y-0 opacity-100 transition-all delay-500 duration-1000`}
        >
          <h3 className="text-foreground mb-6 text-lg font-semibold">
            Hızlı Erişim
          </h3>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            {quickLinks.map((link, index) => (
              <Link
                key={link.href}
                href={link.href}
                className={`border-border bg-card/50 hover:bg-card/80 hover:border-primary/50 hover:shadow-primary/10 animate-fade-in-up group rounded-lg border px-6 py-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                style={{ animationDelay: `${600 + index * 100}ms` }}
              >
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 group-hover:bg-primary/20 rounded-lg p-2 transition-colors">
                    <link.icon className="text-primary h-5 w-5" />
                  </div>
                  <h4 className="text-foreground group-hover:text-primary font-semibold transition-colors">
                    {link.label}
                  </h4>
                  <ArrowRight className="text-muted-foreground group-hover:text-primary h-4 w-4 transition-all group-hover:translate-x-1" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
