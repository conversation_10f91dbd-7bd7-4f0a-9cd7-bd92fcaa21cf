/**
 * FindGym module constants and shared configurations
 */

// ISR Configuration

// Search Configuration
export const SEARCH_DEBOUNCE_TIME = 300; // ms
export const URL_UPDATE_DEBOUNCE_TIME = 800; // ms

// Pagination
export const DEFAULT_GYMS_PER_PAGE = 12;
export const MAX_GYMS_PER_PAGE = 50;

// Feature Display
export const MAX_FEATURES_DISPLAY = 3;

// Review Configuration
export const DEFAULT_REVIEWS_PER_PAGE = 10;
export const MAX_REVIEWS_PER_PAGE = 50;

// Cache Keys
export const CACHE_KEYS = {
  ALL_GYMS: 'all-gyms',
  CITY_GYMS: (cityId: string) => `city-gyms-${cityId}`,
  DISTRICT_GYMS: (cityId: string, district: string) => `district-gyms-${cityId}-${district}`,
} as const;

// Default Filter Values
export const DEFAULT_FILTERS = {
  searchQuery: '',
  selectedCity: 'all',
  selectedDistrict: 'all',
  selectedFeatures: [],
} as const;
