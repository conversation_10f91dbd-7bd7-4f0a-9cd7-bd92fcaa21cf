// TypeScript Interface Definitions for Invitation System

export interface UserSearchResult {
  id: string;
  full_name: string;
  avatar_url: string | null;
}

export interface IncomingInvitation {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
  profile: {
    id: string;
    full_name: string;
  };
  role: 'member' | 'trainer';
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

export interface OutgoingInvitation {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
  role: 'member' | 'trainer';
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

// Gym Manager Invitation Interfaces
export interface GymIncomingInvitation {
  id: string;
  profile: {
    id: string;
    full_name: string;
  };
  role: 'member' | 'trainer';
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

export interface GymOutgoingInvitation {
  id: string;
  profile: {
    id: string;
    full_name: string;
  };
  role: 'member' | 'trainer';
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

export interface GymInvitation {
  id: string;
  gym_id: string;
  profile_id: string;
  type: 'join_request' | 'gym_invite';
  role: 'member' | 'trainer';
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  message: string | null;
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
  updated_at: string;
}

// Trainer invitation interfaces for gym manager (deprecated - use GymIncomingInvitation/GymOutgoingInvitation with role='trainer')
export interface GymIncomingTrainerInvitation {
  id: string;
  profile: {
    id: string;
    full_name: string;
  };
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

export interface GymOutgoingTrainerInvitation {
  id: string;
  profile: {
    id: string;
    full_name: string;
  };
  message: string | null;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

// Combined interface for all gym invitations (simplified - use incoming/outgoing with role filter)
export interface GymAllInvitations {
  incoming: GymIncomingInvitation[];
  outgoing: GymOutgoingInvitation[];
}

// Common invitation status type
export type InvitationStatus = 'pending' | 'accepted' | 'rejected' | 'expired';

// Invitation type enum
export type InvitationType =
  | 'join_request'
  | 'gym_invite';

export type InvitationRole =
  | 'member'
  | 'trainer';

// Notification direction type
export type NotificationDirection = 'to_gym' | 'to_member' | 'to_trainer';

// Notification action type
export type NotificationAction = 'request' | 'accept' | 'reject' | 'invite' | 'cancel';

// Merkezi davet sistemi için configuration types
export interface InvitationConfig {
  type: InvitationType;
  role: InvitationRole;
  notificationDirection: NotificationDirection;
  requiresManagerAuth: boolean;
  requiresTrainerAuth: boolean;
  defaultMessage?: string;
  expirationDays?: number;
}

// Merkezi CRUD operasyon parametreleri
export interface InvitationCreateParams {
  gymId: string;
  profileId: string;
  type: InvitationType;
  role: InvitationRole;
  message?: string;
  expirationDays?: number;
}

export interface InvitationUpdateParams {
  invitationId: string;
  status: InvitationStatus;
  respondedAt?: string;
}

export interface InvitationQueryParams {
  gymId?: string;
  profileId?: string;
  type?: InvitationType;
  role?: InvitationRole;
  status?: InvitationStatus;
  direction?: 'incoming' | 'outgoing';
}

// Davet türü konfigürasyonları
export const INVITATION_CONFIGS: Record<string, InvitationConfig> = {
  'join_request_member': {
    type: 'join_request',
    role: 'member',
    notificationDirection: 'to_gym',
    requiresManagerAuth: false,
    requiresTrainerAuth: false,
    defaultMessage: 'Salonunuza üye olmak istiyorum.',
    expirationDays: 7,
  },
  'join_request_trainer': {
    type: 'join_request',
    role: 'trainer',
    notificationDirection: 'to_gym',
    requiresManagerAuth: false,
    requiresTrainerAuth: false,
    defaultMessage: 'Salonunuzda antrenör olarak çalışmak istiyorum.',
    expirationDays: 7,
  },
  'gym_invite_member': {
    type: 'gym_invite',
    role: 'member',
    notificationDirection: 'to_member',
    requiresManagerAuth: true,
    requiresTrainerAuth: false,
    defaultMessage: 'Salonumuza üye olmaya davetlisiniz.',
    expirationDays: 7,
  },
  'gym_invite_trainer': {
    type: 'gym_invite',
    role: 'trainer',
    notificationDirection: 'to_trainer',
    requiresManagerAuth: true,
    requiresTrainerAuth: false,
    defaultMessage: 'Salonumuzda antrenör olarak çalışmaya davetlisiniz.',
    expirationDays: 7,
  },
};
