'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { getDashboardPath } from '@/lib/actions/auth';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

export default function DashboardRedirectPage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(true);

  const handleRedirect = useCallback(async () => {
    setIsRedirecting(true);
    setError(null);
    try {
      const path = await getDashboardPath();
      router.push(path);
    } catch (err) {
      setError('Yönlendirme sırasında bir hata oluştu. Lütfen tekrar deneyin.');
      setIsRedirecting(false);
    }
  }, [router]);

  useEffect(() => {
    handleRedirect();
  }, [handleRedirect]);

  return (
    <div className="flex flex-col items-center justify-center bg-background">
      <div className="text-center p-8 border rounded-lg shadow-lg max-w-sm w-full">
        {isRedirecting && (
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-lg font-semibold">Yönlendiriliyorsunuz...</p>
            <p className="text-sm text-muted-foreground">Lütfen bekleyin, sizin için en uygun sayfa hazırlanıyor.</p>
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center gap-4">
            <p className="text-destructive font-semibold">Hata!</p>
            <p className="text-sm text-muted-foreground">{error}</p>
            <Button onClick={handleRedirect} className="mt-4">
              Tekrar Dene
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
