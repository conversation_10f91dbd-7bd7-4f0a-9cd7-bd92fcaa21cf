import { Card, CardContent, CardHeader } from '@/components/ui/card';

// Header Skeleton Component
export function HeaderSkeleton() {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <div className="bg-muted/70 dark:bg-muted/50 mb-2 h-9 w-80 animate-pulse rounded" />
        <div className="bg-muted/70 dark:bg-muted/50 h-5 w-96 animate-pulse rounded" />
      </div>
      <div className="bg-muted/70 dark:bg-muted/50 h-10 w-32 animate-pulse rounded" />
    </div>
  );
}

// Invitations Loading Skeleton Component
export function InvitationsLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Tabs Skeleton */}
      <div className="bg-muted/70 dark:bg-muted/50 flex space-x-1 rounded-lg p-1">
        <div className="bg-muted/70 dark:bg-muted/50 h-10 w-32 animate-pulse rounded" />
        <div className="bg-muted/70 dark:bg-muted/50 h-10 w-32 animate-pulse rounded" />
      </div>

      {/* Cards Skeleton */}
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="border-gray-200 dark:border-gray-700">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="bg-muted/70 dark:bg-muted/50 h-6 w-40 animate-pulse rounded" />
                  <div className="bg-muted/70 dark:bg-muted/50 h-4 w-24 animate-pulse rounded" />
                </div>
                <div className="bg-muted/70 dark:bg-muted/50 h-6 w-20 animate-pulse rounded" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="bg-muted/70 dark:bg-muted/50 h-4 w-full animate-pulse rounded" />
                <div className="bg-muted/70 dark:bg-muted/50 h-4 w-32 animate-pulse rounded" />
              </div>
              <div className="flex gap-2">
                <div className="bg-muted/70 dark:bg-muted/50 h-9 w-24 animate-pulse rounded" />
                <div className="bg-muted/70 dark:bg-muted/50 h-9 w-24 animate-pulse rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
