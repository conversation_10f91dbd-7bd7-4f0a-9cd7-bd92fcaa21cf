/**
 * Main types export file
 *
 * Re-exports all types from various modules for easy access
 */

// Database types
export * from './database/tables';
export * from './database/enums';
export * from './database/relations';
export type { Database } from './database/generated';

// Business domain types
export * from './business/gym';
export * from './business/invitation';
export * from './business/membership';
export * from './business/payment';
export * from './business/user';

// Feature types
export * from './features/auth';
export * from './features/dashboard';
/**
 * Auth feature types
 *
 * Re-exports all auth-related types
 */

// Global types
export * from './global/api';
export * from './global/common';
export * from './global/navigation';
export * from './global/ui';

// Utility types
export * from './utils/constants';
export * from './utils/date';
export * from './utils/form';

// Staff types
export * from './staff';
