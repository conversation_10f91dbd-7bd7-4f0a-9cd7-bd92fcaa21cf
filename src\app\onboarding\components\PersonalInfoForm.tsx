'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import { User } from 'lucide-react';
import { INPUT_RULES } from '@/lib/utils/form-validation';

interface PersonalData {
  first_name: string;
  last_name: string;
}

interface PersonalInfoFormProps {
  data: PersonalData;
  onChange: (data: PersonalData) => void;
}

export function PersonalInfoForm({ data, onChange }: PersonalInfoFormProps) {
  const handleChange = (field: keyof PersonalData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="mr-2 h-5 w-5" />
          Kişisel Bilgiler
        </CardTitle>
        <CardDescription>
          Ad ve soyad bilgilerinizi girin
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="first_name">Ad *</Label>
            <EnhancedInput
              id="first_name"
              type="text"
              placeholder="Adınızı girin"
              value={data.first_name}
              onChange={(value) => handleChange('first_name', value)}
              formatter={INPUT_RULES.NAME.formatter}
              validator={INPUT_RULES.NAME.validator}
              maxLength={INPUT_RULES.NAME.maxLength}
              validationMessage="Ad en az 2 karakter olmalı ve sadece harf içermelidir"
              required
            />
          </div>
          <div>
            <Label htmlFor="last_name">Soyad *</Label>
            <EnhancedInput
              id="last_name"
              type="text"
              placeholder="Soyadınızı girin"
              value={data.last_name}
              onChange={(value) => handleChange('last_name', value)}
              formatter={INPUT_RULES.NAME.formatter}
              validator={INPUT_RULES.NAME.validator}
              maxLength={INPUT_RULES.NAME.maxLength}
              validationMessage="Soyad en az 2 karakter olmalı ve sadece harf içermelidir"
              required
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
