'use client';

import { useState, useTransition, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Award, User, FileText } from 'lucide-react';

import {
  TRAINER_SPECIALIZATION_OPTIONS,
  TRAINER_CERTIFICATION_LEVELS,
} from '@/lib/constants/trainer-constants';
import { TrainerDetails } from '@/types/database/tables';
import { updateTrainerDetails } from '@/lib/actions/user/trainer-actions';

interface TrainerInformationProps {
  trainerDetails: TrainerDetails | null;
}

export const TrainerInformation = ({
  trainerDetails,
}: TrainerInformationProps) => {
  const [detailsFormData, setDetailsFormData] = useState({
    specialization: trainerDetails?.specialization || '',
    certification_level: trainerDetails?.certification_level || '',
    experience_years: trainerDetails?.experience_years?.toString() || '',
    bio: trainerDetails?.bio || '',
  });

  // Loading states
  const [isDetailsPending, startDetailsTransition] = useTransition();

  // Message states
  const [detailsError, setDetailsError] = useState<string | undefined>();
  const [detailsSuccess, setDetailsSuccess] = useState<string | undefined>();

  const handleDetailsSubmit = async (formData: FormData) => {
    setDetailsError(undefined);
    setDetailsSuccess(undefined);

    startDetailsTransition(async () => {
      try {
        const result = await updateTrainerDetails(formData);

        if (result.success && result.data) {
          setDetailsSuccess('Antrenör bilgileri başarıyla güncellendi');
          setDetailsFormData({
            specialization: result.data.specialization || '',
            certification_level: result.data.certification_level || '',
            experience_years: result.data.experience_years?.toString() || '',
            bio: result.data.bio || '',
          });
        } else {
          setDetailsError(
            result.error || 'Antrenör bilgileri güncellenirken hata oluştu'
          );
        }
      } catch (err) {
        setDetailsError('Beklenmeyen bir hata oluştu');
        console.error('Trainer details update error:', err);
      }
    });
  };

  const handleDetailsInputChange = useCallback(
    (field: string, value: string) => {
      setDetailsFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    },
    []
  );

  return (
    <div className="space-y-8">
      {/* Trainer Code Display */}
      {trainerDetails?.invite_code && (
        <Card className="from-primary/5 bg-gradient-to-r to-transparent">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="flex items-center gap-2 text-lg font-semibold">
                  <User className="text-primary h-5 w-5" />
                  Antrenör Kodunuz
                </h3>
                <div className="flex items-center gap-4">
                  <Badge
                    variant="outline"
                    className="px-4 py-2 font-mono text-lg"
                  >
                    {trainerDetails.invite_code}
                  </Badge>
                </div>
              </div>
              <div className="text-muted-foreground text-right text-sm">
                <div>Bu kod ile salonlara</div>
                <div>davet edilebilirsiniz</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form action={handleDetailsSubmit} className="space-y-8">
        {/* Uzmanlık, Sertifika ve Deneyim */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Uzmanlık ve Sertifikalar
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="specialization" className="text-sm font-medium">
                  Uzmanlık Alanı
                </Label>
                <Select
                  name="specialization"
                  value={detailsFormData.specialization}
                  onValueChange={value =>
                    handleDetailsInputChange('specialization', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Uzmanlık alanınızı seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {TRAINER_SPECIALIZATION_OPTIONS.map(spec => (
                      <SelectItem key={spec.value} value={spec.value}>
                        {spec.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="certification_level"
                  className="text-sm font-medium"
                >
                  Sertifika Seviyesi
                </Label>
                <Select
                  name="certification_level"
                  value={detailsFormData.certification_level}
                  onValueChange={value =>
                    handleDetailsInputChange('certification_level', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sertifika seviyenizi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {TRAINER_CERTIFICATION_LEVELS.map(cert => (
                      <SelectItem key={cert.value} value={cert.value}>
                        {cert.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="experience_years"
                  className="text-sm font-medium"
                >
                  Deneyim (Yıl)
                </Label>
                <Input
                  id="experience_years"
                  name="experience_years"
                  type="number"
                  min="0"
                  max="50"
                  value={detailsFormData.experience_years}
                  onChange={e =>
                    handleDetailsInputChange('experience_years', e.target.value)
                  }
                  placeholder="Örn: 5"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Biyografi */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Biyografi
            </CardTitle>
            <CardDescription>
              Kendinizi tanıtın ve deneyimlerinizi paylaşın
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="bio" className="text-sm font-medium">
                Kişisel Tanıtım
              </Label>
              <Textarea
                id="bio"
                name="bio"
                value={detailsFormData.bio}
                onChange={e => handleDetailsInputChange('bio', e.target.value)}
                placeholder="Kendinizi tanıtın, deneyimlerinizi ve yaklaşımınızı paylaşın..."
                className="min-h-[120px] resize-none"
                maxLength={500}
              />
              <div className="flex items-center justify-between">
                <p className="text-muted-foreground text-xs">
                  Bu bilgi potansiyel üyeler tarafından görülebilir
                </p>
                <p className="text-muted-foreground text-xs">
                  {detailsFormData.bio.length}/500
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 sm:flex-row sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setDetailsFormData({
                specialization: trainerDetails?.specialization || '',
                certification_level: trainerDetails?.certification_level || '',
                experience_years:
                  trainerDetails?.experience_years?.toString() || '',
                bio: trainerDetails?.bio || '',
              });
              setDetailsError(undefined);
              setDetailsSuccess(undefined);
            }}
            disabled={isDetailsPending}
            className="sm:w-auto"
          >
            Değişiklikleri İptal Et
          </Button>
          <Button
            type="submit"
            disabled={isDetailsPending}
            className="min-w-[200px] sm:w-auto"
          >
            {isDetailsPending ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Kaydediliyor...
              </div>
            ) : (
              'Antrenör Bilgilerini Kaydet'
            )}
          </Button>
        </div>
        {/* Status Messages */}
        {detailsError && (
          <div className="bg-destructive/10 border-destructive/20 text-destructive flex items-center gap-2 rounded-lg border p-4 text-sm">
            <div className="bg-destructive h-2 w-2 rounded-full" />
            {detailsError}
          </div>
        )}
        {detailsSuccess && (
          <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            {detailsSuccess}
          </div>
        )}
      </form>
    </div>
  );
};
