"use client";

import { RouteError } from "@/components/errors/route-error";

export default function OnboardingError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Onboarding Hatası"
      description="Onboarding içeriği yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: "/onboarding" }}
    />
  );
}

