'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GymData } from '@/components/header/shared/header-types';
import { getCityNameById } from '@/lib/constants/cityConstants';

interface TrainerGymsClientProps {
  initialGyms: GymData[];
}

export function TrainerGymsClient({ initialGyms }: TrainerGymsClientProps) {
  const [gyms] = useState<GymData[]>(initialGyms);
  const router = useRouter();

  const getAddress = (gym: GymData) => {
    const parts: string[] = [];
    if (gym.city) parts.push(getCityNameById(gym.city) || gym.city);
    if (gym.district) parts.push(gym.district);
    return parts.join(', ');
  };

  if (gyms.length === 0) {
    return (
      <div className="py-12 text-center">
        <Building2 className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <h3 className="text-foreground mb-2 text-lg font-medium">
          Henüz hiçbir salonda çalışmıyorsunuz
        </h3>
        <p className="text-muted-foreground mb-6">
          Bir salona katılmak için salon yöneticisinden davet bekleyin veya
          salona katılım isteği gönderin.
        </p>
        <Button
          variant="outline"
          className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800"
        >
          <Plus className="mr-2 h-4 w-4" />
          Salon Ara
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="mb-4 text-xl font-semibold">Salonlar</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {gyms.map(gym => (
            <Card
              key={gym.id}
              className="cursor-pointer border-gray-200 bg-white transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800 dark:hover:shadow-lg"
              onClick={() => router.push(`/dashboard/gym/${gym.id}`)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <CardTitle className="text-lg">{gym.name}</CardTitle>
                  </div>
                </div>
                {(gym.city || gym.district) && (
                  <CardDescription className="flex items-center text-sm">
                    <MapPin className="mr-1 h-4 w-4" />
                    {getAddress(gym)}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent />
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
