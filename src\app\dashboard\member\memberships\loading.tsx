export default function MembershipsLoadingPage() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div className="bg-muted mb-2 h-9 w-80 animate-pulse rounded" />
          <div className="bg-muted h-5 w-96 animate-pulse rounded" />
        </div>
        <div className="flex gap-2">
          <div className="bg-muted h-10 w-32 animate-pulse rounded" />
          <div className="bg-muted h-10 w-32 animate-pulse rounded" />
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-card rounded-lg border p-6">
            <div className="mb-4 flex items-start justify-between">
              <div>
                <div className="bg-muted mb-2 h-6 w-48 animate-pulse rounded" />
                <div className="bg-muted h-4 w-32 animate-pulse rounded" />
              </div>
              <div className="bg-muted h-6 w-16 animate-pulse rounded" />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="bg-muted h-4 w-24 animate-pulse rounded" />
                <div className="bg-muted h-4 w-40 animate-pulse rounded" />
              </div>
              <div className="space-y-2">
                <div className="bg-muted h-4 w-24 animate-pulse rounded" />
                <div className="bg-muted h-4 w-32 animate-pulse rounded" />
              </div>
            </div>

            <div className="mt-4 flex gap-2">
              <div className="bg-muted h-9 w-24 animate-pulse rounded" />
              <div className="bg-muted h-9 w-32 animate-pulse rounded" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
