'use server';

import { createAction } from '../../core/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { ApiResponse } from '@/types/global/api';
import { ManagerOverview } from './dashboard-types';
import { getManagerFinancialSummary } from './financial-summary';
import { getManagerGyms } from './dashboard-actions';

/**
 * Manager'ın genel bakış verilerini getirir
 * Following Clean Code principles - orchestrates overview data collection
 */
export async function getManagerOverview(): Promise<
  ApiResponse<ManagerOverview>
> {
  return await createAction<ManagerOverview>(
    async (_, supabase, authUserId) => {
      // Manager'ın salonlarını getir
      const gymsResponse = await getManagerGyms();

      if (
        !gymsResponse.success ||
        !gymsResponse.data ||
        gymsResponse.data.length === 0
      ) {
        return getEmptyOverview();
      }

      const gyms = gymsResponse.data;
      const totalGyms = gyms.length;
      const gymIds = gyms.map(gym => gym.id);

      // Paralel veri çekme
      const [
        totalMembers,
        financialSummary,
        unreadNotifications,
        topPerformingGym,
      ] = await Promise.all([
        getTotalMembers(supabase, gymIds),
        getFinancialData(),
        getUnreadNotifications(supabase, authUserId),
        getTopPerformingGym(supabase),
      ]);

      return {
        totalGyms,
        totalMembers,
        totalRevenue: financialSummary.totalRevenue,
        monthlyGrowth: financialSummary.monthlyGrowth,
        unreadNotifications,
        pendingActions: 0, // Bu değer ihtiyaca göre hesaplanabilir
        topPerformingGym,
      };
    }
  );
}

/**
 * Toplam üye sayısını getirir
 */
async function getTotalMembers(
  supabase: SupabaseClient,
  gymIds: string[]
): Promise<number> {
  const { count, error } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .in('gym_id', gymIds)

  if (error) {
    throw new Error(`Üye sayısı alınırken hata: ${error.message}`);
  }

  return count || 0;
}

/**
 * Finansal verileri getirir
 */
async function getFinancialData(): Promise<{
  totalRevenue: number;
  monthlyGrowth: number;
}> {
  const financialResult = await getManagerFinancialSummary();

  return {
    totalRevenue: financialResult.success
      ? financialResult.data?.totalRevenue || 0
      : 0,
    monthlyGrowth: financialResult.success
      ? financialResult.data?.revenueGrowth || 0
      : 0,
  };
}

/**
 * Okunmamış bildirim sayısını getirir
 */
async function getUnreadNotifications(
  supabase: SupabaseClient,
  userId: string
): Promise<number> {
  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('profile_id', userId)
    .eq('is_read', false);

  if (error) {
    console.error('Unread notifications count error:', error);
    return 0;
  }

  return count || 0;
}

/**
 * En iyi performans gösteren salonu getirir
 */
async function getTopPerformingGym(supabase: SupabaseClient): Promise<{
  id: string;
  name: string;
  revenue: number;
  memberCount: number;
} | null> {
  const financialResult = await getManagerFinancialSummary();

  if (
    !financialResult.success ||
    !financialResult.data?.revenueByGym ||
    financialResult.data.revenueByGym.length === 0
  ) {
    return null;
  }

  const topGym = financialResult.data.revenueByGym[0];

  // Bu salon için üye sayısını al
  const { count: gymMemberCount } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', topGym.gymId)

  return {
    id: topGym.gymId,
    name: topGym.gymName,
    revenue: topGym.revenue,
    memberCount: gymMemberCount || 0,
  };
}

/**
 * Boş overview verisi döndürür
 */
function getEmptyOverview(): ManagerOverview {
  return {
    totalGyms: 0,
    totalMembers: 0,
    totalRevenue: 0,
    monthlyGrowth: 0,
    unreadNotifications: 0,
    pendingActions: 0,
    topPerformingGym: null,
  };
}
