/**
 * Payment business domain types
 *
 * Extracted from payment-related actions and components
 */

/**
 * Payment method types
 */
export type PaymentMethod = 'cash' | 'card' | 'transfer' | 'online';

/**
 * Payment status types
 */
export type PaymentStatus =
  | 'pending'
  | 'completed'
  | 'failed'
  | 'refunded'
  | 'cancelled';

/**
 * Payment transaction data
 */
export interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  description?: string;
  reference?: string;
  createdAt: string;
  completedAt?: string;
  failureReason?: string;
}

/**
 * Membership payment data
 */
export interface MembershipPayment extends PaymentTransaction {
  membershipId: string;
  packageId: string;
  memberId: string;
  gymId: string;
  startDate: string;
  endDate: string;
}

/**
 * Payment summary for analytics
 */
export interface PaymentSummary {
  totalRevenue: number;
  totalTransactions: number;
  averageTransaction: number;
  paymentMethods: {
    method: PaymentMethod;
    count: number;
    amount: number;
    percentage: number;
  }[];
  monthlyRevenue: {
    month: string;
    revenue: number;
    transactionCount: number;
  }[];
}

/**
 * Refund request data
 */
export interface RefundRequest {
  paymentId: string;
  amount: number;
  reason: string;
  requestedBy: string;
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  processedBy?: string;
  processedAt?: string;
  notes?: string;
}

/**
 * Payment plan data
 */
export interface PaymentPlan {
  id: string;
  totalAmount: number;
  installments: number;
  installmentAmount: number;
  frequency: 'weekly' | 'monthly' | 'quarterly';
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'cancelled';
  payments: {
    installmentNumber: number;
    dueDate: string;
    amount: number;
    status: PaymentStatus;
    paidAt?: string;
  }[];
}
