import { Metadata } from 'next';
import { Suspense } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Inbox, Send } from 'lucide-react';
import {
  MemberInvitationsHeader,
  IncomingInvitationsData,
  OutgoingInvitationsData,
} from './components/invitation-data-components';
import {
  HeaderSkeleton,
  InvitationsLoadingSkeleton,
} from './components/invitation-loading-components';

export const metadata: Metadata = {
  title: 'Salon Davetleri - Sportiva',
  description:
    'Aldığınız ve gönderdiğiniz salon davetlerini görüntüleyin ve yönetin.',
};

// Main Page Component (Server Component)
export default function MemberInvitationsPage() {
  return (
    <div className="space-y-6">
      {/* Header Section with Streaming */}
      <Suspense fallback={<HeaderSkeleton />}>
        <MemberInvitationsHeader />
      </Suspense>

      {/* Invitations Tabs with Streaming */}
      <Suspense fallback={<InvitationsLoadingSkeleton />}>
        <Tabs defaultValue="incoming" className="space-y-6">
          <TabsList className="bg-muted/50 dark:bg-muted/30 grid w-full grid-cols-2">
            <TabsTrigger
              value="incoming"
              className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 duration-300 data-[state=active]:shadow-sm"
            >
              <Inbox className="h-4 w-4" />
              Gelen Davetler
            </TabsTrigger>
            <TabsTrigger
              value="outgoing"
              className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 duration-300 data-[state=active]:shadow-sm"
            >
              <Send className="h-4 w-4" />
              Gönderilen Davetler
            </TabsTrigger>
          </TabsList>

          <TabsContent value="incoming" className="space-y-6">
            <Suspense fallback={<InvitationsLoadingSkeleton />}>
              <IncomingInvitationsData />
            </Suspense>
          </TabsContent>

          <TabsContent value="outgoing" className="space-y-6">
            <Suspense fallback={<InvitationsLoadingSkeleton />}>
              <OutgoingInvitationsData />
            </Suspense>
          </TabsContent>
        </Tabs>
      </Suspense>
    </div>
  );
}
