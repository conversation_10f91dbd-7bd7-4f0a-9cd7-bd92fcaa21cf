import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, DollarSign, Clock } from 'lucide-react';
import { Gyms } from '@/types/database/tables';
import { getGymRevenueReport } from '@/lib/actions/dashboard/company/gym-stats';

interface ActivityItem {
  id: string;
  type: 'member_joined' | 'package_purchased' | 'revenue';
  title: string;
  description: string;
  timestamp: string;
  amount?: number;
  status?: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface GymRecentActivityProps {
  gym: Gyms;
}

export async function GymRecentActivity({ gym }: GymRecentActivityProps) {
  const result = await getGymRevenueReport(gym.id);
  if (!result.success || !result.data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Son Aktiv<PERSON>ler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            <Activity className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Henüz aktivite yok</p>
            <p className="mt-1 text-sm">
              İlk üye kaydı veya paket satışı yapıldığında burada görünecek
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Revenue report verilerini ActivityItem formatına dönüştür
  const activities: ActivityItem[] = result.data.records
    .slice(0, 3)
    .map(record => ({
      id: record.id,
      type: 'package_purchased' as const,
      title: `${record.memberName} - ${record.packageName}`,
      description: `₺${record.purchasePrice.toLocaleString('tr-TR')} tutarında paket satın alındı`,
      timestamp: record.createdAt || record.startDate,
      amount: record.purchasePrice,
      status: record.status,
      icon: DollarSign,
    }));

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return 'Az önce';
    } else if (diffInHours < 24) {
      return `${diffInHours} saat önce`;
    } else if (diffInHours < 48) {
      return 'Dün';
    } else {
      return date.toLocaleDateString('tr-TR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-primary/10 text-primary">
            Aktif
          </Badge>
        );
      case 'expired':
        return <Badge variant="secondary">Süresi Dolmuş</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">İptal Edildi</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card className="rounded-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Son Aktiviteler
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length > 0 ? (
          <div className="space-y-3">
            {activities.map(activity => {
              const IconComponent = activity.icon;
              return (
                <div
                  key={activity.id}
                  className="hover:bg-muted/40 flex items-start gap-2 sm:gap-3 rounded-lg border p-2 sm:p-3 transition-colors"
                >
                  <div className="bg-primary/10 rounded-full p-1.5 sm:p-2 shrink-0">
                    <IconComponent className="text-primary h-3 w-3 sm:h-4 sm:w-4" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-1 sm:gap-2">
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-xs sm:text-sm font-medium">{activity.title}</p>
                        <p className="text-muted-foreground mt-0.5 sm:mt-1 line-clamp-2 text-xs">
                          {activity.description}
                        </p>
                        <div className="mt-1 sm:mt-2 flex flex-wrap items-center gap-1 sm:gap-2">
                          <Clock className="text-muted-foreground h-3 w-3" />
                          <span className="text-muted-foreground text-xs">
                            {formatDate(activity.timestamp)}
                          </span>
                          {activity.status && getStatusBadge(activity.status)}
                        </div>
                      </div>
                      {typeof activity.amount === 'number' && (
                        <div className="text-right shrink-0 mt-1 sm:mt-0">
                          <div className="text-xs sm:text-sm font-semibold">
                            ₺{activity.amount.toLocaleString('tr-TR')}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-muted-foreground py-8 text-center">
            <Activity className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Henüz aktivite yok</p>
            <p className="mt-1 text-sm">
              İlk üye kaydı veya paket satışı yapıldığında burada görünecek
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
