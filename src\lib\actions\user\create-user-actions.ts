'use server';

import { getSupabaseAdmin } from '@/lib/supabase/admin';
import { createAdminAction } from '../core/core';
import { ApiResponse } from '@/types/global/api';
import { createGymMembershipRecord, enforceGymMemberLimitForAdmin } from '../dashboard/member/membership-actions';
import { TrainerDetails } from '@/types/database/tables';

// Import utilities and constants
import {
  generateUserPassword,
  generateTrainerCode,
  generateGuestEmail,
  validateUserCreationData,
  rollbackUserCreation,
  createAuthUser,
  createUserProfile,
  createMemberDetailsRecord,
  type MemberDetailsData,
} from './user-creation-utils';

import { ERROR_MESSAGES, DATABASE_DEFAULTS } from './constants';

export interface MemberRequest {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string;
  };
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  message: string | null;
  created_at: string;
  responded_at: string | null;
  expires_at: string | null;
}

export interface MemberPackageDetails {
  id: string;
  packageName: string;
  packageType: string;
  description: string | null;
  durationDays: number;
  startDate: string;
  endDate: string | null;
  purchasePrice: number;
  status: string;
  createdAt: string;
  isActive: boolean;
  isExpired: boolean;
}
interface CompleteUserData {
  email: string | null;
  fullName: string;
  isGuest: boolean;
  avatarUrl?: string | null;
  phoneNumber?: string | null;
  gymId?: string;
  memberData?: MemberDetailsData;
}

interface CompleteUserResult {
  authData: { user: { id: string } };
  generatedPassword: string;
  userId: string;
  email?: string;
}

// ============================================================================
// TRAINER DETAILS CREATION
// ============================================================================
/**
 * Create trainer details record
 * Following Clean Code principles - focused single responsibility
 */
export async function createTrainerDetails(trainerData: {
  specialization: string;
  certification_level: string;
  experience_years?: number | null;
  bio?: string | null;
}): Promise<ApiResponse<TrainerDetails>> {
  return createAdminAction(async (_, _supabase, userId, adminClient) => {
    // Generate unique trainer code
    const inviteCode = await generateTrainerCode();

    // Create trainer details record
    const { data: trainerDetails, error: insertError } = await adminClient
      .from('trainer_details')
      .insert({
        profile_id: userId,
        specialization: trainerData.specialization,
        certification_level: trainerData.certification_level,
        experience_years:
          trainerData.experience_years || DATABASE_DEFAULTS.NULL_VALUE,
        bio: trainerData.bio || DATABASE_DEFAULTS.NULL_VALUE,
        invite_code: inviteCode,
        status: DATABASE_DEFAULTS.TRAINER_STATUS,
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(
        `${ERROR_MESSAGES.TRAINER_DETAILS_CREATION_FAILED}: ${insertError.message}`
      );
    }

    return trainerDetails;
  });
}

// ============================================================================
// COMPLETE USER CREATION WORKFLOW
// ============================================================================

/**
 * Complete user creation workflow
 * Following Clean Code principles - orchestrates user creation steps with proper error handling
 */

async function createCompleteUser(
  adminClient: ReturnType<typeof getSupabaseAdmin>,
  supabase: any,
  userData: CompleteUserData
): Promise<CompleteUserResult> {
  const {
    email,
    fullName,
    isGuest,
    avatarUrl,
    phoneNumber,
    gymId,
    memberData,
  } = userData;

  // Generate password
  const generatedPassword = await generateUserPassword(
    fullName,
    memberData?.age
  );

  try {
    // Step 1: Create auth user
    const authData = await createAuthUser(adminClient, {
      email,
      password: generatedPassword,
      fullName,
      isGuest,
      phoneNumber,
    });
    const userId = authData.user.id;

    // Step 2: Create profile
    try {
      await createUserProfile({
        userId,
        email,
        fullName,
        isGuest,
        avatarUrl,
        phoneNumber,
      });
    } catch (error) {
      await rollbackUserCreation(adminClient, userId, {
        context: ERROR_MESSAGES.ROLLBACK_PROFILE_CREATION,
      });
      const errorMessage =
        error instanceof Error ? error.message : 'Bilinmeyen hata';
      throw new Error(
        `${ERROR_MESSAGES.ROLLBACK_NOTIFICATION}. ${errorMessage}`
      );
    }

    // Step 3: Create member details if provided
    if (memberData) {
      try {
        await createMemberDetailsRecord(adminClient, userId, memberData);
      } catch (error) {
        await rollbackUserCreation(adminClient, userId, {
          deleteProfile: true,
          context: ERROR_MESSAGES.ROLLBACK_MEMBER_DETAILS_CREATION,
        });
        const errorMessage =
          error instanceof Error ? error.message : 'Bilinmeyen hata';
        throw new Error(
          `${ERROR_MESSAGES.ROLLBACK_NOTIFICATION}. ${errorMessage}`
        );
      }
    }

    // Step 4: Create gym membership if gymId provided (with limit enforcement)
    if (gymId) {
      try {
        await enforceGymMemberLimitForAdmin(adminClient, gymId);
        await createGymMembershipRecord(supabase, userId, gymId);
      } catch (error) {
        await rollbackUserCreation(adminClient, userId, {
          deleteProfile: true,
          deleteMemberDetails: !!memberData,
          context: ERROR_MESSAGES.ROLLBACK_GYM_MEMBERSHIP_CREATION,
        });
        const errorMessage =
          error instanceof Error ? error.message : 'Bilinmeyen hata';
        throw new Error(
          `${ERROR_MESSAGES.ROLLBACK_NOTIFICATION}. ${errorMessage}`
        );
      }
    }

    return {
      authData: { user: { id: userId } },
      generatedPassword,
      userId,
      email: isGuest && email ? email : undefined,
    };
  } catch (error) {
    console.error('Complete user creation error:', error);
    throw error;
  }
}

/**
 * Creates a regular user account with email and/or phone
 * Used by both managers and trainers to create new members
 * RLS policies automatically handle authorization
 */
export async function createUserForGym(
  gymId: string,
  userData: {
    email?: string | null;
    phone_number?: string | null;
    profileData: {
      full_name: string;
      avatar_url?: string | null;
    };
  }
): Promise<
  ApiResponse<{
    authData: { user: { id: string } };
    generatedPassword: string;
  }>
> {
  return await createAdminAction(
    async (_, supabase, _userId, adminClient) => {

      // Validate user data using utility function
      const validation = validateUserCreationData(userData);
      if (!validation.isValid) {
        throw new Error(ERROR_MESSAGES.EMAIL_OR_PHONE_REQUIRED);
      }

      const result = await createCompleteUser(adminClient, supabase, {
        email: validation.hasEmail ? userData.email! : null,
        fullName: userData.profileData.full_name,
        isGuest: false,
        avatarUrl: userData.profileData.avatar_url,
        phoneNumber: validation.hasPhone ? userData.phone_number! : null,
        gymId,
      });

      return {
        authData: result.authData,
        generatedPassword: result.generatedPassword,
      };
    },
    {
      revalidatePaths: [
        `/dashboard/gym/${gymId}/members`,
        `/dashboard/gym/${gymId}/members`,
      ],
    }
  );
}

/**
 * Creates a guest member account without email verification
 * Used by both managers and trainers for customers who don't have email or phone number
 */
export async function createGuestMemberForGym(
  gymId: string,
  memberData: {
    full_name: string;
    age: number;
    gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
    height_cm?: number;
    weight_kg?: number;
    fitness_goal?: string;
  }
): Promise<
  ApiResponse<{
    userId: string;
    guestEmail: string;
    generatedPassword: string;
  }>
> {
  return await createAdminAction(
    async (_, supabase, _userId, adminClient) => {

      // Generate guest email
      const guestEmail = generateGuestEmail(
        memberData.full_name,
        memberData.age
      );

      const result = await createCompleteUser(adminClient, supabase, {
        email: guestEmail,
        fullName: memberData.full_name,
        isGuest: true,
        gymId,
        memberData,
      });

      if (!result.userId) {
        throw new Error('Kullanıcı oluşturulurken hata oluştu');
      }

      return {
        userId: result.userId,
        guestEmail,
        generatedPassword: result.generatedPassword,
      };
    },
    {
      revalidatePaths: [
        `/dashboard/gym/${gymId}/members`,
        `/dashboard/gym/${gymId}/members`,
      ],
    }
  );
}

/**
 * Unified action for creating members from add-member form
 * Handles both regular and guest member creation based on form data
 */
export async function createMemberFromForm(formData: FormData): Promise<
  ApiResponse<{
    userId: string;
    generatedPassword: string;
    guestEmail?: string;
  }>
> {
  const gymId = formData.get('gymId') as string;
  return await createAdminAction(
    async (_, supabase, _userId, adminClient) => {
      // Extract form data
      const isGuestMode = formData.get('is_guest_mode') === 'on';
      const fullName = formData.get('full_name') as string;
      const age = parseInt(formData.get('age') as string);
      const gender = formData.get('gender') as
        | 'male'
        | 'female'
        | 'other'
        | 'prefer_not_to_say';
      const email = formData.get('email') as string;
      const phoneNumber = formData.get('phone_number') as string;
      const heightCm = formData.get('height_cm')
        ? parseInt(formData.get('height_cm') as string)
        : undefined;
      const weightKg = formData.get('weight_kg')
        ? parseInt(formData.get('weight_kg') as string)
        : undefined;
      const fitnessGoal = formData.get('fitness_goal') as string;

      // Validate required fields
      if (!gymId || !fullName || !age || !gender) {
        throw new Error(
          'Gerekli alanlar eksik: Salon ID, Ad Soyad, Yaş ve Cinsiyet zorunludur.'
        );
      }


      if (isGuestMode) {
        // Create guest member
        const memberData = {
          age,
          gender,
          height_cm: heightCm,
          weight_kg: weightKg,
          fitness_goal: fitnessGoal || undefined,
        };

        const guestEmail = generateGuestEmail(fullName, age);
        const result = await createCompleteUser(adminClient, supabase, {
          email: guestEmail,
          fullName,
          isGuest: true,
          gymId,
          memberData,
        });

        if (!result.userId) {
          throw new Error('Kullanıcı oluşturulurken hata oluştu');
        }

        return {
          userId: result.userId,
          generatedPassword: result.generatedPassword,
          guestEmail,
        };
      } else {
        // Create regular member
        const validation = validateUserCreationData({
          email: email || null,
          phone_number: phoneNumber || null,
        });

        if (!validation.isValid) {
          throw new Error(
            'E-posta veya telefon numarasından en az biri gereklidir.'
          );
        }

        const memberData = {
          age,
          gender,
          height_cm: heightCm,
          weight_kg: weightKg,
          fitness_goal: fitnessGoal || undefined,
        };

        const result = await createCompleteUser(adminClient, supabase, {
          email: validation.hasEmail ? email : null,
          fullName,
          isGuest: false,
          phoneNumber: validation.hasPhone ? phoneNumber : null,
          gymId,
          memberData,
        });

        if (!result.userId) {
          throw new Error('Kullanıcı oluşturulurken hata oluştu');
        }

        return {
          userId: result.userId,
          generatedPassword: result.generatedPassword,
        };
      }
    },
  );
}
