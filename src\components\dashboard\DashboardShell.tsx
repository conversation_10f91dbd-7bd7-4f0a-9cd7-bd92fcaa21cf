// Statik shell: light/dark tema ile uyumlu basit bir iskelet
export function DashboardShell() {
  return (
    <main id="main-content" className="relative flex min-h-screen w-full flex-col overflow-hidden">
      {/* Header Skeleton */}
      <div className="h-16 w-full border-b bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/40">
        <div className="mx-auto flex h-16 max-w-screen-2xl items-center gap-4 px-4 md:px-10">
          <div className="h-6 w-32 animate-pulse rounded bg-muted" />
          <div className="ml-auto flex items-center gap-3">
            <div className="h-8 w-8 animate-pulse rounded-full bg-muted" />
            <div className="h-8 w-20 animate-pulse rounded bg-muted" />
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="h-[calc(100vh-4rem)] overflow-y-auto px-4 py-8 md:pr-10 md:pl-24">
        <div className="space-y-6">
          <div className="h-8 w-60 animate-pulse rounded bg-muted" />
          <div className="grid gap-4 md:grid-cols-2">
            <div className="h-40 animate-pulse rounded-lg bg-muted" />
            <div className="h-40 animate-pulse rounded-lg bg-muted" />
          </div>
          <div className="h-64 animate-pulse rounded-lg bg-muted" />
        </div>
      </div>
    </main>
  );
}

export default DashboardShell;

