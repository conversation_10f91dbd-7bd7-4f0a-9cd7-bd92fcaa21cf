import { type NextRequest } from 'next/server';
import { authMiddleware } from './lib/auth/middleware-auth';

export async function middleware(request: NextRequest) {
  const { response } = await authMiddleware(request);

  // Pathname'i header'a ekle
  response.headers.set('x-pathname', request.nextUrl.pathname);

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - _vercel (Vercel internals)
     * - sitemap.xml, robots.txt, manifest.json (metadata files)
     * - error (error page to prevent infinite redirects)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|_vercel|sitemap.xml|robots.txt|manifest.json|error).*)',
  ],
};
