'use client'

import { But<PERSON> } from '@/components/ui/button'
import clsx from 'clsx'

interface CapacityNoticeProps {
  label: string
  count: number
  max: number | null
  threshold?: number // 0..1, default 0.8
  ctaHref?: string
}

export function CapacityNotice({
  label,
  count,
  max,
  threshold = 0.8,
  ctaHref = '/pricing',
}: CapacityNoticeProps) {
  if (max == null) return null
  const ratio = max > 0 ? count / max : 0
  const atLimit = count >= max
  const show = atLimit || ratio >= threshold
  if (!show) return null

  const tone = atLimit ? 'destructive' : 'warning'

  return (
    <div
      className={clsx(
        'mt-1 inline-flex items-center gap-3 rounded-md px-3 py-2 text-sm',
        tone === 'destructive' && 'bg-destructive/10 text-destructive',
        tone === 'warning' && 'bg-yellow-500/10 text-yellow-700 dark:text-yellow-400'
      )}
      role="status"
      aria-live="polite"
    >
      <span>
        {label}: {count}/{max}
        {atLimit ? ' — Limit dolu' : ' — <PERSON>ınıra yaklaştınız'}
      </span>
      <Button asChild size="sm" variant={atLimit ? 'destructive' : 'secondary'}>
        <a href={ctaHref}>Planları Gör</a>
      </Button>
    </div>
  )
}
