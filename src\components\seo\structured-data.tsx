import { useMemo } from 'react';
import { getSiteUrl } from '@/lib/utils/url-utils';

interface StructuredDataProps {
  type:
    | 'Organization'
    | 'WebSite'
    | 'WebPage'
    | 'LoginAction'
    | 'RegisterAction'
    | 'SoftwareApplication'
    | 'Product'
    | 'AboutPage';
  data?: {
    title?: string;
    description?: string;
    url?: string;
    [key: string]: unknown;
  };
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const jsonLd = useMemo(() => {
    let schema: Record<string, unknown> = {};
    const siteUrl = getSiteUrl();

    switch (type) {
      case 'Organization':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'Sportiva',
          description: 'Spor salonu yönetim platformu',
          url: siteUrl,
          logo: `${siteUrl}/logo.png`,
          contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            availableLanguage: 'Turkish',
          },
          sameAs: [
            'https://twitter.com/sportiva',
            'https://facebook.com/sportiva',
            'https://instagram.com/sportiva',
          ],
        };
        break;

      case 'WebSite':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'Sportiva',
          url: siteUrl,
          description:
            'Spor salonu yöneticileri ve üyeleri için dijital platform',
          potentialAction: {
            '@type': 'SearchAction',
            target: `${siteUrl}/findGym?search={search_term_string}`,
            'query-input': 'required name=search_term_string',
          },
        };
        break;

      case 'WebPage':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'WebPage',
          name: data?.title || 'Sportiva',
          description: data?.description || 'Spor salonu yönetim platformu',
          url: data?.url || siteUrl,
          isPartOf: {
            '@type': 'WebSite',
            name: 'Sportiva',
            url: siteUrl,
          },
        };
        break;

      case 'SoftwareApplication':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'SoftwareApplication',
          name: 'Sportiva',
          description:
            data?.description ||
            'Spor salonları ve üyeler için tek platform. Salon yönetimini dijitalleştirin, üye deneyimini iyileştirin ve işletmenizi büyütün.',
          url: siteUrl,
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Web',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'TRY',
            description: '14 gün ücretsiz deneme',
          },
          provider: {
            '@type': 'Organization',
            name: 'Sportiva',
            url: siteUrl,
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.9',
            ratingCount: '500',
            bestRating: '5',
          },
        };
        break;

      case 'Product':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'Product',
          name: 'Sportiva Spor Salonu Yönetim Sistemi',
          description: 'Spor salonları için kapsamlı dijital yönetim platformu',
          brand: {
            '@type': 'Brand',
            name: 'Sportiva',
          },
          offers: data?.offers || [
            {
              '@type': 'Offer',
              name: 'Temel Plan',
              price: '299',
              priceCurrency: 'TRY',
              priceValidUntil: '2025-12-31',
              availability: 'https://schema.org/InStock',
              url: `${siteUrl}/pricing`,
            },
            {
              '@type': 'Offer',
              name: 'Pro Plan',
              price: '599',
              priceCurrency: 'TRY',
              priceValidUntil: '2025-12-31',
              availability: 'https://schema.org/InStock',
              url: `${siteUrl}/pricing`,
            },
          ],
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.9',
            ratingCount: '500',
          },
        };
        break;

      case 'AboutPage':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'AboutPage',
          name: 'Hakkımızda - Sportiva',
          description:
            'Sportiva spor salonu yönetim platformu hakkında bilgi edinin. Misyonumuz, vizyonumuz ve ekibimiz.',
          url: `${siteUrl}/about`,
          mainEntity: {
            '@type': 'Organization',
            name: 'Sportiva',
            description: 'Spor salonu yönetim platformu',
            url: siteUrl,
          },
          isPartOf: {
            '@type': 'WebSite',
            name: 'Sportiva',
            url: siteUrl,
          },
        };
        break;

      case 'LoginAction':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'WebPage',
          name: 'Giriş Yap - Sportiva',
          description: 'Sportiva spor salonu yönetim platformuna giriş yapın',
          url: `${siteUrl}/auth/login`,
          isPartOf: {
            '@type': 'WebSite',
            name: 'Sportiva',
            url: siteUrl,
          },
          potentialAction: {
            '@type': 'LoginAction',
            target: `${siteUrl}/auth/login`,
            object: {
              '@type': 'EntryPoint',
              urlTemplate: `${siteUrl}/auth/login`,
              actionPlatform: [
                'http://schema.org/DesktopWebPlatform',
                'http://schema.org/MobileWebPlatform',
              ],
            },
          },
        };
        break;

      case 'RegisterAction':
        schema = {
          '@context': 'https://schema.org',
          '@type': 'WebPage',
          name: 'Kayıt Ol - Sportiva',
          description:
            'Sportiva spor salonu yönetim platformuna ücretsiz kayıt olun',
          url: `${siteUrl}/auth/register`,
          isPartOf: {
            '@type': 'WebSite',
            name: 'Sportiva',
            url: siteUrl,
          },
          potentialAction: {
            '@type': 'RegisterAction',
            target: `${siteUrl}/auth/register`,
            object: {
              '@type': 'EntryPoint',
              urlTemplate: `${siteUrl}/auth/register`,
              actionPlatform: [
                'http://schema.org/DesktopWebPlatform',
                'http://schema.org/MobileWebPlatform',
              ],
            },
          },
        };
        break;
    }

    return schema;
  }, [type, data]);

  // JSON-LD'yi güvenli bir şekilde string'e çevir
  const jsonLdString = useMemo(() => {
    try {
      return JSON.stringify(jsonLd, null, 0);
    } catch (error) {
      console.error('JSON-LD serialization error:', error);
      return '{}';
    }
  }, [jsonLd]);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      suppressHydrationWarning
    />
  );
}
