'use server';
import { sanitizeSlug } from '@/lib/utils/slug';
import { Companies, Gyms } from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import {
  createAction,
  gymSettingsSchema,
  gymSetupSchema,
  validateFormData,
} from '../../core';
import { createLimitReachedError } from '@/lib/utils/error-types';

/**
 * Salon bilgilerini company bilgileri ile birlikte getirir
 */
export async function getGymById(gymId: string): Promise<ApiResponse<Gyms & { company?: Companies }>> {
  return createAction<Gyms & { company?: any }>(
    async (_, supabase, _userId) => {
      // Company bilgilerini de getir
      const { data: gymWithCompany, error } = await supabase
        .from('gyms')
        .select(`
          *,
          company:companies(
            id,
            name,
            logo_url,
            phone,
            email
          )
        `)
        .eq('id', gymId)
        .single();

      if (error || !gymWithCompany) {
        return gymWithCompany!;
      }

      return gymWithCompany;
    },
  );
}

/**
 * Yeni salon oluşturur
 */
export async function createGym(
  formData: FormData
): Promise<ApiResponse<Gyms>> {
  return createAction<Gyms>(
    async (_, supabase, userId) => {
      // Form verilerini doğrula
      const validation = await validateFormData(formData, gymSetupSchema);
      if (validation.error) {
        throw new Error(validation.error);
      }

      const gymData = validation.data!;

      // Company sahipliği ve aktiflik + paket bilgisi
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id, subscription_status, name, phone, platform_package_id')
        .eq('manager_profile_id', userId)
        .eq('id', gymData.company_id)
        .eq('subscription_status', 'active')
        .single();

      if (companyError || !company) {
        throw new Error('Salon oluşturmak için aktif aboneliğiniz olmalıdır.');
      }

      // Salon limit kontrolü (platform_packages.max_gyms)
      if (company.platform_package_id) {
        const { data: pkg, error: pkgError } = await supabase
          .from('platform_packages')
          .select('max_gyms')
          .eq('id', company.platform_package_id)
          .single();
        if (pkgError) {
          throw new Error('Paket bilgileri alınamadı. Lütfen daha sonra tekrar deneyin.');
        }
        const maxGyms = (pkg as any)?.max_gyms as number | null;
        if (maxGyms != null) {
          const { count: currentGymCount, error: countError } = await supabase
            .from('gyms')
            .select('id', { count: 'exact', head: true })
            .eq('company_id', gymData.company_id);
          if (countError) {
            throw new Error('Salon sayısı kontrol edilirken bir hata oluştu.');
          }
          if ((currentGymCount ?? 0) >= maxGyms) {
            throw createLimitReachedError('Paket salon limiti dolu. Planınızı yükseltin.', {
              scope: 'gyms',
              companyId: gymData.company_id,
              current: currentGymCount ?? 0,
              max: maxGyms,
            });
          }
        }
      }

      // time_slots alanını FormData'dan al ve parse et
      const timeSlotsStr = formData.get('time_slots') as string | null;
      let time_slots: string[] = [];
      try {
        time_slots = timeSlotsStr ? JSON.parse(timeSlotsStr) : [];
      } catch {
        time_slots = [];
      }

      // features alanını FormData'dan al ve parse et
      const featuresStr = formData.get('features') as string | null;
      let features: string[] = [];
      try {
        features = featuresStr ? JSON.parse(featuresStr) : [];
      } catch {
        features = [];
      }

      // Slug oluştur
      const baseSlug = sanitizeSlug(gymData.name || 'salon');
      let slug = baseSlug;
      let counter = 1;

      // Benzersiz slug kontrolü
      while (true) {
        const { data: existingGym } = await supabase
          .from('gyms')
          .select('id')
          .eq('slug', slug)
          .single();

        if (!existingGym) break;
        slug = `${baseSlug}-${counter}`;
        counter++;
      }

      // Salon verilerini hazırla (manager_profile_id kaldırıldı, company_id eklendi)
      const insertData = {
        company_id: gymData.company_id,
        name: gymData.name || company.name, // Salon adı boşsa şirket adını kullan
        description: gymData.description || null,
        gym_phone: gymData.gym_phone || company.phone, // Telefon boşsa şirket telefonunu kullan
        address: gymData.address,
        city: gymData.city,
        district: gymData.district,
        gym_type: gymData.gym_type,
        cover_image_url: gymData.cover_image_url || null,
        opening_time: gymData.opening_time || null,
        closing_time: gymData.closing_time || null,
        features: features.length > 0 ? features : null,
        time_slots: time_slots.length > 0 ? time_slots : null,
        max_capacity: gymData.max_capacity ? parseInt(gymData.max_capacity) : null,
        slug,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Salonu oluştur
      const { data: newGym, error: createError } = await supabase
        .from('gyms')
        .insert(insertData)
        .select()
        .single();

      if (createError) {
        throw new Error(`Salon oluşturulurken hata: ${createError.message}`);
      }

      return newGym;
    },
    {
      revalidatePaths: ['/dashboard/company', '/dashboard/company/gym-setup'],
    }
  );
}

/**
 * Salon ayarlarını günceller - Tüm ayarlar tek seferde kaydedilir
 */
export async function updateGymSettings(
  formData: FormData
): Promise<ApiResponse<Gyms>> {
  // Form verilerini doğrula
  const validation = await validateFormData(formData, gymSettingsSchema);
  if (validation.error) {
    throw new Error(validation.error);
  }

  const { gymId, ...updateData } = validation.data!;

  return createAction<Gyms>(
    async (_, supabase, _userId) => {

      const cover_image_url = formData.get('cover_image_url') as string | null;

      const timeSlotsStr = formData.get('time_slots') as string | null;
      let time_slots: string[] = [];
      try {
        time_slots = timeSlotsStr ? JSON.parse(timeSlotsStr) : [];
      } catch {
        time_slots = [];
      }

      // features alanını FormData'dan al ve parse et
      const featuresStr = formData.get('features') as string | null;
      let features: string[] = [];
      try {
        features = featuresStr ? JSON.parse(featuresStr) : [];
      } catch {
        features = [];
      }

      // Güncelleme objesi oluştur
      const updateObj: Partial<Gyms> & {
        opening_time?: string;
        closing_time?: string;
        time_slots?: string[];
        features?: string[];
      } = {
        ...updateData,
        // opening_time ve closing_time alanlarını null ise undefined yap
        opening_time: updateData.opening_time ?? undefined,
        closing_time: updateData.closing_time ?? undefined,
        // max_capacity'yi integer'a çevir
        max_capacity: updateData.max_capacity ? parseInt(updateData.max_capacity) : null,
        updated_at: new Date().toISOString(),
      };

      // Opsiyonel alanları ekle (logo_url kaldırıldı)
      if (cover_image_url) updateObj.cover_image_url = cover_image_url;
      if (formData.has('time_slots')) {
        updateObj.time_slots = time_slots;
      }
      if (features.length > 0 || featuresStr !== null) {
        updateObj.features = features;
      }

      // Salon bilgilerini güncelle - RLS otomatik olarak gym erişimini kontrol edecek
      const { data: updatedGym, error: updateError } = await supabase
        .from('gyms')
        .update(updateObj as any)
        .eq('id', gymId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Salon güncellenirken hata: ${updateError.message}`);
      }

      return updatedGym;
    },
    {
      revalidatePaths: ['/dashboard/company'],
      revalidatePathsWithType: [
        { path: `/dashboard/gym/[gymId]`, type: 'page' },
      ],
    }
  );
}

/**
 * Slug benzersizliğini kontrol eder
 */
export async function checkSlugUniqueness(
  slug: string,
  currentGymId?: string
): Promise<{ available: boolean; error?: string }> {
  return createAction(
    async (_: any, supabase: any) => {
      let query = supabase.from('gyms').select('id').eq('slug', slug);

      // Kendi slug'ını exclude et (edit durumunda)
      if (currentGymId) {
        query = query.neq('id', currentGymId);
      }

      const { error } = await query.single();

      if (error && error.code === 'PGRST116') {
        // No rows found - slug available
        return { available: true };
      }

      if (error) {
        throw new Error('Slug kontrolü sırasında hata oluştu');
      }
      // Slug already exists
      return { available: false, error: 'Bu slug zaten kullanılıyor' };
    },
  ).then((result: any) => {
    if (result.success) {
      return result.data!;
    }
    return { available: false, error: result.error };
  });
}

/**
 * Salon slug'ını günceller
 */
export async function updateGymSlug(
  gymId: string,
  newSlug: string
): Promise<ApiResponse<Gyms>> {
  return createAction<Gyms>(
    async (_, supabase, _userId) => {
      // Slug'ı temizle ve validate et
      const { sanitizeSlug, validateSlug } = await import('@/lib/utils/slug');
      const cleanSlug = sanitizeSlug(newSlug);

      const validation = validateSlug(cleanSlug);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Slug benzersizliğini kontrol et
      const uniquenessCheck = await checkSlugUniqueness(cleanSlug, gymId);
      if (!uniquenessCheck.available) {
        throw new Error(uniquenessCheck.error || 'Bu slug zaten kullanılıyor');
      }

      // Slug'ı güncelle - RLS otomatik olarak gym erişimini kontrol edecek
      const { data: updatedGym, error: updateError } = await supabase
        .from('gyms')
        .update({
          slug: cleanSlug,
          updated_at: new Date().toISOString(),
        })
        .eq('id', gymId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Slug güncellenirken hata: ${updateError.message}`);
      }

      return updatedGym;
    },
    {
      revalidatePaths: [
        '/dashboard/company',
        `/dashboard/gym/${gymId}/settings`,
      ],
    }
  );
}

/**
 * Geçici olarak kapak görseli yükler (gym setup için)
 * Logo artık company'den gelir
 */
export async function uploadTempGymCoverImage(
  file: File
): Promise<{ success: boolean; url?: string; error?: string }> {
  return createAction<{ success: boolean; url?: string; error?: string }>(
    async (_, supabase, userId) => {
      const fileExt = file.name.split('.').pop();
      const fileName = `temp_cover_${userId}_${Date.now()}.${fileExt}`;
      const bucket = 'gym-covers';

      // Dosyayı yükle
      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) {
        throw new Error(`Görsel yüklenirken hata: ${uploadError.message}`);
      }

      // Public URL al
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      return { success: true, url: urlData.publicUrl };
    }
  ).then(result => {
    if (result.success) {
      return result.data!;
    }
    return { success: false, error: result.error };
  });
}

/**
 * Salon için kapak görseli yükler ve veritabanını günceller
 * Logo artık company'den gelir, salon ayarlarından güncellenemez
 */
export async function uploadGymCoverImage(
  file: File,
  gymId: string
): Promise<{ success: boolean; url?: string; error?: string }> {
  return createAction<{ success: boolean; url?: string; error?: string }>(
    async (_, supabase) => {
      const fileExt = file.name.split('.').pop();
      const fileName = `cover_${Date.now()}.${fileExt}`;
      const bucket = 'gym-covers';

      // Dosyayı yükle
      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) {
        throw new Error(`Görsel yüklenirken hata: ${uploadError.message}`);
      }

      // Public URL al
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      const imageUrl = urlData.publicUrl;

      // Veritabanını güncelle
      const { error: updateError } = await supabase
        .from('gyms')
        .update({
          cover_image_url: imageUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('id', gymId);

      if (updateError) {
        throw new Error(
          `Veritabanı güncellenirken hata: ${updateError.message}`
        );
      }

      return { success: true, url: imageUrl };
    },
    {
      revalidatePaths: [
        '/dashboard/company',
        `/dashboard/gym/${gymId}/settings`,
      ],
    }
  ).then(result => {
    if (result.success) {
      return result.data!;
    }
    return { success: false, error: result.error };
  });
}

/**
 * Salonu pasif yapar
 */
export async function deactivateGym(
  gymId: string,
): Promise<ApiResponse<Gyms>> {
  return createAction<Gyms>(
    async (_, supabase, _userId) => {
      const { data, error } = await supabase
        .from('gyms')
        .update({
          status: 'inactive',
          updated_at: new Date().toISOString(),
        })
        .eq('id', gymId)
        .select()
        .single();

      if (error || !data) {
        throw new Error(`Salon pasif yapılırken hata oluştu: ${error?.message}`);
      }

      return data;
    },
    {
      revalidatePaths: ['/dashboard/company', `/dashboard/gym/${gymId}`],
    }
  );
}

/**
 * Salonu soft delete yapar (30 gün geri alma süresi)
 */
export async function softDeleteGym(gymId: string): Promise<ApiResponse<Gyms>> {
  return createAction<Gyms>(
    async (_, supabase, _userId) => {
      const { data, error } = await supabase
        .from('gyms')
        .update({
          status: 'deleted',
          updated_at: new Date().toISOString(),
        })
        .eq('id', gymId)
        .select()
        .single();

      if (error || !data) {
        throw new Error(`Salon silinirken hata oluştu: ${error?.message}`);
      }

      return data;
    },
    {
      revalidatePaths: ['/dashboard/company'],
    }
  );
}

/**
 * Soft delete edilmiş salonu geri yükler
 */
export async function restoreGym(gymId: string): Promise<ApiResponse<Gyms>> {
  return createAction<Gyms>(
    async (_, supabase) => {
      // Deleted gym için özel sahiplik ve subscription kontrolü
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Giriş yapmanız gerekiyor.');
      }

      const { data: gymRow, error: gymError } = await supabase
        .from('gyms')
        .select(`
          status,
          company:companies!inner(
            id,
            manager_profile_id,
            subscription_status
          )
        `)
        .eq('id', gymId)
        .eq('company.manager_profile_id', user.id)
        .eq('company.subscription_status', 'active')
        .single();

      if (gymError || !gymRow) {
        throw new Error('Bu salona erişim yetkiniz yok veya aktif aboneliğiniz bulunmuyor.');
      }

      if (gymRow.status !== 'deleted') {
        throw new Error('Bu salon silinmiş durumda değil.');
      }

      const { data, error } = await supabase
        .from('gyms')
        .update({
          status: 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', gymId)
        .select()
        .single();

      if (error || !data) {
        throw new Error(
          `Salon geri yüklenirken hata oluştu: ${error?.message}`
        );
      }

      return data;
    },
    { revalidatePaths: [`/dashboard/gym/${gymId}`] }
  );
}
