'use client';

import { createContext, useContext, ReactNode } from 'react';
import { HeaderAuthStatus } from '@/components/header/shared/header-types';
import { GymAccessControlResult } from '@/lib/types/gym-access-control';

// 1. Context State'i için tip tanımı
export type DashboardContextState = HeaderAuthStatus & {
  gymAccess?: GymAccessControlResult;
};

// 2. Context'in oluşturulması
// Başlangıç değeri olarak null veriyoruz, çünkü provider olmadan erişilmemeli.
const DashboardContext = createContext<DashboardContextState | null>(null);

// 3. Provider Bileşeni
// Bu bileşen, context verisini alt bileşenlere sağlar.
interface DashboardProviderProps {
  children: ReactNode;
  value: DashboardContextState;
}

export function DashboardProvider({ children, value }: DashboardProviderProps) {
  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
}

// 4. Custom Hook
// Bu hook, bileşenlerin context verisine kolayca erişmesini sağlar.
export function useDashboard() {
  const context = useContext(DashboardContext);
  if (context === null) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
}
