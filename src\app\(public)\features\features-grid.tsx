'use client';

import {
  <PERSON><PERSON><PERSON>,
  Users,
  Package,
  BarChart4,
  Calendar,
  MapPin,
  Shield,
  Smartphone,
  Bell,
  Settings,
  ArrowRight,
  CheckCircle,
  Star,
  Zap,
  Clock,
  TrendingUp,
  UserCheck,
  FileText,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
export function FeaturesGrid() {
  const mainFeatures = [
    {
      icon: Dumbbell,
      title: 'Salon Yönetimi',
      description:
        'Salonunuzu dijital ortamda yönetin, üye ve paket takibini kolayca yapın.',
      details: [
        'Salon profili ve bilgi yönetimi',
        'Paket ve hizmet tanımlamaları',
        'Kapasite ve kaynak planlaması',
        'Çoklu salon desteği',
      ],
      color: 'from-blue-500/10 to-blue-600/10',
      borderColor: 'border-blue-200/50',
    },
    {
      icon: Users,
      title: 'Üye Yönetimi',
      description:
        'Üyelerinizi takip edin, katılım isteklerini onaylayın ve iletişimde kalın.',
      details: [
        '<PERSON><PERSON><PERSON><PERSON><PERSON> üye profilleri',
        '<PERSON>yelik durumu takibi',
        '<PERSON><PERSON><PERSON><PERSON><PERSON> onay sistemi',
        'İletişim ve bildirimler',
      ],
      color: 'from-green-500/10 to-green-600/10',
      borderColor: 'border-green-200/50',
    },
    {
      icon: Package,
      title: 'Paket Yönetimi',
      description:
        'Üyelik ve oturum bazlı paketlerin kurgulanması ve yönetimi.',
      details: [
        'Paket tanımları',
        'Kayıt ve atama akışları',
        'Paket geçmişi takibi',
        'İndirim ve kampanyalar',
      ],
      color: 'from-purple-500/10 to-purple-600/10',
      borderColor: 'border-purple-200/50',
    },
    {
      icon: BarChart4,
      title: 'Analitik Raporlar',
      description:
        'Detaylı raporlar ve analizlerle işletmenizin performansını ölçün.',
      details: [
        'Gelir ve gider analizleri',
        'Üye aktivite raporları',
        'Performans metrikleri',
        'Trend analizleri',
      ],
      color: 'from-orange-500/10 to-orange-600/10',
      borderColor: 'border-orange-200/50',
    },
    {
      icon: Calendar,
      title: 'Randevu Sistemi',
      description:
        'Online randevu sistemi ile üyelerinizin kolayca rezervasyon yapmasını sağlayın.',
      details: [
        'Online randevu alma',
        'Antrenör programı yönetimi',
        'Otomatik hatırlatmalar',
        'Grup dersleri planlaması',
      ],
      color: 'from-red-500/10 to-red-600/10',
      borderColor: 'border-red-200/50',
    },
    {
      icon: MapPin,
      title: 'Salon Keşfi',
      description:
        'Size en yakın ve en uygun spor salonlarını harita üzerinde keşfedin.',
      details: [
        'Harita tabanlı arama',
        'Mesafe ve konum filtreleri',
        'Salon detay sayfaları',
        'Değerlendirme sistemi',
      ],
      color: 'from-teal-500/10 to-teal-600/10',
      borderColor: 'border-teal-200/50',
    },
  ];

  const additionalFeatures = [
    {
      icon: Shield,
      title: 'Güvenli Altyapı',
      description: 'SSL şifreleme ve güvenli veri saklama',
    },
    {
      icon: Smartphone,
      title: 'Mobil Uyumlu',
      description: 'Her cihazdan erişilebilir responsive tasarım',
    },
    {
      icon: Bell,
      title: 'Bildirim Sistemi',
      description: 'Otomatik SMS ve e-posta bildirimleri',
    },
    {
      icon: Settings,
      title: 'Özelleştirme',
      description: 'İhtiyaçlarınıza göre özelleştirilebilir',
    },
    {
      icon: Clock,
      title: '7/24 Erişim',
      description: 'Kesintisiz hizmet ve sürekli erişim',
    },
    {
      icon: TrendingUp,
      title: 'Büyüme Desteği',
      description: 'Ölçeklenebilir altyapı ve çözümler',
    },
    {
      icon: UserCheck,
      title: 'Kolay Kullanım',
      description: 'Sezgisel arayüz ve kullanıcı dostu tasarım',
    },
    {
      icon: FileText,
      title: 'Raporlama',
      description: 'Detaylı raporlar ve veri analizi',
    },
  ];

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-6xl">
          {/* Section Header */}
          <div className="mb-16 text-center">
            <Badge
              variant="outline"
              className="mb-4 px-4 py-2 text-sm font-medium"
            >
              <Star className="mr-2 h-4 w-4" />
              Tüm Özellikler
            </Badge>
            <h2 className="text-primary mb-4 text-3xl font-bold md:text-4xl">
              İşletmenizi Güçlendirecek Özellikler
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
              Spor salonu yönetiminin her alanında size yardımcı olacak kapsamlı
              özellik seti
            </p>
          </div>

          {/* Main Features Grid */}
          <div className="mb-20 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {mainFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <article
                  key={`feature-${feature.title}-${index}`}
                  className={`group relative bg-gradient-to-br ${feature.color} rounded-2xl border p-8 ${feature.borderColor} hover:border-primary/30 transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl`}
                >
                  <div className="from-primary/5 absolute inset-0 rounded-2xl bg-gradient-to-br to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

                  <div className="relative z-10">
                    <div className="bg-primary/10 group-hover:bg-primary/20 mb-6 flex h-16 w-16 items-center justify-center rounded-2xl transition-colors duration-300">
                      <IconComponent className="text-primary h-8 w-8" />
                    </div>

                    <h3 className="text-primary mb-4 text-xl font-bold">
                      {feature.title}
                    </h3>

                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      {feature.description}
                    </p>

                    <ul className="mb-6 space-y-2">
                      {feature.details.map((detail, detailIndex) => (
                        <li
                          key={detailIndex}
                          className="text-muted-foreground flex items-center text-sm"
                        >
                          <CheckCircle className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
                          {detail}
                        </li>
                      ))}
                    </ul>

                    <div className="text-primary flex items-center text-sm font-medium opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                      <span>Daha fazla bilgi</span>
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </div>
                </article>
              );
            })}
          </div>

          {/* Additional Features */}
          <div className="bg-muted/30 border-border rounded-2xl border p-8 md:p-12">
            <div className="mb-12 text-center">
              <Badge
                variant="outline"
                className="mb-4 px-4 py-2 text-sm font-medium"
              >
                <Zap className="mr-2 h-4 w-4" />
                Ek Avantajlar
              </Badge>
              <h3 className="text-primary mb-4 text-2xl font-bold md:text-3xl">
                Sportiva&apos;yı Özel Kılan Özellikler
              </h3>
              <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
                Güvenlik, performans ve kullanıcı deneyimi odaklı ek
                özelliklerle farkı hissedin
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
              {additionalFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="group text-center">
                    <div className="bg-primary/10 group-hover:bg-primary/20 mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl transition-colors duration-300">
                      <IconComponent className="text-primary h-8 w-8" />
                    </div>
                    <h4 className="text-foreground mb-3 font-semibold">
                      {feature.title}
                    </h4>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
