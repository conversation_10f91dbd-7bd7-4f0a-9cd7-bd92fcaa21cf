'use client';

import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle2, XCircle, Clock, TrendingUp } from 'lucide-react';

interface MemberAppointmentsStatsProps {
  stats: {
    total: number;
    completed: number;
    cancelled: number;
    no_show: number;
    upcoming: number;
    sessionsUsed: number;
    totalSessionsPurchased: number;
  };
}

export function MemberAppointmentsStats({
  stats,
}: MemberAppointmentsStatsProps) {
  const statItems = [
    {
      title: 'Yaklaşan Randevular',
      value: stats.upcoming,
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '<PERSON><PERSON>lanan',
      value: stats.completed,
      icon: CheckCircle2,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON> Seans',
      value: stats.sessionsUsed,
      subtitle: `/ ${stats.totalSessionsPurchased} toplam`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'İptal/Gelmedi',
      value: stats.cancelled + stats.no_show,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
      {statItems.map(item => {
        const Icon = item.icon;
        return (
          <Card key={item.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-muted-foreground text-sm font-medium">
                    {item.title}
                  </p>
                  <div className="flex items-baseline gap-1">
                    <p className="text-2xl font-bold">{item.value}</p>
                    {item.subtitle && (
                      <p className="text-muted-foreground text-sm">
                        {item.subtitle}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`${item.bgColor} rounded-lg p-2`}>
                  <Icon className={`h-5 w-5 ${item.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
