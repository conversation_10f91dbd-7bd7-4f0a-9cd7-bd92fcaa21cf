/**
 * Basit bildirim mesajı oluşturur
 */
 export function createNotificationMessage(
  eventType: 'request' | 'accept' | 'reject' | 'invite' | 'cancel',
  direction: 'to_gym' | 'to_user',
  params: {
    userName: string;
    gymName: string;
    userRole: 'member' | 'trainer';
    customMessage?: string;
  }
): { title: string; message: string } {
  const { userName, gymName, userRole, customMessage } = params;

  // İstek gönderme
  if (eventType === 'request' && direction === 'to_gym') {
    return {
      title: userRole === 'trainer' ? 'Yeni Antrenör İsteği' : 'Ye<PERSON> Üyelik İsteği',
      message: `${userName} "${gymName}" salon${userRole === 'trainer' ? 'unda antrenör olmak' : 'una üye olmak'} istiyor.`
    };
  }

  // Davet gönderme
  if (eventType === 'invite' && direction === 'to_user') {
    return {
      title: userRole === 'trainer' ? 'Antrenör <PERSON>' : 'Salon Daveti',
      message: customMessage || `"${gymName}" salon${userRole === 'trainer' ? 'unda antrenör olarak çalışmaya' : 'una'} davet edildiniz.`
    };
  }

  // Kabul etme
  if (eventType === 'accept') {
    if (direction === 'to_gym') {
      return {
        title: 'Davet Kabul Edildi',
        message: `${userName} "${gymName}" davetini kabul etti.`
      };
    } else {
      return {
        title: userRole === 'trainer' ? 'Antrenör Başvurusu Onaylandı' : 'Üyelik Onaylandı',
        message: `"${gymName}" salon${userRole === 'trainer' ? 'unda antrenör başvurunuz' : 'una üyeliğiniz'} onaylandı.`
      };
    }
  }

  // Reddetme
  if (eventType === 'reject') {
    if (direction === 'to_gym') {
      return {
        title: 'Davet Reddedildi',
        message: `${userName} "${gymName}" davetini reddetti.`
      };
    } else {
      return {
        title: userRole === 'trainer' ? 'Antrenör Başvurusu Reddedildi' : 'Üyelik Başvurusu Reddedildi',
        message: `"${gymName}" salon${userRole === 'trainer' ? 'unda antrenör başvurunuz' : 'una üyelik başvurunuz'} reddedildi.`
      };
    }
  }

  // İptal etme
  if (eventType === 'cancel' && direction === 'to_gym') {
    return {
      title: 'İstek İptal Edildi',
      message: `${userName} "${gymName}" isteğini iptal etti.`
    };
  }

  // Fallback
  return {
    title: 'Bildirim',
    message: `${userName} ile "${gymName}" salonu hakkında bir güncelleme.`
  };
}
