import { useState } from 'react';
import { PlatformRoles } from '@/types/database/enums';
import type {
  PersonalData,
  MemberData,
  TrainerData,
  ManagerData,
  OnboardingFieldErrors,
  OnboardingStep,
} from '@/types/onboarding';

/**
 * Onboarding state management hook
 * Following Clean Code principles - single responsibility for state management
 */

export function useOnboardingState(currentRoles: PlatformRoles[]) {
  // Step management
  const [step, setStep] = useState<OnboardingStep>('welcome');
  
  // Role selection
  const [selectedRoles, setSelectedRoles] = useState<PlatformRoles[]>([]);
  
  // Error states
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<OnboardingFieldErrors>({});

  // Form data states
  const [personalData, setPersonalData] = useState<PersonalData>({
    first_name: '',
    last_name: '',
  });

  const [memberData, setMemberData] = useState<MemberData>({
    age: '',
    gender: '',
    height_cm: '',
    weight_kg: '',
    fitness_goal: '',
  });

  const [trainerData, setTrainerData] = useState<TrainerData>({
    specialization: '',
    certification_level: '',
    experience_years: '',
    bio: '',
  });

  const [managerData, setManagerData] = useState<ManagerData>({
    companyName: '',
    companyPhone: '',
    companyEmail: '',
  });

  /**
   * Handles role selection toggle
   */
  const handleRoleToggle = (roleId: PlatformRoles) => {
    const canSelectRole = !currentRoles.includes(roleId);
    if (canSelectRole) {
      setSelectedRoles(prev =>
        prev.includes(roleId)
          ? prev.filter(r => r !== roleId)
          : [...prev, roleId]
      );
    }
  };

  /**
   * Step navigation helpers
   */
  const handleContinueToRoles = () => {
    setStep('roles');
  };

  const handleContinueToForms = () => {
    if (selectedRoles.length > 0) {
      setStep('forms');
    }
  };

  /**
   * Goes back to role selection step
   */
  const handleBackToRoles = () => {
    setStep('roles');
  };

  /**
   * Move from forms to review
   */
  const handleContinueToReview = () => {
    setStep('review');
  };

  /**
   * Go back from review to forms
   */
  const handleBackToForms = () => {
    setStep('forms');
  };

  /**
   * Continue to payment (manager flow)
   */
  const handleContinueToPayment = () => {
    setStep('payment');
  };

  /**
   * Updates personal data with validation
   */
  const updatePersonalData = (data: PersonalData, validationError: string | null) => {
    setPersonalData(data);
    setFieldErrors(prev => ({ ...prev, personal: validationError }));
  };

  /**
   * Updates member data with validation
   */
  const updateMemberData = (data: MemberData, validationError: string | null) => {
    setMemberData(data);
    setFieldErrors(prev => ({ ...prev, member: validationError }));
  };

  /**
   * Updates trainer data with validation
   */
  const updateTrainerData = (data: TrainerData, validationError: string | null) => {
    setTrainerData(data);
    setFieldErrors(prev => ({ ...prev, trainer: validationError }));
  };

  /**
   * Updates manager data with validation
   */
  const updateManagerData = (data: ManagerData, validationError: string | null) => {
    setManagerData(data);
    setFieldErrors(prev => ({ ...prev, manager: validationError }));
  };

  /**
   * Clears all errors
   */
  const clearErrors = () => {
    setError(null);
    setFieldErrors({});
  };

  /**
   * Sets main error message
   */
  const setMainError = (errorMessage: string | null) => {
    setError(errorMessage);
  };

  return {
    // State
    step,
    selectedRoles,
    error,
    fieldErrors,
    personalData,
    memberData,
    trainerData,
    managerData,

    // Actions
    handleRoleToggle,
    handleContinueToRoles,
    handleContinueToForms,
    handleBackToRoles,
    handleContinueToReview,
    handleBackToForms,
    handleContinueToPayment,
    updatePersonalData,
    updateMemberData,
    updateTrainerData,
    updateManagerData,
    clearErrors,
    setMainError,
  };
}
