'use server';

import { getSupabaseAdmin } from '@/lib/supabase/admin';
import type {
  GymAccessControlResult,
  GymAccessRpcResult,
} from '@/lib/types/gym-access-control';

export async function checkGymAccess(gymId: string): Promise<GymAccessControlResult> {
  const supabase = await getSupabaseAdmin();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { hasAccess: false, role: 'none', gymId };
  }

  const { data, error } = await supabase.rpc('check_user_gym_access', {
    p_gym_id: gymId,
    p_user_id: user.id,
  });

  if (error || !data) {
    console.error('Error checking gym access:', error?.message);
    return { hasAccess: false, role: 'none', gymId };
  }

  // RPC'den dönen sonuç bir dizi olduğu için ilk elemanı alıyoruz.
  const result = data[0] as GymAccessRpcResult;

  if (!result) {
    return { hasAccess: false, role: 'none', gymId };
  }

  return {
    hasAccess: result.has_access,
    role: result.user_role,
    gymId,
    restrictedPages: result.restricted_pages || [],
  };
}
