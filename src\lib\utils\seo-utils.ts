import { Metadata } from 'next';
import { validateCityDistrict, generateBreadcrumbs } from './city-utils';

// Şehir sayfası için metadata oluşturma
export function generateCityMetadata(
  citySlug: string,
  districtSlug?: string,
  searchParams?: { [key: string]: string | string[] | undefined },
  gymCount?: number
): Metadata {
  const validation = validateCityDistrict(citySlug, districtSlug);

  if (!validation.isValid || !validation.city) {
    return {
      title: 'Sayfa Bulunamadı | Sportiva',
      description: 'Aradığınız sayfa bulunamadı.',
    };
  }

  const { city, district } = validation;
  const features =
    typeof searchParams?.features === 'string'
      ? searchParams.features.split(',').filter(Boolean)
      : [];
  const query = typeof searchParams?.q === 'string' ? searchParams.q : '';

  // Başlık oluşturma
  let title = district
    ? `${city.name} ${district} Spor Salonları`
    : `${city.name} Spor Salonları`;

  if (features.length > 0) {
    const featureText = features.slice(0, 2).join(', ');
    title += ` - ${featureText}`;
  }

  if (query) {
    title = `"${query}" - ${title}`;
  }

  if (gymCount !== undefined) {
    title = `${gymCount} ${title}`;
  }

  title += ' | Sportiva';

  // Açıklama oluşturma
  let description = district
    ? `${city.name} ${district} bölgesindeki en iyi spor salonlarını keşfedin.`
    : `${city.name} şehrindeki en kaliteli spor salonlarını bulun.`;

  if (features.length > 0) {
    description += ` ${features.join(', ')} özelliklerine sahip salonları inceleyin.`;
  }

  if (gymCount !== undefined) {
    description += ` ${gymCount} aktif spor salonu arasından size en uygun olanını seçin.`;
  }

  description +=
    ' Ücretsiz deneme seansları ve uygun fiyatlarla spor yapmaya başlayın.';

  // Anahtar kelimeler
  const keywords = [
    'spor salonu',
    'gym',
    'fitness',
    city.name.toLowerCase(),
    ...(district ? [district.toLowerCase()] : []),
    ...(features.length > 0 ? features : []),
    ...(query ? [query] : []),
    'spor merkezi',
    'antrenman',
    'fitness center',
  ];

  // Canonical URL
  const canonicalUrl = generateCanonicalUrl(
    citySlug,
    districtSlug,
    searchParams
  );

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'Sportiva',
      locale: 'tr_TR',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
    },
  };
}

import { getSiteUrl } from './url-utils';

// Canonical URL oluşturma
export function generateCanonicalUrl(
  citySlug: string,
  districtSlug?: string,
  searchParams?: { [key: string]: string | string[] | undefined }
): string {
  const baseUrl = getSiteUrl();

  let path = `/findGym/${citySlug}`;
  if (districtSlug) {
    path += `/${districtSlug}`;
  }

  // Query parametrelerini ekle (sadece anlamlı olanları)
  const params = new URLSearchParams();

  if (
    searchParams?.q &&
    typeof searchParams.q === 'string' &&
    searchParams.q.trim()
  ) {
    params.set('q', searchParams.q.trim());
  }

  if (
    searchParams?.features &&
    typeof searchParams.features === 'string' &&
    searchParams.features
  ) {
    params.set('features', searchParams.features);
  }

  const queryString = params.toString();
  return `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`;
}

// Structured data (JSON-LD) oluşturma
export function generateStructuredData(
  citySlug: string,
  districtSlug?: string
) {
  const validation = validateCityDistrict(citySlug, districtSlug);

  if (!validation.isValid || !validation.city) {
    return null;
  }

  const { city, district } = validation;
  const breadcrumbs = generateBreadcrumbs(citySlug, districtSlug);

  // BreadcrumbList schema
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: `${getSiteUrl()}${crumb.href}`,
    })),
  };

  // LocalBusiness schema
  const localBusinessSchema = {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: `Sportiva - ${city.name}${district ? ` ${district}` : ''} Spor Salonları`,
    description: `${city.name}${district ? ` ${district}` : ''} bölgesindeki en iyi spor salonlarını bulun`,
    url: generateCanonicalUrl(citySlug, districtSlug),
    address: {
      '@type': 'PostalAddress',
      addressLocality: district || city.name,
      addressRegion: city.name,
      addressCountry: 'TR',
    },
    areaServed: {
      '@type': 'City',
      name: city.name,
    },
    serviceType: 'Spor Salonu Arama Platformu',
  };

  return {
    breadcrumbSchema,
    localBusinessSchema,
  };
}

// Meta tags için ek bilgiler
export function generateAdditionalMetaTags(
  citySlug: string,
  districtSlug?: string
) {
  const validation = validateCityDistrict(citySlug, districtSlug);

  if (!validation.isValid || !validation.city) {
    return {};
  }

  const { city, district } = validation;

  return {
    'geo.region': 'TR',
    'geo.placename': city.name,
    'geo.position': '', // Koordinatlar eklenebilir
    ICBM: '', // Koordinatlar eklenebilir
    'DC.title': `${city.name}${district ? ` ${district}` : ''} Spor Salonları`,
    'DC.creator': 'Sportiva',
    'DC.subject': 'Spor Salonları, Fitness, Gym',
    'DC.description': `${city.name}${district ? ` ${district}` : ''} bölgesindeki spor salonları`,
  };
}
