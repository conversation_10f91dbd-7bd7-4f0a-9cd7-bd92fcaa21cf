import { memo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Clock, Target, Award, Heart, Zap } from "lucide-react";

interface GymAboutSectionProps {
  name: string | null;
  description: string | null;
  features: string[] | null;
}

export const GymAboutSection = memo(function GymAboutSection({
  name,
  description,
  features
}: GymAboutSectionProps) {
  return (
    <section className="py-24 bg-gradient-to-b from-background to-muted/20" aria-labelledby="about-heading">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6" aria-label="Hakkımızda etiketi">
            <Target className="h-4 w-4" aria-hidden="true" />
            <span>Hakkımızda</span>
          </div>
          <h2 id="about-heading" className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Neden <span className="text-primary">{name}</span>?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Modern ekipmanlar, profesyonel eğitmenler ve motivasyonel ortamımızla fitness hedeflerinize ulaşın
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-20 items-start">
          {/* About Content */}
          <div className="space-y-12">
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center" aria-hidden="true">
                  <Heart className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-3xl font-bold text-foreground">
                  Hikayemiz
                </h3>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {description || "Fitness tutkusu ve sağlıklı yaşam felsefesiyle kurulmuş olan salonumuz, her seviyeden sporcuya hitap eden modern bir fitness merkezi olarak hizmet vermektedir."}
              </p>
            </div>

            {/* Key Benefits */}
            <div className="space-y-6" aria-labelledby="benefits-heading">
              <h4 id="benefits-heading" className="text-2xl font-bold text-foreground mb-6">Avantajlarımız</h4>
              <div className="space-y-6">
                <div className="group flex items-start gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-primary/20 hover:bg-card transition-all duration-300">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors" aria-hidden="true">
                    <Zap className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h5 className="font-semibold text-foreground mb-2 text-lg">Modern Ekipmanlar</h5>
                    <p className="text-muted-foreground">Son teknoloji fitness ekipmanları ile etkili ve güvenli antrenmanlar yapın</p>
                  </div>
                </div>

                <div className="group flex items-start gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-primary/20 hover:bg-card transition-all duration-300">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors" aria-hidden="true">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h5 className="font-semibold text-foreground mb-2 text-lg">Profesyonel Destek</h5>
                    <p className="text-muted-foreground">Deneyimli eğitmenlerimizden kişisel antrenman desteği alın</p>
                  </div>
                </div>

                <div className="group flex items-start gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-primary/20 hover:bg-card transition-all duration-300">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors" aria-hidden="true">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h5 className="font-semibold text-foreground mb-2 text-lg">Esnek Saatler</h5>
                    <p className="text-muted-foreground">Size uygun saatlerde antrenman yapma imkanı sunuyoruz</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Features Section */}
          <div className="space-y-8" aria-labelledby="features-heading">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center" aria-hidden="true">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <h3 id="features-heading" className="text-3xl font-bold text-foreground">
                Salon Özellikleri
              </h3>
            </div>

            {features && Array.isArray(features) && features.length > 0 ? (
              <ul className="grid grid-cols-1 gap-4">
                {features.map((feature: string, index: number) => (
                  <li
                    key={`${feature}-${index}`}
                    className="group bg-card/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-border/50 hover:shadow-lg hover:border-primary/30 hover:bg-card transition-all duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors flex-shrink-0" aria-hidden="true">
                        <CheckCircle className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <span className="font-semibold text-card-foreground group-hover:text-primary transition-colors text-lg">
                          {feature}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="bg-card/80 backdrop-blur-sm rounded-2xl p-12 shadow-sm border border-border/50 text-center" role="status" aria-live="polite">
                <div className="w-20 h-20 bg-muted/50 rounded-2xl flex items-center justify-center mx-auto mb-6" aria-hidden="true">
                  <Dumbbell className="h-10 w-10 text-muted-foreground" />
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-2">Özellikler Yükleniyor</h4>
                <p className="text-muted-foreground">
                  Salon özellikleri yakında eklenecek
                </p>
              </div>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-2 gap-4 mt-8" aria-label="Salon kısa istatistikleri">
              <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-6 border border-primary/20">
                <div className="text-3xl font-bold text-primary mb-2">500+</div>
                <div className="text-sm text-muted-foreground">Mutlu Üye</div>
              </div>
              <div className="bg-gradient-to-br from-green-500/10 to-green-500/5 rounded-2xl p-6 border border-green-500/20">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">24/7</div>
                <div className="text-sm text-muted-foreground">Açık</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});
