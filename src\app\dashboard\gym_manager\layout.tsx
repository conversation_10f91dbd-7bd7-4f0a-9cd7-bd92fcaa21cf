import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';

export const metadata: Metadata = {
  title: 'Salon Yöneticisi Paneli | Sportiva',
  description: 'Sportiva salon yöneticisi paneli.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function GymManagerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Gym Manager Sidebar */}
      <DashboardSidebar mode='gym_manager' />
      {/* Main Content Area */}
      {children}
    </>
  );
}
