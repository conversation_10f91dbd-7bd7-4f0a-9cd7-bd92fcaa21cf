'use client';

import { useState } from 'react';
import { Menu } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

/**
 * App Mobile Navigation
 * - Dashboard hariç tüm sayfalarda kullanılır
 * - Genel site navigation linklerini içerir
 * - User durumuna göre login/register veya dashboard linklerini gösterir
 */
export function AppMobileNavigation() {
  const [isOpen, setIsOpen] = useState(false);

  const handleNavigate = () => {
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          aria-label="Site navigasyon menüsünü aç"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <SheetHeader className="sr-only">
          <SheetTitle>Site Navigasyon Menüsü</SheetTitle>
          <SheetDescription>
            Ana site navigasyonu ve kullanıcı işlemleri
          </SheetDescription>
        </SheetHeader>

        <div className="flex h-full flex-col">
          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Site Navigation */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Site Menü</h3>
                <nav className="space-y-2">
                  <Link
                    href="/findGym"
                    onClick={handleNavigate}
                    className="text-muted-foreground hover:text-primary block py-2 text-sm font-medium transition-colors"
                  >
                    Salon Bul
                  </Link>
                  <Link
                    href="/features"
                    onClick={handleNavigate}
                    className="text-muted-foreground hover:text-primary block py-2 text-sm font-medium transition-colors"
                  >
                    Özellikler
                  </Link>
                  <Link
                    href="/pricing"
                    onClick={handleNavigate}
                    className="text-muted-foreground hover:text-primary block py-2 text-sm font-medium transition-colors"
                  >
                    Fiyatlandırma
                  </Link>
                  <Link
                    href="/about"
                    onClick={handleNavigate}
                    className="text-muted-foreground hover:text-primary block py-2 text-sm font-medium transition-colors"
                  >
                    Hakkımızda
                  </Link>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
