"use client";

import { 
  Shield, 
  Smartphone, 
  BarChart3, 
  Users, 
  Package, 
  Bell,
  Calendar,
  Settings,
  Zap
} from "lucide-react";

export function AboutFeatures() {
  const features = [
    {
      icon: Users,
      title: "Üye Yönetimi",
      description: "Kapsamlı üye profilleri, üyelik takibi ve otomatik bildirimler ile üyelerinizi kolayca yönetin."
    },
    {
      icon: Package,
      title: "Paket Yönetimi",
      description: "Üyelik ve oturum bazlı paketleri tanımlayın, atayın ve yenileme süreçlerini yönetin."
    },
    {
      icon: BarChart3,
      title: "Analitik & Raporlama",
      description: "Detaylı raporlar ve analizler ile salonunuzun performansını takip edin ve stratejik kararlar alın."
    },
    {
      icon: Calendar,
      title: "<PERSON>ev<PERSON> Si<PERSON>",
      description: "Online randevu sistemi ile üyelerinizin kolayca rezervasyon yapmasını sağlayın."
    },
    {
      icon: Bell,
      title: "<PERSON><PERSON><PERSON><PERSON>",
      description: "Otomatik SMS ve e-posta bildirimleri ile üyelerinizle sürekli iletişim halinde kalın."
    },
    {
      icon: Smartphone,
      title: "Mobil Uyumlu",
      description: "Responsive tasarım ile her cihazdan erişilebilir, mobil uyumlu arayüz."
    },
    {
      icon: Shield,
      title: "Güvenlik",
      description: "SSL şifreleme ve güvenli veri saklama ile üye bilgilerinizi koruyoruz."
    },
    {
      icon: Settings,
      title: "Özelleştirme",
      description: "Salonunuzun ihtiyaçlarına göre özelleştirilebilir modüller ve ayarlar."
    },
    {
      icon: Zap,
      title: "Hızlı Performans",
      description: "Bulut tabanlı altyapı ile hızlı ve kesintisiz hizmet deneyimi."
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-muted/20 via-background to-muted/20">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-4 py-2 text-sm font-medium text-primary backdrop-blur-sm">
              <Package className="h-4 w-4" />
              <span>Özellikler</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-6">
              Neler Sunuyoruz?
            </h2>
            <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Spor salonu yönetiminizi kolaylaştıran kapsamlı özellikler ve araçlar
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={index}
                  className="group relative overflow-hidden bg-gradient-to-br from-background/90 to-background/70 rounded-2xl p-8 border border-border/50 hover:shadow-2xl hover:border-primary/30 transition-all duration-500 hover:-translate-y-2"
                >
                  {/* Background glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100" />

                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 border border-primary/20">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>

                    <h3 className="text-xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                      {feature.title}
                    </h3>

                    <p className="text-muted-foreground leading-relaxed text-base">
                      {feature.description}
                    </p>
                  </div>

                  {/* Decorative corner */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
