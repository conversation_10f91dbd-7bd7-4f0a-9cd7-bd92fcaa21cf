/**
 * Cleans Turkish characters and formats name for email/password
 */
function cleanTurkishText(text: string): string {
  return text
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/Ğ/g, 'g')
    .replace(/Ü/g, 'u')
    .replace(/Ş/g, 's')
    .replace(/İ/g, 'i')
    .replace(/Ö/g, 'o')
    .replace(/Ç/g, 'c')
    .toLowerCase()
    .replace(/\s+/g, ''); // Boşlukları kaldır
}

/**
 * Generates an email address for Sportiva users
 * Supports both guest and regular member formats
 *
 * @param fullName - Member's full name
 * @param identifier - Age for guests, phone number for regular members
 * @param type - 'guest' for guest members, 'phone' for regular members
 * @returns A memorable email address
 *
 * @example
 * generateSportivaEmail('<PERSON><PERSON> Yılmaz', 25, 'guest')
 * // Returns: '<EMAIL>'
 *
 * generateSportivaEmail('Ah<PERSON> Yılmaz', '5551234567', 'phone')
 * // Returns: '<EMAIL>'
 */
export function generateSportivaEmail(
  fullName: string,
  identifier: number | string,
  type: 'guest' | 'phone'
): string {
  const cleanName = cleanTurkishText(fullName);

  if (type === 'guest') {
    return `${cleanName}${identifier}@sportiva.com`;
  } else if (type === 'phone') {
    // Clean phone number (remove spaces, dashes, parentheses)
    const cleanPhone = String(identifier).replace(/[\s\-\(\)\+]/g, '');
    // Take last 10 digits if phone is longer
    const phoneDigits = cleanPhone.slice(-10);
    // Limit name to 10 characters to keep email reasonable length
    const limitedName = cleanName.slice(0, 10);
    return `${limitedName}${phoneDigits}@sportiva.com`;
  }

  throw new Error(`Unsupported email type: ${type}`);
}

/**
 * Checks if an email is a guest email
 * Now that both guest and regular members use @sportiva.com,
 * we need to distinguish by pattern
 *
 * @param email - Email address to check
 * @returns True if the email is a guest email
 */
export function isGuestEmail(email: string): boolean {
  if (!email.endsWith('@sportiva.com')) {
    return false;
  }

  // Extract the local part (before @)
  const localPart = email.split('@')[0];

  // Eski formatlar: guest-{7digits}@sportiva.com, guest-{gymId}-{randomId}@sportiva.com
  if (localPart.startsWith('guest-')) {
    return true;
  }

  // Yeni guest format: {name}{age}@sportiva.com (age is typically 2-3 digits)
  // Regular member format: {name}{phone}@sportiva.com (phone is typically 10 digits)
  // If it ends with 2-3 digits, it's likely a guest (age)
  // If it ends with 10 digits, it's likely a regular member (phone)
  const digitMatch = localPart.match(/(\d+)$/);
  if (digitMatch) {
    const digits = digitMatch[1];
    // Age is typically 2-3 digits (10-999), phone is 10 digits
    // If 10 digits, it's definitely a phone number (regular member)
    if (digits.length === 10) {
      return false; // Regular member with phone
    }
    // If 2-3 digits, it's likely age (guest member)
    return digits.length >= 2 && digits.length <= 3;
  }

  return false;
}

/**
 * Extracts gym ID from a guest email
 *
 * @param guestEmail - Misafir email address
 * @returns Gym ID or null if not a valid guest email
 *
 * @example
 * extractGymIdFromGuestEmail('<EMAIL>')
 * // Returns: 'gym123'
 */
export function extractGymIdFromGuestEmail(guestEmail: string): string | null {
  if (!isGuestEmail(guestEmail)) {
    return null;
  }

  // Remove 'guest-' prefix and '@sportiva.com' suffix
  const middle = guestEmail.slice(6, -13); // 'guest-'.length = 6, '@sportiva.com'.length = 13

  // Find the last dash to separate gymId from randomId
  const lastDashIndex = middle.lastIndexOf('-');

  if (lastDashIndex === -1) {
    return null;
  }

  return middle.slice(0, lastDashIndex);
}

/**
 * Generates a memorable password using member's name and age
 * Format: {firstname}{lastname}{age}
 *
 * @param fullName - Member's full name
 * @param age - Member's age
 * @returns A memorable password based on member's name and age
 *
 * @example
 * generateMemorablePassword('Ahmet Yılmaz', 25)
 * // Returns: 'ahmetyilmaz25'
 */
export function generateMemorablePassword(
  fullName: string,
  age: number
): string {
  const cleanName = fullName.toLowerCase().replace(/\s+/g, '');
  return `${cleanName}${age}`;
}

/**
 * Creates a display name for guest users
 * Converts guest email to a user-friendly format
 *
 * @param guestEmail - Misafir email address
 * @returns User-friendly display name
 *
 * @example
 * getGuestDisplayName('<EMAIL>')
 * // Returns: 'Misafir Üye (gym123)'
 */
export function getGuestDisplayName(guestEmail: string): string {
  const gymId = extractGymIdFromGuestEmail(guestEmail);

  if (!gymId) {
    return 'Misafir Üye';
  }

  return `Misafir Üye (${gymId})`;
}

/**
 * Validates guest email format
 *
 * @param email - Email to validate
 * @returns Validation result with success flag and error message
 */
export function validateGuestEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email) {
    return { isValid: false, error: 'Email gereklidir' };
  }

  if (!isGuestEmail(email)) {
    return { isValid: false, error: 'Geçerli bir guest email formatı değil' };
  }

  const gymId = extractGymIdFromGuestEmail(email);
  if (!gymId) {
    return { isValid: false, error: "Misafir email'den gym ID çıkarılamadı" };
  }

  return { isValid: true };
}
