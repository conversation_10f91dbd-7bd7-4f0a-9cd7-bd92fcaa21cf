'use client';

import { usePathname } from 'next/navigation';

export function useSettingsTitle() {
  const pathname = usePathname();

  const getTitleByPath = (path: string) => {
    switch (path) {
      case '/profile/settings/profile':
        return 'Profil Bilgileri';
      case '/profile/settings/physical':
        return 'Fiziksel Bilgiler';
      case '/profile/settings/trainer':
        return 'Antrenör Bilgileri';
      case '/profile/settings/manager':
        return 'Yönetici Bilgileri';
      case '/profile/settings/preferences':
        return 'Kişiselleştirme';
      case '/profile/settings/security':
        return 'Güvenlik Ayarları';
      case '/profile/settings/roles':
        return 'Rol Yönetimi';
      default:
        return 'Profil Ayarları';
    }
  };

  return getTitleByPath(pathname);
}
