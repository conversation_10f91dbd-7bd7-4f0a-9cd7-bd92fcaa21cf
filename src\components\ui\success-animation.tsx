'use client';

import { CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface SuccessAnimationProps {
  show: boolean;
  message?: string;
  className?: string;
  onComplete?: () => void;
}

export function SuccessAnimation({ 
  show, 
  message = "Başarılı!", 
  className,
  onComplete 
}: SuccessAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [show, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm",
      className
    )}>
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-2xl transform animate-in zoom-in-95 duration-300">
        <div className="text-center space-y-4">
          <div className="relative">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto animate-pulse" />
            <div className="absolute inset-0 h-16 w-16 mx-auto rounded-full bg-green-500/20 animate-ping" />
          </div>
          <p className="text-lg font-semibold text-foreground">{message}</p>
        </div>
      </div>
    </div>
  );
}
