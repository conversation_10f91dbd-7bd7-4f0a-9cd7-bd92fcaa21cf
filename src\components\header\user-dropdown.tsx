'use client';

import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LogOut, Monitor, Sun, Moon, UserCog2 } from 'lucide-react';
import Link from 'next/link';
import { logoutAction } from '@/lib/actions/auth/auth-actions';
import { Profiles } from '@/types';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface UserDropdownProps {
  profile: Profiles;
}

export function UserDropdown({ profile }: UserDropdownProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const themeOptions = [
    { value: 'light', label: 'A<PERSON>ık', icon: Sun },
    { value: 'dark', label: 'Koyu', icon: Moon },
    { value: 'system', label: 'Sistem Varsayılanı', icon: Monitor },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="hover:bg-accent/50 hover:ring-primary/20 relative h-9 w-9 rounded-full transition-all duration-200 hover:ring-2"
        >
          <Avatar className="ring-border/50 h-8 w-8 ring-2 transition-all duration-200">
            <AvatarImage
              src={profile?.avatar_url || ''}
              alt={profile?.full_name || 'Kullanıcı'}
              className="object-cover"
            />
            <AvatarFallback className="bg-primary text-primary-foreground text-sm">
              {profile?.full_name
                ?.split(' ')
                .map(n => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          {/* Online indicator */}
          <div className="border-background absolute right-0 bottom-0 h-2.5 w-2.5 rounded-full border-2 bg-green-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end" forceMount>
        {/* User Info */}
        <DropdownMenuLabel className="p-3 font-normal">
          <div className="flex flex-col space-y-1">
            <p className="truncate text-sm leading-none font-semibold">
              {profile?.full_name}
            </p>
            <p className="text-muted-foreground truncate text-xs leading-none">
              {profile?.email}
            </p>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {/* Menu Items */}
        <DropdownMenuItem asChild>
          <Link
            href="/profile/settings"
            className="hover:bg-accent flex cursor-pointer items-center gap-3 px-3 py-2.5 text-sm transition-colors"
          >
            <UserCog2 className="text-muted-foreground h-4 w-4" />
            <span>Hesap Ayarları</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />

        {/* Theme Section */}
        <div className="px-3 py-2">
          <p className="text-muted-foreground mb-2 text-xs font-medium">
            Theme
          </p>
          <div className="space-y-1">
            {mounted &&
              themeOptions.map(option => {
                const Icon = option.icon;
                const isSelected = theme === option.value;

                return (
                  <button
                    key={option.value}
                    onClick={() => setTheme(option.value)}
                    className={`hover:bg-accent flex w-full items-center gap-3 rounded-sm px-2 py-1.5 text-sm transition-colors ${isSelected ? 'bg-accent' : ''
                      }`}
                  >
                    <Icon className="text-muted-foreground h-4 w-4" />
                    <span className="flex-1 text-left">{option.label}</span>
                    {isSelected && (
                      <div className="bg-primary h-2 w-2 rounded-full" />
                    )}
                  </button>
                );
              })}
          </div>
        </div>

        <DropdownMenuSeparator />

        {/* Logout */}
        <DropdownMenuItem asChild>
          <button
            onClick={logoutAction}
            className="flex w-full cursor-pointer items-center gap-3 px-3 py-2.5 text-sm transition-colors hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950/20"
          >
            <LogOut className="h-4 w-4" />
            <span>Çıkış Yap</span>
          </button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
