'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Home, LogIn } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface AuthErrorHandlerProps {
  error: string;
  showRetry?: boolean;
  showHome?: boolean;
  showLogin?: boolean;
  className?: string;
}

/**
 * Auth error'ları için merkezi error handling component'i
 * Kullanıcı dostu hata mesajları ve action button'ları sağlar
 */
export function AuthErrorHandler({
  error,
  showRetry = false,
  showHome = true,
  showLogin = false,
  className = '',
}: AuthErrorHandlerProps) {
  const router = useRouter();
  const errorMessages: Record<
    string,
    { title: string; description: string; icon: React.ReactNode }
  > = {
    session_expired: {
      title: 'Oturumunuz Sona Erdi',
      description:
        'Güvenliğiniz için oturumunuz sonlandırıldı. Lütfen tekrar giriş yapın.',
      icon: <LogIn className="h-4 w-4" />,
    },
    access_denied: {
      title: 'Erişim Reddedildi',
      description:
        'Bu sayfaya erişim yetkiniz bulunmuyor. Lütfen yetkili kişi ile iletişime geçin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    gym_access_denied: {
      title: 'Salon Erişimi Reddedildi',
      description:
        'Bu salona erişim yetkiniz bulunmuyor. Salon yöneticisi ile iletişime geçin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    gym_ownership_denied: {
      title: 'Salon Sahipliği Hatası',
      description:
        'Bu salon size ait değil. Sadece kendi salonlarınızı yönetebilirsiniz.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    no_active_subscription: {
      title: 'Aktif Abonelik Bulunamadı',
      description:
        'Bu özelliği kullanmak için aktif bir aboneliğiniz olması gerekiyor.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    invalid_credentials: {
      title: 'Geçersiz Giriş Bilgileri',
      description:
        'E-posta adresiniz veya şifreniz hatalı. Lütfen tekrar deneyin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    email_not_confirmed: {
      title: 'E-posta Doğrulaması Gerekli',
      description:
        'Hesabınızı kullanmak için e-posta adresinizi doğrulamanız gerekiyor.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    too_many_requests: {
      title: 'Çok Fazla Deneme',
      description:
        'Güvenlik nedeniyle geçici olarak engellendiniz. Lütfen birkaç dakika sonra tekrar deneyin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    network_error: {
      title: 'Bağlantı Hatası',
      description: 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      icon: <RefreshCw className="h-4 w-4" />,
    },
    server_error: {
      title: 'Sunucu Hatası',
      description:
        'Geçici bir teknik sorun yaşanıyor. Lütfen daha sonra tekrar deneyin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
    unknown_error: {
      title: 'Bilinmeyen Hata',
      description:
        'Beklenmeyen bir hata oluştu. Sorun devam ederse destek ekibi ile iletişime geçin.',
      icon: <AlertCircle className="h-4 w-4" />,
    },
  };

  const errorInfo = errorMessages[error] || errorMessages.unknown_error;

  const getActionButtons = () => {
    const buttons = [];

    if (showRetry) {
      buttons.push(
        <Button
          key="retry"
          variant="outline"
          size="sm"
          onClick={() => router.refresh()}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Tekrar Dene
        </Button>
      );
    }

    if (
      showLogin ||
      error === 'session_expired' ||
      error === 'invalid_credentials'
    ) {
      buttons.push(
        <Button key="login" size="sm" asChild>
          <Link href="/auth/login" className="flex items-center gap-2">
            <LogIn className="h-4 w-4" />
            Giriş Yap
          </Link>
        </Button>
      );
    }

    if (error === 'no_active_subscription') {
      buttons.push(
        <Button key="pricing" size="sm" asChild>
          <Link href="/onboarding" className="flex items-center gap-2">
            Abonelik Al
          </Link>
        </Button>
      );
    }

    if (showHome) {
      buttons.push(
        <Button key="home" variant="outline" size="sm" asChild>
          <Link href="/" className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            Ana Sayfa
          </Link>
        </Button>
      );
    }

    return buttons;
  };

  return (
    <div
      className={`flex min-h-[60vh] items-center justify-center p-4 ${className}`}
    >
      <div className="w-full max-w-md space-y-4">
        <Alert variant="destructive">
          <div className="flex items-center gap-2">
            {errorInfo.icon}
            <div className="flex-1">
              <h3 className="font-semibold">{errorInfo.title}</h3>
              <AlertDescription className="mt-1">
                {errorInfo.description}
              </AlertDescription>
            </div>
          </div>
        </Alert>

        <div className="flex flex-wrap justify-center gap-2">
          {getActionButtons()}
        </div>
      </div>
    </div>
  );
}

/**
 * Compact error component (inline kullanım için)
 */
export function AuthErrorInline({
  error,
  className = '',
}: {
  error: string;
  className?: string;
}) {
  const errorMessages: Record<string, string> = {
    session_expired: 'Oturumunuz sona erdi. Lütfen tekrar giriş yapın.',
    access_denied: 'Bu sayfaya erişim yetkiniz bulunmuyor.',
    gym_access_denied: 'Bu salona erişim yetkiniz bulunmuyor.',
    gym_ownership_denied: 'Bu salon size ait değil.',
    no_active_subscription: 'Aktif aboneliğiniz bulunmuyor.',
    invalid_credentials: 'Geçersiz giriş bilgileri.',
    email_not_confirmed: 'E-posta doğrulaması gerekli.',
    too_many_requests: 'Çok fazla deneme. Lütfen bekleyin.',
    network_error: 'Bağlantı hatası.',
    server_error: 'Sunucu hatası.',
    unknown_error: 'Bilinmeyen hata oluştu.',
  };

  const message = errorMessages[error] || errorMessages.unknown_error;

  return (
    <Alert variant="destructive" className={className}>
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
}
