/**
 * User business domain types
 *
 * Extracted from src/lib/actions/user/types.ts and enhanced
 */

import type {
  Profiles,
  TrainerDetails,
  MemberDetails,
} from '../database/tables';
import type { PlatformRoles } from '../database/enums';

/**
 * Enhanced user profile with role information
 */
export interface UserProfile extends Profiles {
  // Enhanced properties
  roles: PlatformRoles[];
  isManager: boolean;
  isTrainer: boolean;
  isMember: boolean;
}

/**
 * Trainer with gym information
 */
export interface TrainerGym {
  gym_id: string;
  gym_name: string;
  gym_address: string;
  status: string;
  left_at: string | null;
}

/**
 * Trainer with gym details
 */
export interface TrainerWithGyms extends TrainerDetails {
  // Related data
  profile: Profiles;
  gyms: TrainerGym[];
}

/**
 * Manager status information
 */
export interface ManagerStatusData {
  isManager: boolean;
  tier?: string;
  packageType?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  status?: string;
  daysRemaining?: number;
}

/**
 * Public membership data
 */
export interface PublicMembershipData {
  id: string;
  gym_id: string;
  status: 'active' | 'passive';
  approved_at: string | null;
  created_at: string;
  updated_at: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
}

/**
 * Membership check result
 */
export interface MembershipCheckResult {
  hasActiveMembership: boolean;
  membership: PublicMembershipData | null;
}

/**
 * Member with membership details
 */
export interface MemberWithMemberships extends MemberDetails {
  // Related data
  profile: Profiles;
  memberships: PublicMembershipData[];
}
