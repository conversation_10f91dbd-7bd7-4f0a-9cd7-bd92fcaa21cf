import { Search } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { GymCard } from './GymCard';
import { CITIES } from '@/lib/constants';
import { GymCardData } from '@/types/business/gym';
import { Suspense } from 'react';

interface SearchCriteria {
  query: string;
  city: string;
  district: string;
  features: string[];
}

interface GymGridProps {
  gyms: GymCardData[];
  hasActiveFilters: boolean;
  searchCriteria?: SearchCriteria;
}

function EmptyState({
  hasActiveFilters,
  searchCriteria,
}: {
  hasActiveFilters: boolean;
  searchCriteria?: SearchCriteria;
}) {
  // Arama kriterlerini açıklayıcı metne çevir
  const getSearchDescription = () => {
    if (!searchCriteria || !hasActiveFilters) {
      return 'Henüz hiç salon eklenmemiş.';
    }

    const parts: string[] = [];

    // Şehir ve ilçe bilgisi
    if (searchCriteria.city && searchCriteria.city !== 'all') {
      const city = CITIES.find(c => c.id.toString() === searchCriteria.city);
      if (city) {
        if (searchCriteria.district && searchCriteria.district !== 'all') {
          parts.push(`${searchCriteria.district}, ${city.name}`);
        } else {
          parts.push(city.name);
        }
      }
    }

    // Arama sorgusu
    if (searchCriteria.query) {
      parts.push(`"${searchCriteria.query}" araması`);
    }

    // Özellikler
    if (searchCriteria.features.length > 0) {
      const featureText =
        searchCriteria.features.length === 1
          ? `${searchCriteria.features[0]} özelliği`
          : `${searchCriteria.features.length} özellik`;
      parts.push(featureText);
    }

    if (parts.length === 0) {
      return 'Arama kriterlerinize uygun salon bulunamadı.';
    }

    const location = parts.find(
      part => !part.includes('araması') && !part.includes('özellik')
    );
    const otherCriteria = parts.filter(
      part => part.includes('araması') || part.includes('özellik')
    );

    let description = '';
    if (location) {
      description = `${location} bölgesinde`;
      if (otherCriteria.length > 0) {
        description += ` ${otherCriteria.join(' ve ')} için`;
      }
      description += ' spor salonu bulunamadı.';
    } else if (otherCriteria.length > 0) {
      description = `${otherCriteria.join(' ve ')} için spor salonu bulunamadı.`;
    } else {
      description = 'Arama kriterlerinize uygun salon bulunamadı.';
    }

    return description;
  };

  return (
    <div className="py-16 text-center">
      <div className="mx-auto max-w-md">
        <div className="bg-muted mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full">
          <Search className="text-muted-foreground h-12 w-12" />
        </div>
        <h3 className="mb-4 text-2xl font-semibold">Salon bulunamadı</h3>
        <p className="text-muted-foreground mb-6">{getSearchDescription()}</p>
        <p className="text-muted-foreground/80 mb-6 text-sm">
          Filtrelerinizi değiştirmeyi veya daha geniş bir alanda arama yapmayı
          deneyin.
        </p>
        {hasActiveFilters && (
          <Button asChild variant="outline">
            <Link href="/findGym">Filtreleri Temizle</Link>
          </Button>
        )}
      </div>
    </div>
  );
}

export function GymGrid({
  gyms,
  hasActiveFilters,
  searchCriteria,
}: GymGridProps) {
  return (
    <main className="min-w-0 flex-1 lg:col-span-3">
      {gyms.length === 0 ? (
        <EmptyState
          hasActiveFilters={hasActiveFilters}
          searchCriteria={searchCriteria}
        />
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {gyms.map((gym, index) => (
            <Suspense key={gym.slug} fallback={null}>
              <GymCard gym={gym} index={index} />
            </Suspense>
          ))}
        </div>
      )}
    </main>
  );
}
