/**
 * Constants for authentication operations
 * Following Clean Code principles - centralized constants to avoid magic numbers and strings
 */

// ============================================================================
// PASSWORD VALIDATION CONSTANTS
// ============================================================================

export const PASSWORD_VALIDATION = {
  MIN_LENGTH: 6,
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const AUTH_ERROR_MESSAGES = {
  // General validation
  EMAIL_AND_PASSWORD_REQUIRED: 'E-posta ve şifre gereklidir',
  PHONE_AND_PASSWORD_REQUIRED: 'Telefon numarası ve şifre gereklidir',
  EMAIL_REQUIRED: 'E-posta adresi gereklidir',
  PHONE_REQUIRED: 'Telefon numarası gereklidir',
  EMAIL_AND_TOKEN_REQUIRED: 'E-posta ve doğrulama kodu gereklidir',
  PHONE_AND_TOKEN_REQUIRED: 'Telefon numarası ve doğrulama kodu gereklidir',

  // Password validation
  PASSWORD_TOO_SHORT: 'Şifre en az 6 karakter olmalıdır',
  PASSWORDS_DO_NOT_MATCH: 'Şifreler eşleşmiyor',
  PASSWORD_AND_CONFIRM_REQUIRED: 'Şifre ve şifre onayı gereklidir',

  // Auth failures
  REGISTRATION_FAILED: 'Kayıt başarısız',
  LOGIN_FAILED: 'Giriş başarısız',
  EMAIL_VERIFICATION_FAILED: 'E-posta doğrulanamadı',
  PHONE_VERIFICATION_FAILED: 'Telefon doğrulanamadı',
  USER_CREATION_FAILED: 'Kullanıcı oluşturulamadı',
  PROFILE_CREATION_FAILED: 'Kullanıcı profili oluşturulamadı',
  PASSWORD_RESET_FAILED: 'Şifre sıfırlama başarısız',
  PASSWORD_UPDATE_FAILED: 'Şifre güncelleme başarısız',

  // Phone specific
  PHONE_ALREADY_REGISTERED: 'Bu telefon numarası zaten kayıtlı',
  PHONE_NOT_FOUND: 'Bu telefon numarası ile kayıtlı kullanıcı bulunamadı',
  PHONE_OR_PASSWORD_INCORRECT: 'Telefon numarası veya şifre hatalı',

  // Email specific
  INVALID_EMAIL_FORMAT: 'Geçerli bir e-posta adresi giriniz',

  // Session and reset
  INVALID_RESET_SESSION:
    'Şifre sıfırlama oturumu geçersiz. Lütfen yeni bir şifre sıfırlama linki talep edin.',

  // Google OAuth
  GOOGLE_OAUTH_FAILED: 'Google ile giriş başarısız. Lütfen tekrar deneyin.',
  GOOGLE_OAUTH_CANCELLED: 'Google ile giriş iptal edildi.',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const AUTH_SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Giriş başarılı',
  REGISTRATION_SUCCESS: 'Kayıt başarılı! Telefon doğrulama kodu gönderildi.',
  OTP_SENT: 'Doğrulama kodu gönderildi',
  OTP_RESENT: 'Doğrulama kodu yeniden gönderildi',
  PASSWORD_RESET_SENT: 'Şifre sıfırlama linki e-posta adresinize gönderildi',
  PASSWORD_UPDATED: 'Şifreniz başarıyla güncellendi',
  GOOGLE_OAUTH_SUCCESS: 'Google ile giriş başarılı',
} as const;

// ============================================================================
// AUTH CONFIGURATION
// ============================================================================

export const AUTH_CONFIG = {
  EMAIL_REDIRECT_TO: undefined, // No redirect for email signup
  SMS_CHANNEL: 'sms' as const,
  EMAIL_CONFIRM: true,
  PHONE_CONFIRM: true,
} as const;

// ============================================================================
// DATABASE DEFAULTS
// ============================================================================

export const AUTH_DATABASE_DEFAULTS = {
  EMPTY_FULL_NAME: '',
  NULL_VALUE: null,
  IS_GUEST_ACCOUNT: false,
} as const;
