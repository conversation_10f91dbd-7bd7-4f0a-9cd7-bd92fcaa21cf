'use server';

import { GymPackages } from '@/types/database/tables';
import { createAction } from '../../core/core';
import { createClient } from '@/lib/supabase/server';
import { Database } from '@/lib/supabase/types';
import { ApiResponse } from '@/types/global/api';

/**
 * Salon paketi oluşturur
 */
export async function createGymPackage(
  gymId: string,
  packageData: Omit<GymPackages, 'id' | 'created_at' | 'updated_at' | 'gym_id'>
): Promise<ApiResponse<GymPackages>> {
  return createAction<GymPackages>(
    async (_, supabase, _userId) => {
      // Paketi oluştur - RLS otomatik olarak gym erişimini kontrol edecek
      const { data: newPackage, error } = await supabase
        .from('gym_packages')
        .insert({
          ...packageData,
          gym_id: gymId,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Paket oluşturulurken hata: ${error.message}`);
      }

      return newPackage;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/packages`],
    }
  );
}

/**
 * Salon paketini günceller
 */
export async function updateGymPackage(
  gymId: string,
  packageId: string,
  packageData: Database['public']['Tables']['gym_packages']['Update']
): Promise<ApiResponse<GymPackages>> {
  return createAction<GymPackages>(
    async (_, supabase, _userId) => {
      const { data: updatedPackage, error } = await supabase
        .from('gym_packages')
        .update(packageData)
        .eq('id', packageId)
        .eq('gym_id', gymId) // RLS otomatik olarak gym erişimini kontrol edecek
        .select()
        .single();

      if (error) {
        throw new Error(`Paket güncellenirken hata: ${error.message}`);
      }

      return updatedPackage;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/packages`],
    }
  );
}

/**
 * Salon paketini siler
 */
export async function deleteGymPackage(
  gymId: string,
  packageId: string
): Promise<ApiResponse<void>> {
  return createAction<void>(
    async (_, supabase, _userId) => {
      // Paketi sil - RLS otomatik olarak gym erişimini kontrol edecek
      const { error } = await supabase
        .from('gym_packages')
        .delete()
        .eq('id', packageId)
        .eq('gym_id', gymId);

      if (error) {
        throw new Error(`Paket silinirken hata: ${error.message}`);
      }

      return undefined;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/packages`],
    }
  );
}

// helper functions
/**
 * Paket ID'lerinden paket isimlerini getiren helper function
 */
export async function getPackageNames(
  supabase: Awaited<ReturnType<typeof createClient>>,
  packageIds: string[]
): Promise<Map<string, string>> {
  if (packageIds.length === 0) {
    return new Map();
  }
  const { data: packages, error } = await supabase
    .from('gym_packages')
    .select('id, name')
    .in('id', packageIds);

  const packageMap = new Map<string, string>();

  if (!error && packages) {
    packages.forEach((pkg: { id: string; name: string }) => {
      packageMap.set(pkg.id, pkg.name);
    });
  }
  return packageMap;
}
