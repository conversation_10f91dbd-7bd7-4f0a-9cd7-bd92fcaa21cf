import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';

export const metadata: Metadata = {
  title: 'Üye Paneli | Sportiva',
  description:
    'Sportiva üye paneli. Üyeliklerinizi yönetin, ilerlemenizi takip edin.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function MemberLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Member Sidebar */}
      <DashboardSidebar mode="member" />
      {/* Main Content Area */}
      {children}
    </>
  );
}
