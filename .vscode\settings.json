{
  // TypeScript ayarları
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  // ESLint ayarları
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.run": "onType",
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  // Prettier ayarları
  "prettier.enable": true,
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,
  // Format on save
  "editor.formatOnSave": false,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "never",
    "source.organizeImports": "never",
    "source.removeUnusedImports": "never"
  },
  // File associations
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript",
    "*.css": "css",
    "*.scss": "scss",
    "*.sass": "sass",
    "*.less": "less"
  },
  // Tailwind CSS
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "typescriptreact": "typescriptreact"
  },
  // CSS Path Intellisense
  "css.validate": true,
  "css.lint.unknownAtRules": "ignore",
  "path-intellisense.mappings": {
    "@": "${workspaceRoot}/src",
    "@/components": "${workspaceRoot}/src/components",
    "@/lib": "${workspaceRoot}/src/lib",
    "@/hooks": "${workspaceRoot}/src/hooks",
    "@/types": "${workspaceRoot}/src/types",
    "@/store": "${workspaceRoot}/src/store",
    "@/utils": "${workspaceRoot}/src/lib/utils"
  },
  "tailwindCSS.experimental.classRegex": [
    [
      "cva\\(([^)]*)\\)",
      "[\"'`]([^\"'`]*).*?[\"'`]"
    ],
    [
      "cn\\(([^)]*)\\)",
      "[\"'`]([^\"'`]*).*?[\"'`]"
    ],
    [
      "clsx\\(([^)]*)\\)",
      "[\"'`]([^\"'`]*).*?[\"'`]"
    ]
  ],
  // Editor ayarları
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.rulers": [
    100,
    120
  ],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 100,
  // File explorer
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.next": true,
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true,
    "**/*.tsbuildinfo": true
  },
  // Search
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.next": true,
    "**/coverage": true,
    "**/*.min.js": true,
    "**/*.min.css": true
  },
  // Git
  "git.ignoreLimitWarning": true,
  "git.autofetch": true,
  // Emmet
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  // Auto save
  "files.autoSave": "onFocusChange",
  // Breadcrumbs
  "breadcrumbs.enabled": true,
  "breadcrumbs.symbolPath": "on",
  // Minimap
  "editor.minimap.enabled": true,
  // Terminal
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.fontSize": 14
}
