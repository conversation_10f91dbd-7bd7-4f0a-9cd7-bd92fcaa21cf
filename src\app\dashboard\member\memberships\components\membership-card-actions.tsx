'use client';

import { useState, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Activity, Pause, XCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  cancelMembership,
  updateMembershipStatus,
} from '@/lib/actions/all-actions';

interface MembershipCardActionsProps {
  membershipId: string;
  currentStatus: 'active' | 'passive';
  gymName: string;
}

export function MembershipCardActions({
  membershipId,
  currentStatus,
  gymName,
}: MembershipCardActionsProps) {
  const [isPending, startTransition] = useTransition();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const router = useRouter();

  const handleStatusChange = (newStatus: 'active' | 'passive') => {
    startTransition(async () => {
      try {
        const result = await updateMembershipStatus(membershipId, newStatus);

        if (result.success) {
          toast.success(result.data?.message || 'Üyelik durumu güncellendi');
          router.refresh();
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      } catch (error) {
        toast.error('Bir hata oluştu');
        console.error('Membership status update error:', error);
      }
    });
  };

  const handleCancelMembership = () => {
    startTransition(async () => {
      try {
        const result = await cancelMembership(membershipId);

        if (result.success) {
          toast.success(result.data?.message || 'Üyelik iptal edildi');
          router.refresh();
          setShowCancelDialog(false);
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      } catch (error) {
        toast.error('Bir hata oluştu');
        console.error('Membership cancel error:', error);
      }
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            disabled={isPending}
            className="h-8 w-8 p-0"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Menüyü aç</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {currentStatus === 'passive' ? (
            <DropdownMenuItem
              onClick={() => handleStatusChange('active')}
              disabled={isPending}
            >
              <Activity className="mr-2 h-4 w-4" />
              Üyeliği Aktifleştir
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              onClick={() => handleStatusChange('passive')}
              disabled={isPending}
            >
              <Pause className="mr-2 h-4 w-4" />
              Üyeliği Pasifleştir
            </DropdownMenuItem>
          )}

          {currentStatus === 'active' && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setShowCancelDialog(true)}
                disabled={isPending}
                className="text-red-600 focus:text-red-600"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Üyeliği İptal Et
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Üyeliği İptal Et</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>{gymName}</strong> üyeliğinizi iptal etmek istediğinizden
              emin misiniz? Bu işlem üyeliğinizi pasif duruma getirecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>Vazgeç</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelMembership}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending ? 'İptal ediliyor...' : 'Üyeliği İptal Et'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
