'use client';

import { useState, useTransition, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { User, CheckCircle, Activity, Target, TrendingUp } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { upsertMemberDetails } from '@/lib/actions/user/member-actions';
import { MemberDetails } from '@/types/database/tables';
import {
  FITNESS_GOAL_OPTIONS,
  getFitnessGoalDescription,
} from '@/lib/constants';

interface PhysicalInformationProps {
  memberDetails: MemberDetails | null;
}

export const PhysicalInformation = ({
  memberDetails,
}: PhysicalInformationProps) => {
  const [detailsFormData, setDetailsFormData] = useState({
    age: memberDetails?.age?.toString() || '',
    gender: memberDetails?.gender || '',
    height_cm: memberDetails?.height_cm?.toString() || '',
    weight_kg: memberDetails?.weight_kg?.toString() || '',
    fitness_goal: memberDetails?.fitness_goal || '',
  });

  // Loading states
  const [isDetailsPending, startDetailsTransition] = useTransition();

  // Message states
  const [detailsError, setDetailsError] = useState<string | undefined>();
  const [detailsSuccess, setDetailsSuccess] = useState<string | undefined>();

  const handleDetailsSubmit = async (formData: FormData) => {
    setDetailsError(undefined);
    setDetailsSuccess(undefined);

    startDetailsTransition(async () => {
      try {
        const result = await upsertMemberDetails(formData);

        if (result.success && result.data) {
          setDetailsSuccess('Detay bilgileri başarıyla güncellendi');
          setDetailsFormData({
            age: result.data.age?.toString() || '',
            gender: result.data.gender || '',
            height_cm: result.data.height_cm?.toString() || '',
            weight_kg: result.data.weight_kg?.toString() || '',
            fitness_goal: result.data.fitness_goal || '',
          });
        } else {
          setDetailsError(
            result.error || 'Detay bilgileri güncellenirken hata oluştu'
          );
        }
      } catch (err) {
        setDetailsError('Beklenmeyen bir hata oluştu');
        console.error('Details update error:', err);
      }
    });
  };

  const handleDetailsInputChange = useCallback(
    (field: string, value: string) => {
      setDetailsFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    },
    []
  );

  // BMI hesaplama ve kategori belirleme - memoized
  const { bmi, bmiCategory } = useMemo(() => {
    const height = parseFloat(detailsFormData.height_cm);
    const weight = parseFloat(detailsFormData.weight_kg);

    if (!height || !weight) {
      return { bmi: null, bmiCategory: null };
    }

    const heightInMeters = height / 100;
    const calculatedBMI = weight / (heightInMeters * heightInMeters);
    const bmiValue = calculatedBMI.toFixed(1);

    let category;
    if (calculatedBMI < 18.5) {
      category = {
        label: 'Zayıf',
        color: 'text-blue-600',
        bg: 'bg-blue-100',
        description:
          'Vücut kitle indeksiniz normal değerin altında. Sağlıklı kilo almak için beslenme uzmanına danışmanız önerilir.',
      };
    } else if (calculatedBMI < 25) {
      category = {
        label: 'Normal',
        color: 'text-green-600',
        bg: 'bg-green-100',
        description:
          'Vücut kitle indeksiniz ideal aralıkta. Mevcut kilonuzu korumaya odaklanın.',
      };
    } else if (calculatedBMI < 30) {
      category = {
        label: 'Fazla Kilolu',
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
        description:
          'Vücut kitle indeksiniz normal değerin üzerinde. Sağlıklı beslenme ve egzersiz programı önerilir.',
      };
    } else {
      category = {
        label: 'Obez',
        color: 'text-red-600',
        bg: 'bg-red-100',
        description:
          'Vücut kitle indeksiniz obezite kategorisinde. Sağlık uzmanından destek almanız önerilir.',
      };
    }

    return { bmi: bmiValue, bmiCategory: category };
  }, [detailsFormData.height_cm, detailsFormData.weight_kg]);

  return (
    <div className="space-y-8">
      {/* Status Messages */}
      {detailsError && (
        <div className="bg-destructive/10 border-destructive/20 text-destructive flex items-center gap-2 rounded-lg border p-4 text-sm">
          <div className="bg-destructive h-2 w-2 rounded-full" />
          {detailsError}
        </div>
      )}
      {detailsSuccess && (
        <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
          <CheckCircle className="h-4 w-4" />
          {detailsSuccess}
        </div>
      )}

      <form action={handleDetailsSubmit} className="space-y-6">
        {/* Temel Bilgiler */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Temel Bilgiler
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="age" className="text-sm font-medium">
                  Yaş
                </Label>
                <Input
                  id="age"
                  name="age"
                  type="number"
                  min="16"
                  max="120"
                  value={detailsFormData.age}
                  onChange={e =>
                    handleDetailsInputChange('age', e.target.value)
                  }
                  placeholder="Yaşınız"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender" className="text-sm font-medium">
                  Cinsiyet
                </Label>
                <Select
                  name="gender"
                  value={detailsFormData.gender}
                  onValueChange={value =>
                    handleDetailsInputChange('gender', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Cinsiyet seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Erkek</SelectItem>
                    <SelectItem value="female">Kadın</SelectItem>
                    <SelectItem value="other">Diğer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="from-primary/5 bg-gradient-to-r to-transparent">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="flex items-center gap-2 text-lg font-semibold">
                  <User className="text-primary h-5 w-5" />
                  Üye Kodunuz
                </h3>
                <div className="flex items-center gap-4">
                  <Badge
                    variant="outline"
                    className="px-4 py-2 font-mono text-lg"
                  >
                    { memberDetails?.invite_code}
                  </Badge>
                </div>
              </div>
              <div className="text-muted-foreground text-right text-sm">
                <div>Bu kod ile salonlara</div>
                <div>davet edilebilirsiniz</div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Fiziksel Ölçümler ve BMI */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Fiziksel Ölçümler
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Boy ve Kilo Girişi */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="height_cm" className="text-sm font-medium">
                  Boy (cm)
                </Label>
                <Input
                  id="height_cm"
                  name="height_cm"
                  type="number"
                  min="100"
                  max="250"
                  value={detailsFormData.height_cm}
                  onChange={e =>
                    handleDetailsInputChange('height_cm', e.target.value)
                  }
                  placeholder="Örn: 175"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="weight_kg" className="text-sm font-medium">
                  Kilo (kg)
                </Label>
                <Input
                  id="weight_kg"
                  name="weight_kg"
                  type="number"
                  min="30"
                  max="300"
                  step="0.1"
                  value={detailsFormData.weight_kg}
                  onChange={e =>
                    handleDetailsInputChange('weight_kg', e.target.value)
                  }
                  placeholder="Örn: 70"
                />
              </div>
            </div>

            {/* BMI Gösterimi */}
            {detailsFormData.height_cm && detailsFormData.weight_kg && (
              <div className="border-t pt-6">
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <TrendingUp className="text-primary h-5 w-5" />
                    <span className="font-medium">
                      Vücut Kitle İndeksi (BMI)
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-primary text-2xl font-bold">{bmi}</div>
                    {bmiCategory && (
                      <Badge
                        className={`${bmiCategory.bg} ${bmiCategory.color} border-0`}
                      >
                        {bmiCategory.label}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* BMI Açıklama */}
                {bmiCategory && (
                  <div className="text-muted-foreground bg-muted/30 rounded-lg p-3 text-sm">
                    <p>{bmiCategory.description}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Fitness Hedefi */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Fitness Hedefi
            </CardTitle>
            <CardDescription>Ana fitness hedefinizi seçin</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fitness_goal" className="text-sm font-medium">
                  Hedefiniz
                </Label>
                <Select
                  name="fitness_goal"
                  value={detailsFormData.fitness_goal}
                  onValueChange={value =>
                    handleDetailsInputChange('fitness_goal', value)
                  }
                >
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Fitness hedefinizi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {FITNESS_GOAL_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.emoji} {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-muted-foreground text-xs">
                  Bu hedef size özel antrenman programları önerilmesinde
                  kullanılır
                </p>
              </div>

              {/* Hedef açıklamaları */}
              {detailsFormData.fitness_goal && (
                <div className="bg-muted/50 rounded-lg p-4">
                  <h4 className="mb-2 text-sm font-medium">
                    Seçtiğiniz Hedef Hakkında:
                  </h4>
                  <p className="text-muted-foreground text-xs">
                    {getFitnessGoalDescription(
                      detailsFormData.fitness_goal as any
                    )}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 sm:flex-row sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setDetailsFormData({
                age: memberDetails?.age?.toString() || '',
                gender: memberDetails?.gender || '',
                height_cm: memberDetails?.height_cm?.toString() || '',
                weight_kg: memberDetails?.weight_kg?.toString() || '',
                fitness_goal: memberDetails?.fitness_goal || '',
              });
              setDetailsError(undefined);
              setDetailsSuccess(undefined);
            }}
            disabled={isDetailsPending}
            className="sm:w-auto"
          >
            Değişiklikleri İptal Et
          </Button>
          <Button
            type="submit"
            disabled={isDetailsPending}
            className="min-w-[200px] sm:w-auto"
          >
            {isDetailsPending ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Kaydediliyor...
              </div>
            ) : (
              'Fiziksel Bilgileri Kaydet'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
