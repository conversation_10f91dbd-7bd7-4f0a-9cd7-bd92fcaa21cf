'use server';

import { ApiResponse } from '@/types';
import { Notifications } from '@/types/database/tables';
import { createAction, createAdminAction } from '../core/core';
import {
  baseNotificationSchema,
  markNotificationReadSchema,
  deleteNotificationSchema,
  deleteAllNotificationsSchema,
} from '../core/schemas';
import { CORE_ERROR_MESSAGES } from '../core/core-constants';
// CORE_ERROR_MESSAGES içinde olmayan anahtarlar için fallback sabitleri
const ERRORS = {
  VALIDATION_FAILED: CORE_ERROR_MESSAGES.VALIDATION_FAILED ?? 'Geçersiz veri',
  ACCESS_DENIED: '<PERSON><PERSON><PERSON><PERSON> reddedildi',
  FETCH_FAILED: 'Veri getirilemedi',
  CREATE_FAILED: 'Oluşturma başarısız',
  UPDATE_FAILED: '<PERSON><PERSON><PERSON><PERSON><PERSON> başarısız',
  DELETE_FAILED: '<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>',
  TARGET_NOT_FOUND: '<PERSON><PERSON><PERSON> bulu<PERSON>',
  INVALID_OPERATION: 'Geçersiz işlem',
};

export type Notification = Notifications;

export interface BaseNotificationParams {
  recipientUserId?: string;
  gymId?: string;
  title: string;
  message: string;
}

export interface GymNotificationData {
  recipientId: string;
  action: 'request' | 'accept' | 'reject' | 'invite' | 'cancel';
  direction: 'to_manager' | 'to_member' | 'to_trainer';
  gymName: string;
  userName: string;
  invitationId: string;
}

// Gym entity notifications (gym-scoped)
export interface GymEntityNotificationParams {
  gymId: string;
  title: string;
  message: string;
}

/**
 * Core notification creator - createAction pattern
 */
export async function createNotification(
  params: BaseNotificationParams
): Promise<ApiResponse<Notification>> {
  return await createAdminAction(
    async (_, _supabase, _userId, adminClient) => {
      const {
        recipientUserId,
        gymId,
        title,
        message,
      } = params;

      // Hem recipientUserId hem gymId boş olamaz
      if (!recipientUserId && !gymId) {
        throw new Error('recipientUserId veya gymId belirtilmelidir');
      }

      // Eğer recipientUserId varsa kullanıcı kontrolü yap
      if (recipientUserId) {
        const { data: recipient, error: recipientError } = await adminClient
          .from('profiles')
          .select('id')
          .eq('id', recipientUserId)
          .single();

        if (recipientError || !recipient) {
          throw new Error(ERRORS.TARGET_NOT_FOUND);
        }
      }

      // Eğer gymId varsa salon kontrolü yap
      if (gymId) {
        const { data: gym, error: gymError } = await adminClient
          .from('gyms')
          .select('id')
          .eq('id', gymId)
          .single();

        if (gymError || !gym) {
          throw new Error(ERRORS.TARGET_NOT_FOUND);
        }
      }

      const { data: notification, error } = await adminClient
        .from('notifications')
        .insert({
          user_id: recipientUserId || null,
          gym_id: gymId || null,
          title,
          message,
          is_read: false,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`${ERRORS.CREATE_FAILED}: ${error.message}`);
      }

      return notification;
  });
}




/**
 * Fetch notifications for a specific Gym (manager-only)
 */
export async function getGymNotifications(gymId: string, limit?: number): Promise<ApiResponse<Notification[]>> {
  return await createAction(async (_, supabase) => {
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('gym_id', gymId)
      .order('created_at', { ascending: false })
      .limit(limit || 10);

    if (error) {
      throw new Error(`${ERRORS.FETCH_FAILED}: ${error.message}`);
    }

    return notifications || [];
  });
}

/**
 * Okunmamış salon bildirimi sayısı (manager-only)
 */
export async function getGymUnreadNotificationCount(gymId: string): Promise<ApiResponse<number>> {
  return await createAction(async (_, supabase) => {
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('gym_id', gymId)
      .eq('is_read', false);

    if (error) {
      throw new Error(`${ERRORS.FETCH_FAILED}: ${error.message}`);
    }

    return count || 0;
  });
}

/**
 * Yöneticinin erişebildiği tüm salonlara ait okunmamış bildirim sayısı (RLS ile sınırlı)
 */
export async function getManagerGymUnreadNotificationCount(): Promise<ApiResponse<number>> {
  return await createAction(async (_, supabase) => {
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .not('gym_id', 'is', null)
      .eq('is_read', false);

    if (error) {
      throw new Error(`${ERRORS.FETCH_FAILED}: ${error.message}`);
    }

    return count || 0;
  });
}

/**
 * Tek bir salon bildirimini okundu işaretle (manager-only, paylaşımlı okundu durumu)
 */
export async function markGymNotificationAsRead(notificationId: string, gymId: string): Promise<ApiResponse<null>> {
  return await createAction(async (_, supabase) => {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)
      .eq('gym_id', gymId);

    if (error) {
      throw new Error(`${ERRORS.UPDATE_FAILED}: ${error.message}`);
    }

    return null;
  });
}

/**
 * Bulk notifications creator - createAction pattern
 */
export async function createBulkNotifications(
  notifications: BaseNotificationParams[]
): Promise<ApiResponse<Notification[]>> {
  return await createAdminAction(
    async (_, __, ___, adminClient) => {
      if (!notifications.length) {
        throw new Error(ERRORS.VALIDATION_FAILED);
      }

      for (const notification of notifications) {
        const validation = baseNotificationSchema.safeParse(notification);
        if (!validation.success) {
          throw new Error(
            `${ERRORS.VALIDATION_FAILED}: ${validation.error.issues[0]?.message}`
          );
        }
      }

      const insertData = notifications.map(n => ({
        user_id: n.recipientUserId,
        title: n.title,
        message: n.message,
        is_read: false,
      }));

      const { data, error } = await adminClient
        .from('notifications')
        .insert(insertData)
        .select();

      if (error) {
        throw new Error(`${ERRORS.CREATE_FAILED}: ${error.message}`);
      }

      return data;
    },
    {
      revalidatePaths: ['/dashboard/notifications'],
    }
  );
}

/**
 * Kullanıcının bildirimlerini getirir - createAction pattern
 */
export async function getUserNotifications(limit?: number): Promise<ApiResponse<Notification[]>> {
  return await createAction(async (_, supabase,  authUserId) => {
      const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', authUserId)
      .order('created_at', { ascending: false })
      .limit(limit || 10);

    if (error) {
      throw new Error(`${ERRORS.FETCH_FAILED}: ${error.message}`);
    }

    return notifications || [];
  });
}

/**
 * Bildirimi okundu olarak işaretler - createAction pattern
 */
export async function markNotificationAsRead(params: {
  notificationId: string;
  userId: string;
}): Promise<ApiResponse<null>> {
  return await createAction(
    async (_, supabase, currentUserId) => {
      // Validate input using schema
      const validation = markNotificationReadSchema.safeParse(params);
      if (!validation.success) {
        throw new Error(validation.error.issues[0]?.message || ERRORS.VALIDATION_FAILED);
      }

      const { notificationId, userId } = validation.data;

      // Users can only mark their own notifications as read
      if (currentUserId !== userId) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`${ERRORS.UPDATE_FAILED}: ${error.message}`);
      }

      return null;
    },
    {
      revalidatePaths: ['/dashboard/notifications'],
    }
  );
}

/**
 * Tüm bildirimleri okundu olarak işaretler - createAction pattern
 */
export async function markAllNotificationsAsRead(
  userId: string
): Promise<ApiResponse<null>> {
  return await createAction(
    async (_, supabase, currentUserId) => {
      // Users can only mark their own notifications as read
      if (currentUserId !== userId) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) {
        throw new Error(`${ERRORS.UPDATE_FAILED}: ${error.message}`);
      }

      return null;
    },
    {
      revalidatePaths: ['/dashboard/notifications'],
    }
  );
}

/**
 * Okunmamış bildirim sayısını getirir - createAction pattern
 */
export async function getUnreadNotificationCount(
  userId: string
): Promise<ApiResponse<number>> {
  return await createAction(async (_, supabase, currentUserId) => {
    // Users can only access their own notification count
    if (currentUserId !== userId) {
      throw new Error(ERRORS.ACCESS_DENIED);
    }

    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      throw new Error(`${ERRORS.FETCH_FAILED}: ${error.message}`);
    }

    return count || 0;
  });
}

/**
 * Tek bir bildirimi siler - createAction pattern
 */
export async function deleteNotification(params: {
  notificationId: string;
  userId: string;
}): Promise<ApiResponse<null>> {
  return await createAction(
    async (_, supabase, currentUserId) => {
      // Validate input using schema
      const validation = deleteNotificationSchema.safeParse(params);
      if (!validation.success) {
        throw new Error(validation.error.issues[0]?.message || ERRORS.VALIDATION_FAILED);
      }

      const { notificationId, userId } = validation.data;

      // Users can only delete their own notifications
      if (currentUserId !== userId) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`${ERRORS.DELETE_FAILED}: ${error.message}`);
      }

      return null;
    },
    {
      revalidatePaths: ['/dashboard/notifications'],
    }
  );
}

/**
 * Kullanıcının tüm bildirimlerini siler - createAction pattern
 */
export async function deleteAllNotifications(
  userId: string
): Promise<ApiResponse<null>> {
  return await createAction(
    async (_, supabase, currentUserId) => {
      // Validate input using schema
      const validation = deleteAllNotificationsSchema.safeParse({ userId });
      if (!validation.success) {
        throw new Error(validation.error.issues[0]?.message || ERRORS.VALIDATION_FAILED);
      }

      const { userId: validatedUserId } = validation.data;

      // Users can only delete their own notifications
      if (currentUserId !== validatedUserId) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', validatedUserId);

      if (error) {
        throw new Error(`${ERRORS.DELETE_FAILED}: ${error.message}`);
      }

      return null;
    },
    {
      revalidatePaths: ['/dashboard/notifications'],
    }
  );
}
