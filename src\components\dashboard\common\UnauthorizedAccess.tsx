import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface UnauthorizedAccessProps {
  reason: string;
}

export function UnauthorizedAccess({ reason }: UnauthorizedAccessProps) {
  return (
    <div className="flex min-h-[400px] items-center justify-center">
      <div className="space-y-4 text-center">
        <h2 className="text-xl font-semibold">
          Bu sayfaya erişim yetkiniz yok
        </h2>
        <p className="text-muted-foreground">{reason}</p>
        <Button asChild>
          <Link href="/dashboard">Panel&apos;e <PERSON>ön</Link>
        </Button>
      </div>
    </div>
  );
}
