'use client';

import { useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';

export function OnboardingToastHandler() {
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const message = searchParams.get('message');
    const toastType = searchParams.get('toast'); // optional: success | error | info

    if (message) {
      if (toastType === 'error') {
        toast.error(message);
      } else if (toastType === 'success') {
        toast.success(message);
      } else if (toastType === 'info') {
        toast.info(message);
      } else {
        toast(message);
      }

      // URL'den parametreleri temizle (yenilemeden)
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('message');
      newUrl.searchParams.delete('toast');
      // Toast'ın görünmes<PERSON> iç<PERSON> küçük bir gecik<PERSON> ekle
      setTimeout(() => {
        router.replace(newUrl.pathname + newUrl.search, { scroll: false });
      }, 100);
    }
  }, [searchParams, router]);

  return null;
}
