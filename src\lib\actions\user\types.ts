// Trainer related types
export interface TrainerGym {
  gym_id: string;
  gym_name: string;
  gym_address: string;
  gym_city: string | null;
  gym_district: string | null;
  status: string;
  left_at: string | null;
  created_at: string;
}

export interface ManagerStatusData {
  isManager: boolean;
  tier?: string;
  packageType?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  status?: string;
  daysRemaining?: number;
}

export interface PublicMembershipData {
  id: string;
  gym_id: string;
  status: 'active' | 'passive';
  approved_at: string | null;
  created_at: string;
  updated_at: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
}

export interface MembershipCheckResult {
  hasActiveMembership: boolean;
  membership: PublicMembershipData | null;
}
