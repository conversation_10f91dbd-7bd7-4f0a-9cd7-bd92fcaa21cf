export default function GymDetailLoading() {
  return (
    <div className="-mt-25 min-h-screen">
      {/* Hero Section Skeleton - Landing Page Style */}
      <section className="relative flex min-h-screen items-center justify-center overflow-hidden">
        {/* Background Skeleton */}
        <div className="absolute inset-0 z-0">
          <div className="from-muted/60 via-muted/40 to-muted/60 h-full w-full bg-gradient-to-br" />
        </div>

        {/* Main Content Skeleton */}
        <div className="container relative z-10 mx-auto px-4 py-20">
          <div className="mx-auto max-w-4xl space-y-8 text-center">
            {/* Gym Logo Skeleton */}
            <div className="mb-8 flex justify-center">
              <div className="bg-background/20 border-background/20 h-32 w-32 animate-pulse rounded-full border-4" />
            </div>

            {/* Main Headline Skeleton */}
            <div className="space-y-4">
              <div className="bg-background/20 mx-auto h-16 max-w-2xl animate-pulse rounded-2xl md:h-20" />
              <div className="bg-background/15 mx-auto h-8 max-w-3xl animate-pulse rounded-xl md:h-10" />
            </div>

            {/* Location Skeleton */}
            <div className="flex items-center justify-center gap-2">
              <div className="bg-background/20 h-6 w-6 animate-pulse rounded" />
              <div className="bg-background/15 h-6 w-48 animate-pulse rounded" />
            </div>

            {/* CTA Button Skeleton */}
            <div className="pt-8">
              <div className="bg-primary/30 mx-auto h-14 w-48 animate-pulse rounded-2xl" />
            </div>

            {/* Trust Indicators Skeleton */}
            <div className="mx-auto grid max-w-3xl grid-cols-1 gap-8 pt-12 md:grid-cols-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2 text-center">
                  <div className="bg-background/20 mx-auto mb-4 h-16 w-16 animate-pulse rounded-full" />
                  <div className="bg-background/20 mx-auto h-8 w-16 animate-pulse rounded" />
                  <div className="bg-background/15 mx-auto h-5 w-24 animate-pulse rounded" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Scroll Indicator Skeleton */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 transform">
          <div className="border-background/30 h-10 w-6 animate-pulse rounded-full border-2" />
        </div>
      </section>

      {/* Quick Info Bar Skeleton */}
      <section className="bg-background border-border border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-wrap items-center justify-center gap-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="bg-primary/30 h-5 w-5 animate-pulse rounded" />
                <div className="bg-muted h-5 w-32 animate-pulse rounded" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section Skeleton - Landing Page Style */}
      <section className="bg-muted/30 py-20">
        <div className="container mx-auto px-4">
          {/* Section Header Skeleton */}
          <div className="mb-16 text-center">
            <div className="bg-muted mx-auto mb-6 h-12 max-w-md animate-pulse rounded-2xl md:h-14" />
            <div className="bg-muted/70 mx-auto h-6 max-w-3xl animate-pulse rounded-xl" />
          </div>

          <div className="grid items-center gap-16 lg:grid-cols-2">
            {/* About Content Skeleton */}
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="bg-muted h-10 max-w-xs animate-pulse rounded-xl" />
                <div className="space-y-3">
                  <div className="bg-muted/70 h-5 w-full animate-pulse rounded" />
                  <div className="bg-muted/70 h-5 w-full animate-pulse rounded" />
                  <div className="bg-muted/70 h-5 w-4/5 animate-pulse rounded" />
                </div>
              </div>

              {/* Key Benefits Skeleton */}
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-start gap-4">
                    <div className="bg-primary/20 mt-1 h-8 w-8 flex-shrink-0 animate-pulse rounded-full" />
                    <div className="flex-1 space-y-2">
                      <div className="bg-muted/70 h-5 w-40 animate-pulse rounded" />
                      <div className="bg-muted/50 h-4 w-full animate-pulse rounded" />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Features Skeleton */}
            <div className="space-y-8">
              <div className="bg-muted h-10 max-w-xs animate-pulse rounded-xl" />

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div
                    key={i}
                    className="bg-card border-border animate-pulse rounded-2xl border p-6 shadow-sm"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/20 h-10 w-10 animate-pulse rounded-xl" />
                      <div className="bg-muted/70 h-5 flex-1 animate-pulse rounded" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Packages Section Skeleton - Landing Page Style */}
      <section className="bg-background py-20">
        <div className="container mx-auto px-4">
          {/* Section Header Skeleton */}
          <div className="mb-16 text-center">
            <div className="bg-muted mx-auto mb-6 h-12 max-w-md animate-pulse rounded-2xl md:h-14" />
            <div className="bg-muted/70 mx-auto h-6 max-w-3xl animate-pulse rounded-xl" />
          </div>

          <div className="mx-auto grid max-w-7xl gap-8 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="bg-card border-border relative animate-pulse rounded-3xl border-2 p-8 shadow-lg"
              >
                {/* Popular Badge Skeleton */}
                {i === 1 && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2 transform">
                    <div className="bg-primary/30 h-8 w-24 animate-pulse rounded-full px-6 py-2" />
                  </div>
                )}

                {/* Package Header Skeleton */}
                <div className="mb-8 text-center">
                  <div className="bg-muted mx-auto mb-4 h-16 w-16 animate-pulse rounded-2xl" />
                  <div className="bg-muted mx-auto mb-2 h-8 max-w-32 animate-pulse rounded-xl" />
                  <div className="bg-muted/70 mx-auto h-5 max-w-20 animate-pulse rounded" />
                </div>

                {/* Price Skeleton */}
                <div className="mb-8 text-center">
                  <div className="flex items-baseline justify-center gap-1">
                    <div className="bg-muted h-12 w-24 animate-pulse rounded-xl" />
                    <div className="bg-muted/70 h-8 w-8 animate-pulse rounded" />
                  </div>
                  <div className="bg-muted/50 mx-auto mt-2 h-5 w-16 animate-pulse rounded" />
                </div>

                {/* Description Skeleton */}
                <div className="mb-8">
                  <div className="space-y-2">
                    <div className="bg-muted/70 h-4 w-full animate-pulse rounded" />
                    <div className="bg-muted/70 mx-auto h-4 w-3/4 animate-pulse rounded" />
                  </div>
                </div>

                {/* Features Skeleton */}
                <div className="mb-8 space-y-4">
                  {Array.from({ length: 3 }).map((_, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <div className="h-5 w-5 flex-shrink-0 animate-pulse rounded-full bg-green-500/20" />
                      <div className="bg-muted/70 h-4 flex-1 animate-pulse rounded" />
                    </div>
                  ))}
                </div>

                {/* CTA Button Skeleton */}
                <div className="bg-primary/30 h-12 animate-pulse rounded-2xl" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section Skeleton - Landing Page Style */}
      <section className="bg-muted text-foreground py-20">
        <div className="container mx-auto px-4">
          {/* Section Header Skeleton */}
          <div className="mb-16 text-center">
            <div className="bg-background/20 mx-auto mb-6 h-12 max-w-md animate-pulse rounded-2xl md:h-14" />
            <div className="bg-background/15 mx-auto h-6 max-w-3xl animate-pulse rounded-xl" />
          </div>

          <div className="mx-auto max-w-6xl">
            <div className="grid items-center gap-16 lg:grid-cols-2">
              {/* Contact Info Skeleton */}
              <div className="space-y-8">
                <div className="bg-background/20 mb-8 h-10 max-w-xs animate-pulse rounded-xl" />

                <div className="space-y-6">
                  {/* Address Skeleton */}
                  <div className="flex items-start gap-4">
                    <div className="bg-primary/20 h-12 w-12 flex-shrink-0 animate-pulse rounded-2xl" />
                    <div className="flex-1 space-y-2">
                      <div className="bg-background/20 h-6 w-24 animate-pulse rounded" />
                      <div className="space-y-1">
                        <div className="bg-background/15 h-5 w-48 animate-pulse rounded" />
                        <div className="bg-background/15 h-5 w-32 animate-pulse rounded" />
                      </div>
                    </div>
                  </div>

                  {/* Phone Skeleton */}
                  <div className="flex items-start gap-4">
                    <div className="bg-primary/20 h-12 w-12 flex-shrink-0 animate-pulse rounded-2xl" />
                    <div className="flex-1 space-y-2">
                      <div className="bg-background/20 h-6 w-20 animate-pulse rounded" />
                      <div className="bg-background/15 h-5 w-36 animate-pulse rounded" />
                    </div>
                  </div>

                  {/* Working Hours Skeleton */}
                  <div className="flex items-start gap-4">
                    <div className="bg-primary/20 h-12 w-12 flex-shrink-0 animate-pulse rounded-2xl" />
                    <div className="flex-1 space-y-2">
                      <div className="bg-background/20 h-6 w-32 animate-pulse rounded" />
                      <div className="space-y-1">
                        <div className="bg-background/15 h-4 w-48 animate-pulse rounded" />
                        <div className="bg-background/15 h-4 w-44 animate-pulse rounded" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* CTA Card Skeleton */}
              <div className="bg-card animate-pulse rounded-3xl p-8">
                <div className="mb-8 text-center">
                  <div className="bg-primary/20 mx-auto mb-4 h-16 w-16 animate-pulse rounded-2xl" />
                  <div className="bg-muted mx-auto mb-4 h-8 max-w-48 animate-pulse rounded-xl" />
                  <div className="space-y-2">
                    <div className="bg-muted/70 h-4 w-full animate-pulse rounded" />
                    <div className="bg-muted/70 mx-auto h-4 w-4/5 animate-pulse rounded" />
                  </div>
                </div>

                {/* Quick Contact Options Skeleton */}
                <div className="mb-8 space-y-4">
                  <div className="bg-primary/30 h-14 animate-pulse rounded-2xl" />
                  <div className="bg-muted h-14 animate-pulse rounded-2xl" />
                </div>

                {/* Additional Info Skeleton */}
                <div className="border-border border-t pt-6">
                  <div className="bg-muted/50 mx-auto h-4 max-w-64 animate-pulse rounded" />
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA Skeleton */}
          <div className="border-border mt-16 border-t pt-16 text-center">
            <div className="bg-background/20 mx-auto mb-4 h-8 max-w-md animate-pulse rounded-xl" />
            <div className="bg-background/15 mx-auto mb-8 h-5 max-w-2xl animate-pulse rounded" />
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <div className="bg-primary/30 h-14 w-48 animate-pulse rounded-2xl" />
              <div className="bg-background/20 h-14 w-48 animate-pulse rounded-2xl" />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
