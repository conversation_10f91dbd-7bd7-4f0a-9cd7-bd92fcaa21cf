/**
 * Centralized logging utility for the application
 * Provides structured logging with different levels and proper error handling
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  [key: string]: unknown;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private isClient = typeof window !== 'undefined';

  /**
   * Log debug information (only in development)
   */
  debug(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      this.log('debug', message, context);
    }
  }

  /**
   * Log general information
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * Log warnings
   */
  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  /**
   * Log errors
   */
  error(message: string, error?: Error | unknown, context?: LogContext): void {
    const errorContext: LogContext = {
      ...context,
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack,
            }
          : error,
    };

    this.log('error', message, errorContext);

    // In production, you might want to send errors to an external service
    // Example: Sentry, LogRocket, etc.
    if (!this.isDevelopment && this.isClient) {
      // TODO: Integrate with error tracking service
      // Example: Sentry.captureException(error);
    }
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      level,
      message,
      context,
      environment: process.env.NODE_ENV,
      client: this.isClient,
    };

    // In development, use console for immediate feedback
    if (this.isDevelopment) {
      if (level === 'error' || level === 'warn') {
        // eslint-disable-next-line no-console
        console[level](
          `[${timestamp}] ${level.toUpperCase()}: ${message}`,
          context || ''
        );
      }
      return;
    }

    // In production, you might want to send logs to an external service
    // For now, we'll use console but with structured data
    if (level === 'error' || level === 'warn') {
      // eslint-disable-next-line no-console
      console[level](JSON.stringify(logData));
    }
  }

  /**
   * Log authentication events
   */
  auth(event: string, userId?: string, context?: LogContext): void {
    this.info(`Auth: ${event}`, {
      ...context,
      userId,
      category: 'authentication',
    });
  }

  /**
   * Log API requests/responses
   */
  api(
    method: string,
    endpoint: string,
    status?: number,
    context?: LogContext
  ): void {
    const level = status && status >= 400 ? 'error' : 'info';
    this.log(level, `API: ${method} ${endpoint}`, {
      ...context,
      method,
      endpoint,
      status,
      category: 'api',
    });
  }

  /**
   * Log database operations
   */
  database(operation: string, table?: string, context?: LogContext): void {
    this.debug(`DB: ${operation}`, {
      ...context,
      operation,
      table,
      category: 'database',
    });
  }

  /**
   * Log performance metrics
   */
  performance(
    metric: string,
    value: number,
    unit: string = 'ms',
    context?: LogContext
  ): void {
    this.info(`Performance: ${metric}`, {
      ...context,
      metric,
      value,
      unit,
      category: 'performance',
    });
  }
}

// Export singleton instance
export const logger = new Logger();

// Export types for external use
export type { LogLevel, LogContext };
