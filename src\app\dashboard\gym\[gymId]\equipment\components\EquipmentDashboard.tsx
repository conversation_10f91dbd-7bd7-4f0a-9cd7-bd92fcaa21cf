'use client';

import { useState } from 'react';
import { Plus, Filter, Search, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

import {
  EquipmentListResponse,
  EquipmentCategory,
  EquipmentFilters,
  GymEquipment
} from '@/types/database/equipment-inventory';
import { EquipmentList } from './EquipmentList';
import { EquipmentFiltersPanel } from './EquipmentFiltersPanel';
import { EquipmentStatsCards } from './EquipmentStatsCards';
import { CreateEquipmentDialog } from './CreateEquipmentDialog';
import { EditEquipmentDialog } from './EditEquipmentDialog';

interface EquipmentDashboardProps {
  gymId: string;
  equipmentData: EquipmentListResponse;
  categories: EquipmentCategory[];
  initialFilters: EquipmentFilters;
}

export function EquipmentDashboard({
  gymId,
  equipmentData,
  categories,
  initialFilters,
}: EquipmentDashboardProps) {
  const [filters, setFilters] = useState<EquipmentFilters>(initialFilters);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedEquipment, setSelectedEquipment] = useState<GymEquipment | null>(null);
  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');

  const { equipment, analytics } = equipmentData;

  const handleFilterChange = (newFilters: EquipmentFilters) => {
    setFilters(newFilters);
    // Update URL params
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) params.set(key, value.toString());
    });
    window.history.replaceState({}, '', `?${params.toString()}`);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    handleFilterChange({ ...filters, search: query });
  };

  const exportEquipment = () => {
    // TODO: Implement CSV export
  };

  const handleEditEquipment = (equipment: GymEquipment) => {
    setSelectedEquipment(equipment);
    setShowEditDialog(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ekipmanlar</h1>
          <p className="text-muted-foreground">
            Spor salonu ekipmanlarını yönetin ve bakım takibi yapın
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtreler
          </Button>
          <Button
            variant="outline"
            onClick={exportEquipment}
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Dışa Aktar
          </Button>
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Yeni Ekipman
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <EquipmentStatsCards analytics={analytics} />

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Ekipman ara (ad, marka, model)..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {showFilters && (
          <EquipmentFiltersPanel
            filters={filters}
            categories={categories}
            onFiltersChange={handleFilterChange}
          />
        )}
      </div>

      {/* Active Filters */}
      {Object.values(filters).some(Boolean) && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-muted-foreground">Aktif filtreler:</span>
          {filters.category_id && (
            <Badge variant="secondary" className="gap-1">
              Kategori: {categories.find(c => c.id === filters.category_id)?.name}
              <button
                onClick={() => handleFilterChange({ ...filters, category_id: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.condition && (
            <Badge variant="secondary" className="gap-1">
              Durum: {filters.condition}
              <button
                onClick={() => handleFilterChange({ ...filters, condition: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.status && (
            <Badge variant="secondary" className="gap-1">
              Statü: {filters.status}
              <button
                onClick={() => handleFilterChange({ ...filters, status: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.location && (
            <Badge variant="secondary" className="gap-1">
              Konum: {filters.location}
              <button
                onClick={() => handleFilterChange({ ...filters, location: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Arama: {filters.search}
              <button
                onClick={() => handleFilterChange({ ...filters, search: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setFilters({});
              setSearchQuery('');
              window.history.replaceState({}, '', window.location.pathname);
            }}
            className="h-6 px-2 text-xs"
          >
            Tümünü Temizle
          </Button>
        </div>
      )}

      {/* Equipment List */}
      <EquipmentList
        equipment={equipment}
        categories={categories}
        onCreateEquipment={() => setShowCreateDialog(true)}
        onEditEquipment={handleEditEquipment}
      />

      {/* Create Equipment Dialog */}
      <CreateEquipmentDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        gymId={gymId}
        categories={categories}
      />

      {/* Edit Equipment Dialog */}
      <EditEquipmentDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        equipment={selectedEquipment}
        categories={categories}
      />


    </div>
  );
}
