{
  "recommendations": [
    // TypeScript ve JavaScript
    "ms-vscode.vscode-typescript-next",
    
    // ESLint ve Prettier
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    
    // React ve Next.js
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    
    // Git
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    
    // Utility
    "christian-kohler.path-intellisense",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-json",
    
    // Code quality
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    
    // Productivity
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify"
  ]
}
