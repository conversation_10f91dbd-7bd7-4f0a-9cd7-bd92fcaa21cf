import { Check, Zap, ArrowRight, Phone } from "lucide-react";
import Link from "next/link";

export function PricingCTA() {
  const benefits = [
    "Kurulum desteği dahil",
    "İstediğiniz zaman iptal",
    "7/24 müşteri desteği",
    "Güvenli ödeme sistemi"
  ];

  return (
    <section className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-background rounded-2xl p-8 md:p-12 border border-border shadow-lg">
            <div className="text-center">
              {/* Main Heading */}
              <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                He<PERSON> Başlamaya Hazır mısınız?
              </h2>
              
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                Spor salonunuzu dijitalleştirin ve verimliliğinizi artırın.
                Size uygun paketi seçin ve hemen başlayın.
              </p>

              {/* Benefits */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8 max-w-2xl mx-auto">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3 text-left">
                    <Check className="h-5 w-5 text-primary flex-shrink-0" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </div>
                ))}
              </div>


              {/* Additional Info */}
              <div className="space-y-4 text-sm text-muted-foreground">
                <p>
                  <strong className="text-foreground">Kredi kartı gerekmez.</strong> 
                  {" "}14 gün sonunda otomatik ücretlendirme yapılmaz.
                </p>
                
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4 border-t border-border">
                  <span>Hala kararsız mısınız?</span>
                  <Link 
                    href="/about" 
                    className="inline-flex items-center gap-1 text-primary hover:underline"
                  >
                    Müşteri hikayelerini okuyun
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 text-center">
            <p className="text-sm text-muted-foreground mb-6">
              500+ spor salonu tarafından güveniliyor
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-3">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Check className="h-6 w-6 text-primary" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-foreground">99.9% Uptime</div>
                  <div className="text-sm text-muted-foreground">Güvenilir hizmet</div>
                </div>
              </div>
              
              <div className="flex items-center justify-center gap-3">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-foreground">Hızlı Kurulum</div>
                  <div className="text-sm text-muted-foreground">24 saat içinde</div>
                </div>
              </div>
              
              <div className="flex items-center justify-center gap-3">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <Phone className="h-6 w-6 text-primary" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-foreground">7/24 Destek</div>
                  <div className="text-sm text-muted-foreground">Her zaman yanınızda</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
