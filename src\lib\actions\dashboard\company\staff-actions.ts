'use server';

import { ApiResponse } from '@/types/global/api';
import { Staff, CreateStaffData, UpdateStaffData } from '@/types/staff';
import { createAction } from '../../core/core';

/**
 * Get all staff members for a specific gym
 */
export async function getStaffByGymId(
  gymId: string
): Promise<ApiResponse<Staff[]>> {
  return createAction<Staff[]>(
    async (_, supabase, _userId) => {
      const { data: staff, error } = await supabase
        .from('gym_staffs')
        .select('*')
        .eq('gym_id', gymId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Personel listesi alınamadı: ${error.message}`);
      }

      return staff || [];
    },
  );
}

/**
 * Create a new staff member
 */
export async function createStaff(
  gymId: string,
  staffData: CreateStaffData
): Promise<ApiResponse<Staff>> {
  return createAction<Staff>(
    async (_, supabase, _userId) => {
      const { data: newStaff, error } = await supabase
        .from('gym_staffs')
        .insert({
          gym_id: gymId,
          name: staffData.name,
          surname: staffData.surname,
          hire_date: staffData.hire_date,
          salary_amount: staffData.salary_amount || null,
          staff_type: staffData.staff_type,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Personel oluşturulamadı: ${error.message}`);
      }

      return newStaff;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/staff`],
    }
  );
}

/**
 * Update an existing staff member
 */
export async function updateStaff(
  staffId: string,
  gymId: string,
  staffData: UpdateStaffData
): Promise<ApiResponse<Staff>> {
  return createAction<Staff>(
    async (_, supabase, _userId) => {
      // Personel güncelle - RLS otomatik olarak gym erişimini kontrol edecek
      const { data: updatedStaff, error } = await supabase
        .from('gym_staffs')
        .update({
          ...staffData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', staffId)
        .eq('gym_id', gymId)
        .select()
        .single();

      if (error) {
        throw new Error(`Personel güncellenemedi: ${error.message}`);
      }

      return updatedStaff;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/staff`],
    }
  );
}

/**
 * Delete a staff member
 */
export async function deleteStaff(
  staffId: string,
  gymId: string
): Promise<ApiResponse<void>> {
  return createAction<void>(
    async (_, supabase, _userId) => {
      // Personeli sil - RLS otomatik olarak gym erişimini kontrol edecek
      const { error } = await supabase
        .from('gym_staffs')
        .delete()
        .eq('id', staffId)
        .eq('gym_id', gymId);

      if (error) {
        throw new Error(`Personel silinemedi: ${error.message}`);
      }

      return undefined;
    },
    {
      requireAuth: true,
      revalidatePaths: [`/dashboard/gym/${gymId}/staff`],
    }
  );
}

/**
 * Get staff member by ID
 */
export async function getStaffById(
  staffId: string,
  gymId: string
): Promise<ApiResponse<Staff>> {
  return createAction<Staff>(
    async (_, supabase, _userId) => {
      // Personel bilgilerini getir
      const { data: staff, error } = await supabase
        .from('gym_staffs')
        .select('*')
        .eq('id', staffId)
        .eq('gym_id', gymId)
        .single();

      if (error) {
        throw new Error(`Personel bilgileri alınamadı: ${error.message}`);
      }

      return staff;
    },
    
  );
}
