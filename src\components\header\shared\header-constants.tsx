import { RoleOption } from './header-types';
import { Crown, Dumbbell, User } from 'lucide-react';

// 🎯 Navigation Constants
export const NAV_LINKS = {
  login: '/auth/login',
  register: '/auth/register',
  dashboard: '/dashboard',
  profile: '/profile/settings',
} as const;

// 🏷️ Role Options Constants
export const ROLE_OPTIONS: Record<string, Omit<RoleOption, 'available'>> = {
  member: {
    role: 'member',
    label: 'Üye',
    href: '/dashboard/member',
    icon: <User className="h-4 w-4" />,
  },
  trainer: {
    role: 'trainer',
    label: 'Antrenör',
    href: '/dashboard/trainer',
    icon: <Dumbbell className="h-4 w-4" />,
  },
  company_manager: {
    role: 'company_manager',
    label: 'Şirket Yöneticisi',
    href: '/dashboard/company',
    icon: <Crown className="h-4 w-4" />,
  },
  gym_manager: {
    role: 'gym_manager',
    label: 'Salon Yöneticisi',
    href: '/dashboard/gym_manager',
    icon: <Crown className="h-4 w-4 text-yellow-500" />,
  },
};

// 📊 Dashboard Specific Constants
export const DASHBOARD_CONFIG = {
  pathPrefix: '/dashboard',
  roles: {
    member: 'member',
    trainer: 'trainer',
    company_manager: 'company_manager',
    gym_manager: 'gym_manager',
  },
  gymPathPattern: /\/gym\/([^\/]+)/,
} as const;

// 🔧 Utility Functions
export const createRoleOptions = (userRoles: string[]): RoleOption[] => {
  return userRoles
    .filter(role => role in ROLE_OPTIONS)
    .map(role => ROLE_OPTIONS[role]);
};

export const extractCurrentRole = (pathname: string): string => {
  const segments = pathname.split('/');
  const dashboardIndex = segments.indexOf('dashboard');
  return dashboardIndex !== -1 && segments[dashboardIndex + 1]
    ? segments[dashboardIndex + 1]
    : '';
};

export const extractCurrentGymId = (pathname: string): string | null => {
  const segments = pathname.split('/');
  const gymIndex = segments.indexOf('gym');
  return gymIndex !== -1 && segments[gymIndex + 1]
    ? segments[gymIndex + 1]
    : null;
};
