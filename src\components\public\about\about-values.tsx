import { Target, Eye, Heart, Lightbulb, Shield, Zap } from "lucide-react";

export function AboutValues() {
  const values = [
    {
      icon: Target,
      title: "Misyonumuz",
      description: "Spor salonu işletmecilerinin dijital dönüşü<PERSON>ü<PERSON><PERSON> des<PERSON>kleyerek, daha verimli ve karlı işletmeler yaratmak. Her salon sahibinin teknolojinin gücünden faydalanabilmesini sağlamak."
    },
    {
      icon: Eye,
      title: "Vizyonumuz",
      description: "Türkiye'nin en güvenilir ve yenilikçi spor salonu yönetim platformu olmak. Spor endüstrisinde dijital standartları belirleyen lider konumunda bulunmak."
    },
    {
      icon: Heart,
      title: "Müşteri Odaklılık",
      description: "Her kararımızda müşterilerimizin ihtiyaçlarını önceleyeriz. Onların başarısı bizim başarımızdır. Sürekli geri bildirim alarak hizmetlerimizi geliştiririz."
    },
    {
      icon: Lightbulb,
      title: "İnovasyon",
      description: "Teknolojinin sınırlarını zorlayarak, spor endüstrisine yenilikçi çözümler getiriyoruz. Sürekli araştırma ve geliştirme ile sektörde öncü olmaya devam ediyoruz."
    },
    {
      icon: Shield,
      title: "Güvenilirlik",
      description: "Müşterilerimizin verilerini en yüksek güvenlik standartlarıyla koruyoruz. Şeffaf iletişim ve tutarlı hizmet kalitesi ile güven inşa ediyoruz."
    },
    {
      icon: Zap,
      title: "Sürekli Gelişim",
      description: "Hem bireysel hem de kurumsal olarak sürekli öğrenmeye ve gelişmeye odaklanıyoruz. Değişen teknolojilere ve müşteri ihtiyaçlarına hızla adapte oluyoruz."
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Değerlerimiz
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Sportiva&apos;yı yönlendiren temel değerler ve ilkeler
            </p>
          </div>

          {/* Values Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <div
                  key={index}
                  className="bg-muted/30 rounded-xl p-8 border border-border hover:shadow-lg hover:border-primary/30 transition-all duration-300"
                >
                  <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>

                  <h3 className="text-xl font-semibold text-primary mb-4">
                    {value.title}
                  </h3>

                  <p className="text-muted-foreground leading-relaxed">
                    {value.description}
                  </p>
                </div>
              );
            })}
          </div>

          {/* Achievement Section */}
          <div className="mt-20 bg-muted/30 rounded-xl p-8 md:p-12 border border-border">
            <div className="text-center">
              <h3 className="text-2xl md:text-3xl font-bold text-primary mb-6">
                Başarılarımız
              </h3>
              <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto">
                Değerlerimiz doğrultusunda elde ettiğimiz başarılar ve tanınırlık
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Target className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="text-lg font-semibold text-foreground">En İyi Startup 2024</h4>
                  <p className="text-sm text-muted-foreground">TechCrunch Türkiye tarafından</p>
                </div>

                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Zap className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="text-lg font-semibold text-foreground">%300 Büyüme</h4>
                  <p className="text-sm text-muted-foreground">Son 12 ayda kullanıcı artışı</p>
                </div>

                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Heart className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="text-lg font-semibold text-foreground">4.9/5 Memnuniyet</h4>
                  <p className="text-sm text-muted-foreground">Müşteri değerlendirme ortalaması</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
