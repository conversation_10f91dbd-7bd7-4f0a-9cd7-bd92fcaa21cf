import type { ReactNode } from 'react';
import { Oxanium } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { SettingsInitializer } from '@/components/settings-initializer';
import { GlobalToastHandler } from '@/components/global-toast-handler';
import './globals.css';

export const experimental_ppr = true;

const oxanium = Oxanium({ subsets: ['latin'] });
export const metadata = {
  title: {
    default: 'Sportiva | Spor Salonu Yönetim Sistemi',
    template: '%s | Sportiva',
  },
  authors: [{ name: 'Sportiva' }],
  creator: 'Sportiva',
  metadataBase: new URL('https://sportiva.com.tr'),
  openGraph: {
    type: 'website',
    locale: 'tr_TR',
    siteName: 'Sportiva',
  },
  twitter: {
    card: 'summary_large_image',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({ children }: { children: ReactNode }) {
  const supabaseHost = getSupabaseHostname();
  return (
    <html lang="tr" suppressHydrationWarning>
      <head>
        {/* DNS-prefetch & preconnect: Supabase storage ve Google avatars için */}
        {supabaseHost && (
          <>
            <link rel="dns-prefetch" href={`https://${supabaseHost}`} />
            <link rel="preconnect" href={`https://${supabaseHost}`} crossOrigin="anonymous" />
          </>
        )}
        <link rel="dns-prefetch" href="https://lh3.googleusercontent.com" />
        <link rel="preconnect" href="https://lh3.googleusercontent.com" crossOrigin="anonymous" />
      </head>
      <body className={oxanium.className}>
        <SettingsInitializer />
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          {/* Erişilebilirlik için Skip Link */}
          <a
            href="#main-content"
            className="bg-primary text-primary-foreground sr-only z-50 rounded-md px-4 py-2 focus:not-sr-only focus:absolute focus:top-4 focus:left-4"
          >
            Ana içeriğe geç
          </a>

          {children}
        <Toaster position="bottom-right" />
        {/* Global toast yakalayıcısı: URL parametreleri + flash cookie */}
        <GlobalToastHandler />
      </ThemeProvider>
      </body>
    </html>
  );
}

// Helper: SUPABASE_URL'den hostname üret
function getSupabaseHostname(): string {
  const supabaseUrl = process.env.SUPABASE_URL;
  if (!supabaseUrl) return '';
  try {
    return new URL(supabaseUrl).hostname;
  } catch {
    return '';
  }
}
