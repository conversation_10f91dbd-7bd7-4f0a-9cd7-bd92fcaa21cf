/**
 * Company dashboard types
 *
 * Extracted from company dashboard components
 */

/**
 * Company dashboard overview data
 */
export interface ManagerDashboardOverview {
  totalGyms: number;
  totalMembers: number;
  totalRevenue: number;
  monthlyGrowth: number;
  recentActivities: {
    id: string;
    type: 'member_joined' | 'payment_received' | 'gym_created';
    description: string;
    timestamp: string;
    gymId?: string;
    gymName?: string;
  }[];
}

/**
 * Branch management data
 */
export interface GymManagementData {
  id: string;
  name: string;
  address: string;
  memberCount: number;
  activeMembers: number;
  monthlyRevenue: number;
  status: 'active' | 'inactive';
  lastActivity: string;
  packages: {
    id: string;
    name: string;
    price: number;
    memberCount: number;
  }[];
}

/**
 * Staff management data
 */
export interface StaffManagementData {
  id: string;
  profileId: string;
  fullName: string;
  email: string;
  role: 'trainer' | 'receptionist' | 'cleaner' | 'security';
  status: 'active' | 'inactive' | 'on_leave';
  joinDate: string;
  salary?: number;
  gymId: string;
  gymName: string;
}

/**
 * Financial summary data
 */
export interface FinancialSummary {
  totalRevenue: number;
  monthlyRevenue: number;
  yearlyRevenue: number;
  revenueGrowth: number;
  expenses: number;
  profit: number;
  profitMargin: number;
  revenueByGym: {
    gymId: string;
    gymName: string;
    revenue: number;
    percentage: number;
  }[];
}

/**
 * Company notification data
 */
export interface ManagerNotification {
  id: string;
  type: 'payment' | 'member_request' | 'staff_update' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  gymId?: string;
  gymName?: string;
  actionRequired?: boolean;
  actionUrl?: string;
}
