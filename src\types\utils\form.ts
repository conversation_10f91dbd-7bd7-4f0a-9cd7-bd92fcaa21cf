/**
 * Form utility types
 *
 * Types for form validation, field states, and form handling
 */

/**
 * Form field state
 */
export interface FormFieldState {
  value: unknown;
  error?: string;
  touched: boolean;
  dirty: boolean;
}

/**
 * Form validation result
 */
export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Form submission state
 */
export type FormSubmissionState = 'idle' | 'submitting' | 'success' | 'error';

/**
 * Generic form data type
 */
export type FormData<T = Record<string, unknown>> = T;

/**
 * Form event handlers
 */
export interface FormEventHandlers<T = Record<string, unknown>> {
  onSubmit: (data: T) => void | Promise<void>;
  onChange?: (field: keyof T, value: unknown) => void;
  onBlur?: (field: keyof T) => void;
  onReset?: () => void;
}
