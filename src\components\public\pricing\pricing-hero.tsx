import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, Zap, ArrowDown} from "lucide-react";
import Link from "next/link";

export function PricingHero() {
  const benefits = [
    "Kurulum desteği dahil",
    "7/24 müşteri desteği",
    "İstediğiniz zaman iptal",
    "Güvenli ödeme sistemi"
  ];

  return (
    <section className="relative py-20 lg:py-32 bg-background overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Badges */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
            <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-muted/30 text-foreground border-border">
              <Zap className="h-4 w-4 mr-2 text-primary" />
              Fiyatlandırma
            </Badge>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary leading-tight mb-6">
            İşletmenize Uygun
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary/60">
              Planı Seçin
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed mb-12 max-w-3xl mx-auto">
            Spor salonunuzun büyüklüğüne ve ihtiyaçlarına göre tasarlanmış esnek fiyatlandırma planları.
            Tüm planlar 14 gün ücretsiz deneme ile başlar.
          </p>

          {/* Benefits */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center justify-center gap-2 text-sm">
                <Check className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-muted-foreground">{benefit}</span>
              </div>
            ))}
          </div>

          {/* CTA Buttons - Removed free trial, premium branding */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button asChild size="lg" variant="outline">
              <Link href="/contact">
                İletişime Geçin
              </Link>
            </Button>
          </div>

          {/* Scroll Indicator */}
          <Link
            href="#pricing-plans"
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors group"
            aria-label="Fiyatlandırma planlarına kaydır"
          >
            <span className="text-sm">Planları inceleyin</span>
            <ArrowDown className="h-4 w-4 group-hover:translate-y-1 transition-transform" />
          </Link>
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
      </div>
    </section>
  );
}
