import { createClient } from '@supabase/supabase-js';
import { Database } from './types';

// Supabase admin client oluştur - bu client, service_role key kullanır
// NOT: Bu client sadece server-side'da kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, asla client-side'da kullanmayın!
export function getSupabaseAdmin() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase URL veya Service Role Key eksik');
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}
