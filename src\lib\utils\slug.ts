/**
 * Slug utility functions for gym URLs
 */

/**
 * Türkçe karakterleri İngilizce karşılıklarına çevirir
 */
function turkishToEnglish(text: string): string {
  const turkishChars: { [key: string]: string } = {
    ç: 'c',
    Ç: 'C',
    ğ: 'g',
    Ğ: 'G',
    ı: 'i',
    I: 'I',
    İ: 'I',
    i: 'i',
    ö: 'o',
    Ö: 'O',
    ş: 's',
    Ş: 'S',
    ü: 'u',
    Ü: 'U',
  };

  return text.replace(/[çÇğĞıIİiöÖşŞüÜ]/g, char => turkishChars[char] || char);
}

/**
 * Gym için default slug generate eder
 */
export function generateDefaultSlug(gymName: string, city: string): string {
  // Random 4 digit suffix for uniqueness
  const randomSuffix = Math.floor(1000 + Math.random() * 9000);

  const baseSlug = `${gymName}-${city}-${randomSuffix}`;

  return turkishToEnglish(baseSlug)
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // <PERSON><PERSON>e harf, rakam ve boşluk
    .replace(/\s+/g, '-') // Boşlukları tire yap
    .replace(/-+/g, '-') // Ardışık tireleri tek tire yap
    .substring(0, 80); // Max 80 karakter
}

/**
 * Kullanıcı tarafından girilen slug'ı temizler ve validate eder
 */
export function sanitizeSlug(slug: string): string {
  return turkishToEnglish(slug)
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Sadece harf, rakam, boşluk ve tire
    .replace(/\s+/g, '-') // Boşlukları tire yap
    .replace(/-+/g, '-') // Ardışık tireleri tek tire yap
    .replace(/^-+|-+$/g, '') // Başındaki ve sonundaki tireleri kaldır
    .substring(0, 80); // Max 80 karakter
}

/**
 * Slug validation
 */
export function validateSlug(slug: string): { valid: boolean; error?: string } {
  if (!slug || slug.length === 0) {
    return { valid: false, error: 'Slug boş olamaz' };
  }

  if (slug.length < 3) {
    return { valid: false, error: 'Slug en az 3 karakter olmalıdır' };
  }

  if (slug.length > 80) {
    return { valid: false, error: 'Slug en fazla 80 karakter olabilir' };
  }

  // Sadece harf, rakam ve tire
  if (!/^[a-z0-9-]+$/.test(slug)) {
    return {
      valid: false,
      error: 'Sadece küçük harf, rakam ve tire kullanabilirsiniz',
    };
  }

  // Tire ile başlayamaz/bitemez
  if (slug.startsWith('-') || slug.endsWith('-')) {
    return { valid: false, error: 'Tire ile başlayamaz veya bitemez' };
  }

  // Ardışık tire olamaz
  if (slug.includes('--')) {
    return { valid: false, error: 'Ardışık tire kullanılamaz' };
  }

  // Reserved words
  const reservedWords = [
    'admin',
    'api',
    'www',
    'app',
    'dashboard',
    'settings',
    'profile',
  ];
  if (reservedWords.includes(slug)) {
    return { valid: false, error: 'Bu slug kullanılamaz' };
  }

  return { valid: true };
}

/**
 * Slug preview oluşturur (kullanıcı yazarken real-time preview için)
 */
export function generateSlugPreview(input: string): string {
  if (!input || input.trim().length === 0) {
    return '';
  }

  return sanitizeSlug(input);
}
