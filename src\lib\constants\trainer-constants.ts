import { z } from 'zod';

// Trainer Specialization Options
export const TRAINER_SPECIALIZATION_OPTIONS = [
  { value: 'fitness', label: 'Fitness & Kondisyon' },
  { value: 'bodybuilding', label: 'Vü<PERSON> Geliştirme' },
  { value: 'powerlifting', label: 'Powerlifting' },
  { value: 'crossfit', label: 'CrossFit' },
  { value: 'yoga', label: 'Yoga' },
  { value: 'pilates', label: 'Pilates' },
  { value: 'cardio', label: '<PERSON><PERSON><PERSON>vask<PERSON><PERSON> Antrenman' },
  { value: 'functional', label: 'Fonksiyonel Antrenman' },
  { value: 'rehabilitation', label: 'Rehabilitasyon' },
  { value: 'nutrition', label: 'Beslenme Koçluğu' },
  { value: 'other', label: '<PERSON>ğ<PERSON>' },
] as const;

// Trainer Certification Levels
export const TRAINER_CERTIFICATION_LEVELS = [
  { value: 'beginner', label: 'Başlangıç Seviyesi' },
  { value: 'intermediate', label: 'Orta <PERSON>' },
  { value: 'advanced', label: '<PERSON><PERSON>i <PERSON>' },
  { value: 'expert', label: 'U<PERSON>' },
  { value: 'master', label: 'Master Trainer' },
] as const;

// Type definitions
export type TrainerSpecialization =
  (typeof TRAINER_SPECIALIZATION_OPTIONS)[number]['value'];
export type TrainerCertificationLevel =
  (typeof TRAINER_CERTIFICATION_LEVELS)[number]['value'];

// Constants
export const TRAINER_BIO_MAX_LENGTH = 500;
export const TRAINER_MAX_EXPERIENCE_YEARS = 50;

// Specialization label mapping
const SPECIALIZATION_LABEL_MAP: Record<string, string> = {
  fitness: 'Fitness & Kondisyon',
  bodybuilding: 'Vücut Geliştirme',
  powerlifting: 'Powerlifting',
  crossfit: 'CrossFit',
  yoga: 'Yoga',
  pilates: 'Pilates',
  cardio: 'Kardiyovasküler Antrenman',
  functional: 'Fonksiyonel Antrenman',
  rehabilitation: 'Rehabilitasyon',
  nutrition: 'Beslenme Koçluğu',
  other: 'Diğer',
};

// Certification level label mapping
const CERTIFICATION_LEVEL_LABEL_MAP: Record<string, string> = {
  beginner: 'Başlangıç Seviyesi',
  intermediate: 'Orta Seviye',
  advanced: 'İleri Seviye',
  expert: 'Uzman',
  master: 'Master Trainer',
};

/**
 * Get human-readable label for trainer specialization
 * @param specialization - Specialization value
 * @returns Human-readable label or 'Belirtilmemiş' if not found
 */
export function getTrainerSpecializationLabel(
  specialization: string | null | undefined
): string {
  if (!specialization) return 'Belirtilmemiş';
  return SPECIALIZATION_LABEL_MAP[specialization] || specialization;
}

/**
 * Get human-readable label for trainer certification level
 * @param level - Certification level value
 * @returns Human-readable label or 'Belirtilmemiş' if not found
 */
export function getTrainerCertificationLabel(
  level: string | null | undefined
): string {
  if (!level) return 'Belirtilmemiş';
  return CERTIFICATION_LEVEL_LABEL_MAP[level] || level;
}

// Validation Schemas
export const trainerDetailsSchema = z.object({
  specialization: z.string().optional(),
  certificationLevel: z.string().optional(),
  experience: z.string().optional(),
  bio: z
    .string()
    .max(
      TRAINER_BIO_MAX_LENGTH,
      `Tanıtım en fazla ${TRAINER_BIO_MAX_LENGTH} karakter olabilir`
    )
    .optional(),
});

export const inviteTrainerSchema = z.object({
  trainerCode: z.string().min(1, 'Antrenör kodu gereklidir'),
  message: z
    .string()
    .max(
      TRAINER_BIO_MAX_LENGTH,
      `Mesaj en fazla ${TRAINER_BIO_MAX_LENGTH} karakter olabilir`
    )
    .optional(),
});

export const updateTrainerSchema = z.object({
  specialization: z.string().optional(),
  certification_level: z.string().optional(),
  experience_years: z
    .number()
    .min(0)
    .max(TRAINER_MAX_EXPERIENCE_YEARS)
    .optional(),
  bio: z
    .string()
    .max(TRAINER_BIO_MAX_LENGTH, 'Bio en fazla 500 karakter olabilir')
    .optional(),
  status: z.enum(['active', 'inactive']).optional(),
});

// Type exports for forms
export type TrainerDetailsFormData = z.infer<typeof trainerDetailsSchema>;
export type InviteTrainerFormData = z.infer<typeof inviteTrainerSchema>;
export type UpdateTrainerFormData = z.infer<typeof updateTrainerSchema>;
