import { memo } from "react";
import { MapPin, Phone, Clock, Mail, MessageCircle, Navigation, Calendar, Users, Zap } from "lucide-react";
import { GymSocialLinks } from './gym-social-links';

import { resolveCityName } from '@/lib/utils/display-helpers';

interface GymContactSectionProps {
  address: string | null;
  city: string | null;
  district: string | null;
  gym_phone: string | null;
  socialLinks?: Record<string, string> | null;
  companyName?: string;
}

export const GymContactSection = memo(function GymContactSection({
  address,
  city,
  district,
  gym_phone,
  socialLinks,
  companyName,
}: GymContactSectionProps) {
  return (
    <section className="py-24 bg-gradient-to-b from-muted/20 to-background" aria-labelledby="contact-heading">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6" aria-label="İletiş<PERSON> etike<PERSON>">
            <MessageCircle className="h-4 w-4" aria-hidden="true" />
            <span>İletişim</span>
          </div>
          <h2 id="contact-heading" className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Bizimle <span className="text-primary">İletişime</span> Geçin
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Sorularınız mı var? Size yardımcı olmaktan mutluluk duyarız. Hemen iletişime geçin!
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-8 mb-16">

            {/* Contact Cards */}
            <div className="lg:col-span-2 grid md:grid-cols-2 gap-6" role="list">

              {/* Address Card */}
              <div className="group bg-card/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg" role="listitem">
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform" aria-hidden="true">
                  <MapPin className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Adresimiz</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {address}<br />
                  <span className="font-medium">{district}, {resolveCityName(city)}</span>
                </p>
                <button className="mt-4 inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors" aria-label="Yol tarifi al">
                  <Navigation className="h-4 w-4" />
                  <span>Yol tarifi al</span>
                </button>
              </div>

              {/* Phone Card */}
              {gym_phone && (
                <div className="group bg-card/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg" role="listitem">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform" aria-hidden="true">
                    <Phone className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-3">Telefon</h3>
                  <p className="text-muted-foreground mb-4">
                    Hemen arayın ve bilgi alın
                  </p>
                  <a
                    href={`tel:${gym_phone}`}
                    className="inline-flex items-center gap-2 px-6 py-3 bg-green-500/10 text-green-600 dark:text-green-400 rounded-xl font-semibold hover:bg-green-500/20 transition-colors"
                    aria-label={`${gym_phone} numarasını ara`}
                  >
                    <Phone className="h-4 w-4" />
                    {gym_phone}
                  </a>
                </div>
              )}

              {/* Working Hours Card */}
              <div className="group bg-card/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg md:col-span-2" role="region" aria-label="Çalışma saatleri">
                <div className="flex items-start gap-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-blue-500/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform flex-shrink-0" aria-hidden="true">
                    <Clock className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-foreground mb-4">Çalışma Saatleri</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between items-center p-3 bg-muted/50 rounded-xl">
                          <span className="font-medium text-foreground">Pazartesi - Cuma</span>
                          <span className="text-primary font-semibold">06:00 - 23:00</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-muted/50 rounded-xl">
                          <span className="font-medium text-foreground">Cumartesi - Pazar</span>
                          <span className="text-primary font-semibold">08:00 - 22:00</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-center" aria-live="polite">
                        <div className="text-center">
                          <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2 animate-pulse"></div>
                          <span className="text-sm font-medium text-green-600 dark:text-green-400">Şu an açık</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Card */}
            <div className="bg-gradient-to-br from-primary/10 via-primary/5 to-primary/10 rounded-3xl p-8 border border-primary/20" role="region" aria-label="Hızlı iletişim">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6" aria-hidden="true">
                  <MessageCircle className="h-10 w-10 text-primary" />
                </div>
                <h3 className="text-3xl font-bold text-foreground mb-4">
                  Hemen Başlayın!
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Fitness yolculuğunuza bugün başlayın. Size özel antrenman programı ve rehberlik için bizimle iletişime geçin.
                </p>
              </div>

              {/* Quick Contact Options */}
              <div className="space-y-4">
                {gym_phone && (
                  <a
                    href={`tel:${gym_phone}`}
                    className="w-full bg-primary text-primary-foreground py-4 px-6 rounded-2xl font-semibold text-lg hover:bg-primary/90 transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl"
                    aria-label="Telefon ile ara"
                  >
                    <Phone className="h-5 w-5" />
                    Hemen Ara
                  </a>
                )}

                <button className="w-full bg-card/80 backdrop-blur-sm text-foreground py-4 px-6 rounded-2xl font-semibold text-lg hover:bg-card transition-all duration-300 flex items-center justify-center gap-3 border border-border/50 hover:border-primary/30" aria-label="E-posta gönder">
                  <Mail className="h-5 w-5" />
                  E-posta Gönder
                </button>
              </div>

              {/* Features */}
              <div className="mt-8 pt-6 border-t border-border/50">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary">24/7</div>
                    <div className="text-xs text-muted-foreground">Destek</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">Ücretsiz</div>
                    <div className="text-xs text-muted-foreground">Danışmanlık</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="relative mt-20">
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-3xl p-12 border border-primary/20 text-center">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
                Fitness Hedeflerinize Ulaşmaya <span className="text-primary">Hazır mısınız?</span>
              </h3>
              <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto leading-relaxed">
                Profesyonel ekibimiz ve modern ekipmanlarımızla size en iyi spor deneyimini sunuyoruz.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
                <button className="group bg-primary text-primary-foreground py-4 px-8 rounded-2xl font-semibold text-lg hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-3">
                  <Users className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Üyelik Başvurusu Yap
                </button>
                <button className="group border-2 border-border text-foreground py-4 px-8 rounded-2xl font-semibold text-lg hover:border-primary hover:text-primary transition-all duration-300 flex items-center justify-center gap-3">
                  <Calendar className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Salon Turuna Katıl
                </button>
              </div>

              {/* Social Links */}
              {socialLinks && companyName && (
                <div className="pt-8 border-t border-border/50">
                  <div className="flex justify-center">
                    <GymSocialLinks
                      socialLinks={socialLinks}
                      companyName={companyName}
                    />
                  </div>
                </div>
              )}

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-8 border-t border-border/50">
                <div className="flex items-center justify-center gap-3">
                  <div className="w-10 h-10 bg-green-500/10 rounded-full flex items-center justify-center">
                    <Zap className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-sm font-medium text-muted-foreground">Hızlı Başlangıç</span>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <span className="text-sm font-medium text-muted-foreground">Profesyonel Destek</span>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <div className="w-10 h-10 bg-purple-500/10 rounded-full flex items-center justify-center">
                    <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <span className="text-sm font-medium text-muted-foreground">Esnek Programlar</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});
