'use client';

import { Progress } from '@/components/ui/progress';
import { StepHeaderProps } from '@/types/onboarding';

export function StepHeader({
  stepIndex,
  totalSteps,
  title,
  description,
  progress,
}: StepHeaderProps) {
  return (
    <div className="mb-8">
      <div className="mx-auto max-w-md">
        <div className="mb-2 flex items-center justify-between text-sm font-medium">
          <span className="text-primary" aria-live="polite">
            Adım {stepIndex}
          </span>
          <span className="text-muted-foreground" aria-label={`Toplam ${totalSteps} adım`}>
            {totalSteps} adımdan
          </span>
        </div>
        <Progress value={progress} className="h-2" aria-valuenow={progress} />
      </div>
      <div className="mt-8 text-center">
        <h1 className="mb-3 text-3xl font-bold tracking-tight sm:text-4xl">
          {title}
        </h1>
        {description && (
          <p className="text-muted-foreground mx-auto max-w-3xl text-base sm:text-lg">
            {description}
          </p>
        )}
      </div>
    </div>
  );
}
