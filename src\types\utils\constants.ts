/**
 * Constant utility types
 * 
 * Types for application constants and configuration
 */

/**
 * Environment types
 */
export type Environment = 'development' | 'staging' | 'production';

/**
 * Log level types
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Application configuration
 */
export interface AppConfig {
  environment: Environment;
  apiUrl: string;
  logLevel: LogLevel;
  features: Record<string, boolean>;
}

/**
 * Pagination constants
 */
export interface PaginationConfig {
  defaultPageSize: number;
  maxPageSize: number;
  pageSizeOptions: number[];
}

/**
 * File upload configuration
 */
export interface FileUploadConfig {
  maxFileSize: number; // in bytes
  allowedTypes: string[];
  maxFiles: number;
}
