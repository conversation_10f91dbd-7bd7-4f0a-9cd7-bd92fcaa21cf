import { <PERSON>ed<PERSON>, Twitter, Github } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function AboutTeam() {
  const team = [
    {
      name: "<PERSON><PERSON>",
      role: "<PERSON><PERSON><PERSON> & CEO",
      bio: "10+ yıl tek<PERSON><PERSON>, spor endü<PERSON><PERSON> u<PERSON>",
      image: "/team/ceo.jpg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    },
    {
      name: "<PERSON><PERSON>",
      role: "CTO",
      bio: "Full-stack gelişti<PERSON>i, sistem mimarisi uzman<PERSON>",
      image: "/team/cto.jpg",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#"
      }
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "<PERSON><PERSON><PERSON><PERSON>",
      bio: "UX/UI tasarım ve ürün geliştirme deneyimi",
      image: "/team/pm.jpg",
      social: {
        linkedin: "#",
        twitter: "#"
      }
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "<PERSON><PERSON><PERSON><PERSON> Müdürü",
      bio: "Dijital pazarlama ve büyüme stratejileri uzmanı",
      image: "/team/marketing.jpg",
      social: {
        linkedin: "#",
        twitter: "#"
      }
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Ekibimiz
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Sportiva&apos;yı hayata geçiren deneyimli ve tutkulu ekibimizle tanışın
            </p>
          </div>

          {/* Team Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {team.map((member, index) => (
              <div
                key={index}
                className="text-center group"
              >
                <div className="bg-muted/30 rounded-xl p-6 border border-border hover:shadow-lg hover:border-primary/30 transition-all duration-300">
                  {/* Avatar */}
                  <div className="w-24 h-24 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors duration-300">
                    <span className="text-2xl font-bold text-primary">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>

                  {/* Info */}
                  <h3 className="text-lg font-semibold text-primary mb-1">
                    {member.name}
                  </h3>

                  <p className="text-sm font-medium text-foreground mb-3">
                    {member.role}
                  </p>

                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Social Links */}
                  <div className="flex justify-center gap-2">
                    {member.social.linkedin && (
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Linkedin className="h-4 w-4" />
                      </Button>
                    )}
                    {member.social.twitter && (
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Twitter className="h-4 w-4" />
                      </Button>
                    )}
                    {member.social.github && (
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Github className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Join Team Section */}
          <div className="bg-muted/30 rounded-xl p-8 md:p-12 border border-border text-center">
            <h3 className="text-2xl md:text-3xl font-bold text-primary mb-4">
              Ekibimize Katılın
            </h3>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
              Spor teknolojileri alanında yenilik yapmak ve Türkiye&apos;nin en büyük spor salonu
              platformunu geliştirmek için aramıza katılın.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">🚀 Hızlı Büyüme</h4>
                <p className="text-sm text-muted-foreground">Dinamik startup ortamında hızla gelişin</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">💡 İnovasyon</h4>
                <p className="text-sm text-muted-foreground">Yenilikçi projeler üzerinde çalışın</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">🎯 Etki</h4>
                <p className="text-sm text-muted-foreground">Binlerce salonu etkileyen çözümler üretin</p>
              </div>
            </div>

            <Button size="lg" className="shadow-lg">
              Açık Pozisyonları Görüntüle
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
