import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';
import {
  signInWithEmail,
  signInWithPhonePassword,
  sendPhoneOtp,
  verifyPhoneOtp,
} from '@/lib/actions/auth/auth-actions';
import { signInWithGoogle } from '@/lib/actions/auth/google-auth-actions';
import { LoginForm } from './login-form';
import { GoogleOAuthButton } from '@/components/auth/google-oauth-button';

export const metadata: Metadata = {
  title: 'Giriş Yap | Sportiva',
  description: 'Sportiva spor salonu yönetim sistemi giriş sayfası.',
};

interface LoginPageProps {
  searchParams: {
    error?: string;
    message?: string;
    identifier_error?: string;
    password_error?: string;
    redirect?: string;
    context?: string; // Error context bilgisi
  };
}
export default async function LoginPage({ searchParams }: LoginPageProps) {
  const params = await searchParams;
  const { error, message, identifier_error, password_error, context } = params;

  async function handleEmailLogin(formData: FormData) {
    'use server';
    return await signInWithEmail(formData);
  }

  async function handlePhonePasswordLogin(formData: FormData) {
    'use server';
    return await signInWithPhonePassword(formData);
  }

  async function handleSendPhoneOtp(formData: FormData) {
    'use server';
    return await sendPhoneOtp(formData);
  }

  async function handleVerifyPhoneOtp(formData: FormData) {
    'use server';
    return await verifyPhoneOtp(formData);
  }

  async function handleGoogleLogin() {
    'use server';
    return await signInWithGoogle();
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-foreground mb-2 text-2xl font-bold lg:text-3xl">
          {AUTH_UI_TEXT.LOGIN_TITLE}
        </h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          {AUTH_UI_TEXT.LOGIN_SUBTITLE}
        </p>
      </div>

      {/* Success Message */}
      {message && (
        <div
          className="flex w-full items-center space-x-3 rounded-lg border border-green-200 bg-green-50 p-4 text-green-700"
          role="alert"
          aria-live="polite"
        >
          <span className="text-sm font-medium">{message}</span>
        </div>
      )}

      {/* Google OAuth Button */}
      <GoogleOAuthButton action={handleGoogleLogin}>
        Google ile Giriş Yap
      </GoogleOAuthButton>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background text-muted-foreground px-2">Veya</span>
        </div>
      </div>

      {/* Login Form */}
      <LoginForm
        error={error}
        identifierError={identifier_error}
        passwordError={password_error}
        context={context}
        onEmailLogin={handleEmailLogin}
        onPhonePasswordLogin={handlePhonePasswordLogin}
        onSendPhoneOtp={handleSendPhoneOtp}
        onVerifyPhoneOtp={handleVerifyPhoneOtp}
      />

      {/* Footer */}
      <div className="text-center">
        <span className="text-sm">{AUTH_UI_TEXT.NO_ACCOUNT}</span>
        <Link
          href="/auth/register"
          className="text-primary hover:text-primary/80 ml-1 text-sm font-medium hover:underline"
        >
          {AUTH_UI_TEXT.REGISTER_LINK}
        </Link>
      </div>
    </div>
  );
}
