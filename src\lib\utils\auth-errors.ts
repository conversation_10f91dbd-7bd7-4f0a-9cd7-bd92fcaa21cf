/**
 * Auth error context bilgisi
 */
export interface AuthErrorContext {
  action?:
    | 'login'
    | 'register'
    | 'verify_email'
    | 'verify_phone'
    | 'reset_password';
  method?: 'email' | 'phone';
  stage?: 'initial' | 'verification' | 'callback';
}

/**
 * Detaylı error response
 */
export interface DetailedAuthError {
  message: string;
  suggestion?: string;
  actionText?: string;
  actionUrl?: string;
  canRetry?: boolean;
}

/**
 * Supabase error mesajlarını Türkçe'ye çeviren gelişmiş fonksiyon
 */
export function translateAuthError(
  errorMessage: string,
  context?: AuthErrorContext
): DetailedAuthError {
  const baseErrorMap: Record<string, DetailedAuthError> = {
    // Giriş hataları
    'Invalid login credentials': {
      message: 'E-posta/telefon veya şifre hatalı.',
      suggestion: 'Bilgilerinizi kontrol ederek tekrar deneyin.',
      canRetry: true,
    },
    'Email not confirmed': {
      message: 'E-posta adresiniz doğrulanmamış.',
      suggestion: 'E-posta kutunuzu kontrol ederek doğrulama linkine tıklayın.',
      actionText: 'Doğrulama E-postası Gönder',
      canRetry: true,
    },
    'User not found': {
      message: 'Bu bilgilerle kayıtlı kullanıcı bulunamadı.',
      suggestion: 'Bilgilerinizi kontrol edin veya kayıt olmayı deneyin.',
      actionText: 'Kayıt Ol',
      actionUrl: '/auth/register',
      canRetry: true,
    },

    // Kayıt hataları
    'User already registered': {
      message: 'Bu e-posta adresi zaten kayıtlı.',
      suggestion: 'Giriş yapmayı deneyin veya şifrenizi sıfırlayın.',
      actionText: 'Giriş Yap',
      actionUrl: '/auth/login',
      canRetry: false,
    },
    'Email already registered': {
      message: 'Bu e-posta adresi zaten kayıtlı.',
      suggestion: 'Giriş yapmayı deneyin veya şifrenizi sıfırlayın.',
      actionText: 'Giriş Yap',
      actionUrl: '/auth/login',
      canRetry: false,
    },
    'Phone number already registered': {
      message: 'Bu telefon numarası zaten kayıtlı.',
      suggestion: 'Giriş yapmayı deneyin veya şifrenizi sıfırlayın.',
      actionText: 'Giriş Yap',
      actionUrl: '/auth/login',
      canRetry: false,
    },

    // Doğrulama hataları
    'Invalid email': {
      message: 'Geçersiz e-posta adresi.',
      suggestion: 'E-posta adresinizi kontrol ederek tekrar deneyin.',
      canRetry: true,
    },
    'Invalid phone number': {
      message: 'Geçersiz telefon numarası.',
      suggestion: 'Telefon numaranızı kontrol ederek tekrar deneyin.',
      canRetry: true,
    },
    'Password should be at least 6 characters': {
      message: 'Şifre en az 6 karakter olmalıdır.',
      suggestion: 'Daha güçlü bir şifre seçin.',
      canRetry: true,
    },

    // Sistem hataları
    'Signup disabled': {
      message: 'Kayıt işlemi şu anda devre dışı.',
      suggestion: 'Lütfen daha sonra tekrar deneyin.',
      canRetry: true,
    },
    'Email rate limit exceeded': {
      message: 'E-posta gönderim limiti aşıldı.',
      suggestion: 'Lütfen birkaç dakika bekleyip tekrar deneyin.',
      canRetry: true,
    },
    'SMS rate limit exceeded': {
      message: 'SMS gönderim limiti aşıldı.',
      suggestion: 'Lütfen birkaç dakika bekleyip tekrar deneyin.',
      canRetry: true,
    },
    'Too many requests': {
      message: 'Çok fazla istek gönderildi.',
      suggestion: 'Lütfen birkaç dakika bekleyip tekrar deneyin.',
      canRetry: true,
    },
    'Weak password': {
      message: 'Şifre çok zayıf.',
      suggestion:
        'En az 8 karakter, büyük-küçük harf ve rakam içeren bir şifre seçin.',
      canRetry: true,
    },
    'Phone number not confirmed': {
      message: 'Telefon numaranız doğrulanmamış.',
      suggestion: 'SMS ile gelen doğrulama kodunu girin.',
      actionText: 'Doğrulama Kodu Gönder',
      canRetry: true,
    },
    'Invalid verification code': {
      message: 'Doğrulama kodu geçersiz.',
      suggestion: 'Kodu kontrol ederek tekrar deneyin.',
      canRetry: true,
    },
    'Verification code expired': {
      message: 'Doğrulama kodu süresi dolmuş.',
      suggestion: 'Yeni bir doğrulama kodu isteyin.',
      actionText: 'Yeni Kod Gönder',
      canRetry: true,
    },
    'Invalid token': {
      message: "Geçersiz doğrulama token'ı.",
      suggestion: 'Lütfen tekrar giriş yapmayı deneyin.',
      canRetry: true,
    },
    'Token expired': {
      message: "Doğrulama token'ı süresi dolmuş.",
      suggestion: 'Lütfen tekrar giriş yapmayı deneyin.',
      canRetry: true,
    },

    // OTP ve verification hataları
    otp_expired: {
      message: 'Doğrulama kodu süresi dolmuş.',
      suggestion: 'Yeni bir doğrulama kodu isteyin.',
      actionText: 'Yeni Kod Gönder',
      canRetry: true,
    },
    invalid_otp: {
      message: 'Doğrulama kodu geçersiz.',
      suggestion: 'Kodu kontrol ederek tekrar deneyin.',
      canRetry: true,
    },
    email_link_invalid: {
      message: 'E-posta doğrulama linki geçersiz.',
      suggestion: 'Yeni bir doğrulama e-postası isteyin.',
      actionText: 'Yeni Link Gönder',
      canRetry: true,
    },
  };

  // Error'u bul
  let errorInfo = baseErrorMap[errorMessage];

  // Eğer bulunamazsa, partial match dene
  if (!errorInfo) {
    const lowerMessage = errorMessage.toLowerCase();
    for (const [key, value] of Object.entries(baseErrorMap)) {
      if (
        lowerMessage.includes(key.toLowerCase()) ||
        key.toLowerCase().includes(lowerMessage)
      ) {
        errorInfo = value;
        break;
      }
    }
  }

  // Hala bulunamazsa varsayılan
  if (!errorInfo) {
    errorInfo = {
      message: errorMessage,
      suggestion: 'Lütfen tekrar deneyin.',
      canRetry: true,
    };
  }

  // Context'e göre özelleştir
  if (context) {
    errorInfo = customizeErrorByContext(errorInfo, context);
  }

  return errorInfo;
}

/**
 * Context'e göre error mesajını özelleştir
 */
function customizeErrorByContext(
  error: DetailedAuthError,
  context: AuthErrorContext
): DetailedAuthError {
  const customized = { ...error };

  // Email not confirmed durumunda context'e göre action URL'i ayarla
  if (error.message.includes('doğrulanmamış') && context.method === 'email') {
    if (context.action === 'login') {
      customized.actionUrl = '/auth/verify-email';
    } else if (context.action === 'register') {
      customized.actionUrl = '/auth/verify-email';
    }
  }

  // Phone not confirmed durumunda
  if (error.message.includes('doğrulanmamış') && context.method === 'phone') {
    if (context.action === 'login') {
      customized.actionUrl = '/verify-phone';
    } else if (context.action === 'register') {
      customized.actionUrl = '/verify-phone';
    }
  }

  return customized;
}

/**
 * Basit string döndüren backward compatibility fonksiyonu
 */
export function translateAuthErrorSimple(errorMessage: string): string {
  const detailed = translateAuthError(errorMessage);
  return detailed.message;
}
