'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Calendar,
  Clock,
  Plus,
  Minus,
  RotateCcw,
  Zap,
} from 'lucide-react';
import { toast } from 'sonner';
import { format, addWeeks, addDays } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { checkTrainerAvailability } from '@/lib/actions/dashboard/company/appointment-actions';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';

// Fallback saat listesi: gym time_slots bo<PERSON><PERSON> kull<PERSON><PERSON>lacak
const DEFAULT_FALLBACK_TIME_SLOTS = [
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
  '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
  '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
  '20:00', '20:30', '21:00', '21:30', '22:00'
];



interface SessionSlot {
  id: string;
  sessionNumber: number;
  date?: string;
  time?: string;
  dayOfWeek?: number; // 0=Sunday, 1=Monday, etc.
  isSelected: boolean;
  isAutoFilled: boolean;
}

interface SmartSessionPlannerProps {
  totalSessions: number;
  trainerId: string;
  gymId: string;
  packageData: {
    gym_package: {
      session_duration_minutes: number;
      package_type: string;
    };
  };
  onSessionsConfirm: (sessions: Array<{ date: string; time: string }>) => void;
}

export function SmartSessionPlanner({
  totalSessions,
  trainerId,
  gymId,
  packageData,
  onSessionsConfirm
}: SmartSessionPlannerProps) {
  const [sessions, setSessions] = useState<SessionSlot[]>([]);
  const [currentSessionCount, setCurrentSessionCount] = useState(1); // Başlangıçta sadece 1 kart
  const [suggestedDateTime, setSuggestedDateTime] = useState<{ date: string; time: string } | null>(null);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [timeSlots, setTimeSlots] = useState<string[]>(DEFAULT_FALLBACK_TIME_SLOTS);

  // Gym'in tanımlı randevu saatlerini yükle
  useEffect(() => {
    let mounted = true;
    const loadGymTimeSlots = async () => {
      try {
        const res = await getGymById(gymId);
        if (mounted && res.success) {
          const slots = (res.data?.time_slots as string[] | null) || [];
          setTimeSlots(slots.length > 0 ? slots : DEFAULT_FALLBACK_TIME_SLOTS);
        }
      } catch (e) {
        // Hata durumunda fallback kullan
        if (mounted) setTimeSlots(DEFAULT_FALLBACK_TIME_SLOTS);
      }
    };
    loadGymTimeSlots();
    return () => { mounted = false; };
  }, [gymId]);

  // Find next available slot for trainer
  const findNextAvailableSlot = useCallback(async () => {
    setIsLoadingSuggestion(true);
    try {
      const sessionDuration = packageData.gym_package.session_duration_minutes || 60;
      const now = new Date();

      // Check next 14 days (starting from today)
      for (let dayOffset = 0; dayOffset <= 14; dayOffset++) {
        const checkDate = addDays(now, dayOffset);

        // Check each time slot for this day (gym ayarları)
        for (const timeSlot of timeSlots) {
          const checkDateTime = new Date(`${checkDate.toISOString().split('T')[0]}T${timeSlot}:00`);

          // Skip past times (only if it's today and the time has already passed)
          if (dayOffset === 0 && checkDateTime <= now) {
            continue;
          }

          const availabilityResult = await checkTrainerAvailability(
            trainerId,
            checkDateTime.toISOString(),
            sessionDuration,
            gymId
          );

          if (availabilityResult.success && availabilityResult.data?.available) {
            setSuggestedDateTime({
              date: checkDate.toISOString().split('T')[0],
              time: timeSlot
            });
            return;
          }
        }
      }

      // If no slot found, suggest tomorrow at 10:00 anyway
      const tomorrow = addDays(now, 1);
      setSuggestedDateTime({
        date: tomorrow.toISOString().split('T')[0],
        time: '10:00'
      });
    } catch (error) {
      console.error('Error finding available slot:', error);
      // Fallback to tomorrow 10:00
      const tomorrow = addDays(new Date(), 1);
      setSuggestedDateTime({
        date: tomorrow.toISOString().split('T')[0],
        time: '10:00'
      });
    } finally {
      setIsLoadingSuggestion(false);
    }
  }, [trainerId, gymId, packageData.gym_package.session_duration_minutes, timeSlots]);

  // Initialize sessions - preserve existing session data when count changes
  useEffect(() => {
    setSessions(prevSessions => {
      const newSessions: SessionSlot[] = [];

      for (let i = 1; i <= currentSessionCount; i++) {
        // Try to find existing session data
        const existingSession = prevSessions.find(s => s.sessionNumber === i);

        if (existingSession) {
          // Keep existing session data
          newSessions.push(existingSession);
        } else {
          // Create new session
          newSessions.push({
            id: `session-${i}`,
            sessionNumber: i,
            isSelected: false,
            isAutoFilled: false
          });
        }
      }

      return newSessions;
    });
  }, [currentSessionCount]);

  // Find suggested time when component mounts
  useEffect(() => {
    if (trainerId && gymId && packageData) {
      findNextAvailableSlot();
    }
  }, [trainerId, gymId, packageData, findNextAvailableSlot]);

  // Add session
  const addSession = () => {
    if (currentSessionCount < totalSessions) {
      setCurrentSessionCount(prev => prev + 1);
    }
  };

  // Remove session
  const removeSession = () => {
    if (currentSessionCount > 1) {
      setCurrentSessionCount(prev => prev - 1);
    }
  };

  // Update session date/time
  const updateSessionDateTime = (sessionId: string, date: string, time: string) => {
    if (!date || !time) return;

    const dateTime = new Date(`${date}T${time}`);
    const dayOfWeek = dateTime.getDay();

    setSessions(prev => prev.map(session =>
      session.id === sessionId
        ? {
            ...session,
            date,
            time: dateTime.toISOString(),
            dayOfWeek,
            isSelected: true,
            isAutoFilled: false
          }
        : session
    ));
  };

  // Clear all sessions
  const clearAllSessions = () => {
    setSessions(prev => prev.map(session => ({
      ...session,
      date: undefined,
      time: undefined,
      dayOfWeek: undefined,
      isSelected: false,
      isAutoFilled: false
    })));
  };

  // Auto-fill weekly pattern
  const autoFillWeekly = () => {
    const selectedSessions = sessions.filter(s => s.isSelected && s.date && s.time && s.dayOfWeek !== undefined);

    if (selectedSessions.length === 0) {
      toast.error('Önce en az bir seans seçin!');
      return;
    }

    // Extract pattern from selected sessions (day of week and time)
    const pattern = selectedSessions.map(s => ({
      dayOfWeek: s.dayOfWeek!,
      time: s.time!
    }));

    // Find the latest selected date as starting point
    const latestDate = selectedSessions
      .map(s => new Date(s.date!))
      .sort((a, b) => b.getTime() - a.getTime())[0]; // Sort descending to get latest

    // Start from next week after the latest selected date
    const startWeek = 1; // Start from 1 week after the latest date
    let patternIndex = 0;

    setSessions(prev => prev.map(session => {
      if (session.isSelected) return session; // Keep manually selected sessions

      const currentPattern = pattern[patternIndex % pattern.length];

      // Calculate the week offset for this unselected session
      const weekOffset = startWeek + Math.floor(patternIndex / pattern.length);
      const baseDate = addWeeks(latestDate, weekOffset);

      // Adjust to the correct day of week
      const dayDiff = currentPattern.dayOfWeek - baseDate.getDay();
      const finalDate = addDays(baseDate, dayDiff);

      patternIndex++;

      return {
        ...session,
        date: finalDate.toISOString().split('T')[0],
        time: currentPattern.time,
        dayOfWeek: currentPattern.dayOfWeek,
        isSelected: true,
        isAutoFilled: true
      };
    }));
  };



  // Confirm sessions
  const confirmSessions = () => {
    const confirmedSessions = sessions
      .filter(s => s.isSelected && s.date && s.time)
      .map(s => {
        // Combine date and time properly for Turkish timezone
        const dateStr = s.date!; // YYYY-MM-DD format
        const timeStr = format(new Date(s.time!), 'HH:mm'); // Extract time from ISO string

        // Create proper datetime for Turkish timezone
        const combinedDateTime = new Date(`${dateStr}T${timeStr}:00`);

   

        return {
          date: dateStr,
          time: combinedDateTime.toISOString()
        };
      });

  
    if (confirmedSessions.length === 0) {
      toast.error('En az bir seans seçin!');
      return;
    }

    onSessionsConfirm(confirmedSessions);
  };

  const selectedSessionsCount = sessions.filter(s => s.isSelected).length;

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <Card className="border-0 bg-gradient-to-r from-primary/5 to-primary/10 shadow-sm">
        <CardHeader className="pb-6">
          <div className="space-y-6">
            {/* Title and Status */}
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold tracking-tight">Akıllı Seans Planlayıcı</h2>
                <p className="text-sm text-muted-foreground">
                  {selectedSessionsCount > 0
                    ? `${selectedSessionsCount} seans seçildi`
                    : 'Seanslarınızı planlayın'}
                </p>
              </div>

              {/* Session Counter */}
              <div className="flex items-center gap-2 rounded-lg bg-background/80 px-4 py-2 shadow-sm">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={removeSession}
                  disabled={currentSessionCount <= 1}
                  className="h-8 w-8 p-0"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-2 px-2">
                  <span className="text-sm font-medium text-muted-foreground">Seans:</span>
                  <span className="text-lg font-bold">{currentSessionCount}</span>
                  <span className="text-sm text-muted-foreground">/ {totalSessions}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={addSession}
                  disabled={currentSessionCount >= totalSessions}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={autoFillWeekly}
                disabled={selectedSessionsCount === 0}
                className="bg-background/80 hover:bg-background"
              >
                <Zap className="mr-2 h-4 w-4" />
                Haftalık Otomatik Doldur
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={clearAllSessions}
                disabled={selectedSessionsCount === 0}
                className="bg-background/80 hover:bg-background"
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Seansları Temizle
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Sessions Grid */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Seans Detayları</h3>
          <div className="text-sm text-muted-foreground">
            {currentSessionCount} / {totalSessions} seans gösteriliyor
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
          {sessions.map(session => (
            <Card
              key={session.id}
              className={`group relative overflow-hidden border-2 transition-all duration-200 ${
                session.isSelected
                  ? 'border-primary bg-primary/5 shadow-md'
                  : 'border-border hover:border-primary/50 hover:shadow-sm'
              }`}
            >
              {/* Session Header */}
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between gap-3">
                  {/* Session Number & Status */}
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full text-sm font-bold transition-colors ${
                        session.isSelected
                          ? 'bg-primary text-primary-foreground shadow-sm'
                          : 'bg-muted text-muted-foreground group-hover:bg-primary/10'
                      }`}
                    >
                      {session.sessionNumber}
                    </div>
                    <div className="min-w-0 flex-1">
                      {session.isSelected && session.date && session.time ? (
                        <div className="mt-1 space-y-1">
                          <p className="text-sm font-medium text-primary">
                            {format(new Date(session.date), 'dd MMMM yyyy, EEEE', { locale: tr })}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {format(new Date(session.time), 'HH:mm')}
                          </p>
                        </div>
                      ) : (
                        session.sessionNumber === 1 && (
                          <div className="mt-1">
                            <p className="text-xs text-muted-foreground">
                              {isLoadingSuggestion
                                ? 'Önerilen tarih aranıyor...'
                                : suggestedDateTime
                                  ? `Önerilen: ${format(new Date(suggestedDateTime.date), 'dd MMM', { locale: tr })} ${suggestedDateTime.time}`
                                  : 'Tarih ve saat seçin'}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Action Badges & Buttons */}
                  <div className="flex flex-col items-end gap-2">
                    {session.isAutoFilled && (
                      <Badge variant="secondary" className="text-xs">
                        <Zap className="mr-1 h-3 w-3" />
                        Otomatik
                      </Badge>
                    )}
                    {session.sessionNumber === 1 &&
                      !session.isSelected &&
                      suggestedDateTime && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateSessionDateTime(
                              session.id,
                              suggestedDateTime.date,
                              suggestedDateTime.time
                            )
                          }
                          className="h-8 text-xs"
                          disabled={isLoadingSuggestion}
                        >
                          Önerilen Seç
                        </Button>
                      )}
                  </div>
                </div>
              </CardHeader>

              {/* Form Fields */}
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* Date Selection */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>Tarih</span>
                    </div>
                    <Input
                      type="date"
                      placeholder="Tarih seçin"
                      value={session.date?.split('T')[0] || ''}
                      onChange={e => {
                        if (e.target.value) {
                          // Only update date, don't auto-select time
                          setSessions(prev => prev.map(s =>
                            s.id === session.id
                              ? {
                                  ...s,
                                  date: e.target.value,
                                  // Keep existing time if available, otherwise don't set any time
                                  ...(s.time && {
                                    time: s.time,
                                    dayOfWeek: new Date(`${e.target.value}T${format(new Date(s.time), 'HH:mm')}`).getDay(),
                                    isSelected: true
                                  })
                                }
                              : s
                          ));
                        }
                      }}
                      className="w-full transition-colors focus:border-primary"
                    />
                  </div>

                  {/* Time Selection */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>Saat</span>
                    </div>
                    <Select
                      value={
                        session.time ? format(new Date(session.time), 'HH:mm') : ''
                      }
                      onValueChange={value => {
                        if (value) {
                          const currentDate =
                            session.date?.split('T')[0] ||
                            suggestedDateTime?.date ||
                            addDays(new Date(), 1).toISOString().split('T')[0];
                          updateSessionDateTime(session.id, currentDate, value);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full transition-colors focus:border-primary">
                        <SelectValue placeholder="Saat seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {timeSlots.map((time: string) => (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Confirmation Section */}
      <Card className="border-dashed">
        <CardContent className="py-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-1">
              <h4 className="font-medium">Seansları Onayla</h4>
              <p className="text-sm text-muted-foreground">
                {selectedSessionsCount > 0
                  ? `${selectedSessionsCount} seans seçildi ve onaylanmaya hazır`
                  : 'Onaylamak için en az bir seans seçin'}
              </p>
            </div>
            <Button
              onClick={confirmSessions}
              disabled={selectedSessionsCount === 0}
              size="lg"
              className="min-w-[200px] shadow-sm"
            >
              {selectedSessionsCount > 0
                ? `${selectedSessionsCount} Seansı Onayla`
                : 'Seans Seçin'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
