'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserPlus } from 'lucide-react';
import { gymToasts } from '@/lib/utils/toast-utils';
import { sendTrainerJoinRequest } from '@/lib/actions/gym_invitations/invitation-actions';

interface TrainerJoinButtonProps {
  gymId: string;
  gymName: string;
  trainerStatus?: 'none' | 'pending' | 'active';
}

export function TrainerJoinButton({ gymId, trainerStatus = 'none' }: TrainerJoinButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [localTrainerStatus, setLocalTrainerStatus] = useState(trainerStatus);

  const handleTrainerJoinRequest = async () => {
    if (isLoading || localTrainerStatus !== 'none') return;

    setIsLoading(true);
    try {
      const result = await sendTrainerJoinRequest(gymId);

      if (result.success) {
        gymToasts.trainerJoinSuccess();
        setLocalTrainerStatus('pending');
      } else {
        gymToasts.memberJoinError();
      }
    } catch (error) {
      console.error('Trainer join request error:', error);
      gymToasts.memberJoinError();
    } finally {
      setIsLoading(false);
    }
  };

  // Duruma göre buton içeriği
  const getButtonContent = () => {
    switch (localTrainerStatus) {
      case 'pending':
        return {
          text: 'İstek Gönderildi',
          disabled: true,
          className: 'bg-yellow-600 hover:bg-yellow-700 text-white cursor-not-allowed',
        };
      case 'active':
        return {
          text: 'Antrenör',
          disabled: true,
          className: 'bg-green-600 hover:bg-green-700 text-white cursor-not-allowed',
        };
      default:
        return {
          text: isLoading ? 'Gönderiliyor...' : 'Antrenör Olarak Katıl',
          disabled: isLoading,
          className: 'bg-orange-600 hover:bg-orange-700 text-white',
        };
    }
  };

  const buttonContent = getButtonContent();

  return (
    <Button
      onClick={handleTrainerJoinRequest}
      disabled={buttonContent.disabled}
      className={buttonContent.className}
    >
      <UserPlus className="mr-2 h-4 w-4" />
      {buttonContent.text}
    </Button>
  );
}
