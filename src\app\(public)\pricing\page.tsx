import { Suspense } from 'react';
import { PricingHero } from '@/components/public/pricing/pricing-hero';
import { PricingComparison } from '@/components/public/pricing/pricing-comparison';
import { PricingFAQ } from '@/components/public/pricing/pricing-faq';
import { PricingCTA } from '@/components/public/pricing/pricing-cta';
import { PricingModern } from '@/components/public/home/<USER>';
import { getAllPlatformPackagesCached } from '@/lib/actions/business/platform-packages';

// ISR: veriyi belirli aralıklarla yenileyerek build-time statik çıktı üretir
export const revalidate = 3600; // 1 saat
// PPR pilotu: statik kabuk + dinamik adaları stream etmek için
export const experimental_ppr = true;

export default async function PricingPage() {
  return (
    <main className="flex-1">
      <PricingHero />
      <Suspense fallback={<PackagesFallback />}> 
        {/* Dinamik veri gerektiren bölüm */}
        <PackagesSection />
      </Suspense>
      <PricingFAQ />
      <PricingCTA />
    </main>
  );
}

async function PackagesSection() {
  const res = await getAllPlatformPackagesCached();
  const packages = res.data ?? [];
  return (
    <>
      <PricingModern showHeader={false} compact={false} packages={packages} />
      <PricingComparison packages={packages} />
    </>
  );
}

function PackagesFallback() {
  return (
    <div className="mx-auto w-full max-w-5xl animate-pulse space-y-6 p-4">
      <div className="h-8 w-40 rounded bg-muted" />
      <div className="h-32 w-full rounded bg-muted" />
      <div className="h-6 w-64 rounded bg-muted" />
      <div className="h-48 w-full rounded bg-muted" />
    </div>
  );
}
