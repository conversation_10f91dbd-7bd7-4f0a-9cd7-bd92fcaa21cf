"use client";

import { toast } from 'sonner';

export type NotifyOptions = {
  duration?: number;
  description?: string;
};

export const notify = (message: string, opts?: NotifyOptions) => toast(message, opts);
export const notifySuccess = (message: string, opts?: NotifyOptions) => toast.success(message, opts);
export const notifyError = (message: string, opts?: NotifyOptions) => toast.error(message, opts);
export const notifyInfo = (message: string, opts?: NotifyOptions) => toast.info(message, opts);
