import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Zap, Phone, Mail } from "lucide-react";
import Link from "next/link";

export function AboutCTA() {
  return (
    <section className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA */}
          <div className="bg-background rounded-xl p-8 md:p-12 border border-border mb-12">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                Salonunuzu Dijitalleştirmeye Hazır mısınız?
              </h2>
              <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                Sportiva ile spor salonu yönetiminizi modernleştirin, üye deneyimini iyileştirin
                ve işletme verimliliğinizi artırın. Hemen başlayın!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <Button asChild size="lg" className="shadow-lg">
                  <Link href="/auth/login">
                    <Zap className="mr-2 h-5 w-5" />
                    Ücretsiz Deneyin
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/onboarding">
                    Fiyatları İnceleyin
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>

              <p className="text-sm text-muted-foreground">
                ✅ 14 gün ücretsiz deneme • ✅ Kredi kartı gerekmez • ✅ Anında kurulum
              </p>
            </div>
          </div>

          {/* Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Contact */}
            <div className="bg-background rounded-xl p-8 border border-border">
              <h3 className="text-xl font-semibold text-primary mb-6">
                Bizimle İletişime Geçin
              </h3>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Telefon</p>
                    <p className="text-muted-foreground">+90 (*************</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">E-posta</p>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </div>

              <Button variant="outline" className="w-full mt-6">
                İletişim Formu
              </Button>
            </div>

            {/* Support */}
            <div className="bg-background rounded-xl p-8 border border-border">
              <h3 className="text-xl font-semibold text-primary mb-6">
                Destek & Yardım
              </h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-foreground mb-2">Teknik Destek</h4>
                  <p className="text-sm text-muted-foreground">
                    7/24 teknik destek ekibimiz size yardımcı olmaya hazır
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">Eğitim & Onboarding</h4>
                  <p className="text-sm text-muted-foreground">
                    Ücretsiz eğitim ve kurulum desteği ile hızla başlayın
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-foreground mb-2">Dokümantasyon</h4>
                  <p className="text-sm text-muted-foreground">
                    Kapsamlı kullanım kılavuzu ve video eğitimler
                  </p>
                </div>
              </div>

              <Button variant="outline" className="w-full mt-6">
                Yardım Merkezi
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
