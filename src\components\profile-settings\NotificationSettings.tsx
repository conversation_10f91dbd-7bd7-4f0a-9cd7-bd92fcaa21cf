'use client';

import { useState, useTransition } from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bell } from 'lucide-react';

import { toast } from 'sonner';
import { upSertUserSettings } from '@/lib/actions/user/user-settings-actions';
import { UserSettings } from '@/types/database/tables';
import { NotificationCategories } from '@/types/database/enums';

interface NotificationSettingsProps {
  settings: UserSettings;
}

export function NotificationSettings({ settings }: NotificationSettingsProps) {
  // Notification categories'i güvenli şekilde parse et
  const parseNotificationCategories = (
    categories: any
  ): NotificationCategories => {
    if (typeof categories === 'object' && categories !== null) {
      return {
        appointments: categories.appointments ?? true,
        payments: categories.payments ?? true,
        announcements: categories.announcements ?? true,
        reminders: categories.reminders ?? true,
      };
    }
    return {
      appointments: true,
      payments: true,
      announcements: true,
      reminders: true,
    };
  };

  const [localSettings, setLocalSettings] = useState({
    email_notifications: settings.email_notifications ?? true,
    push_notifications: settings.push_notifications ?? true,
    notification_categories: parseNotificationCategories(
      settings.notification_categories
    ),
  });

  const [isPending, startTransition] = useTransition();

  const handleToggle = (key: string, value: boolean) => {
    const newSettings = { ...localSettings };

    if (key === 'email_notifications' || key === 'push_notifications') {
      newSettings[key] = value;
    } else {
      newSettings.notification_categories = {
        ...newSettings.notification_categories,
        [key]: value,
      };
    }

    setLocalSettings(newSettings);

    startTransition(async () => {
      try {
        const result = await upSertUserSettings(newSettings);
        if (result.success) {
          toast.success('Bildirim ayarları güncellendi');
        } else {
          toast.error(result.error || 'Ayarlar güncellenirken hata oluştu');
          // Hata durumunda eski ayarlara geri dön
          setLocalSettings({
            email_notifications: settings.email_notifications ?? true,
            push_notifications: settings.push_notifications ?? true,
            notification_categories: parseNotificationCategories(
              settings.notification_categories
            ),
          });
        }
      } catch (error) {
        toast.error('Beklenmeyen bir hata oluştu');
        console.error('Notification settings update error:', error);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Bildirim Ayarları
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* E-posta Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">E-posta Bildirimleri</Label>
          <Switch
            checked={localSettings.email_notifications}
            onCheckedChange={value =>
              handleToggle('email_notifications', value)
            }
            disabled={isPending}
          />
        </div>

        {/* Push Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Push Bildirimleri</Label>
          <Switch
            checked={localSettings.push_notifications}
            onCheckedChange={value => handleToggle('push_notifications', value)}
            disabled={isPending}
          />
        </div>

        {/* Randevu Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Randevu Bildirimleri</Label>
          <Switch
            checked={localSettings.notification_categories.appointments}
            onCheckedChange={value => handleToggle('appointments', value)}
            disabled={isPending}
          />
        </div>

        {/* Ödeme Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Ödeme Bildirimleri</Label>
          <Switch
            checked={localSettings.notification_categories.payments}
            onCheckedChange={value => handleToggle('payments', value)}
            disabled={isPending}
          />
        </div>

        {/* Duyuru Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Duyuru Bildirimleri</Label>
          <Switch
            checked={localSettings.notification_categories.announcements}
            onCheckedChange={value => handleToggle('announcements', value)}
            disabled={isPending}
          />
        </div>

        {/* Hatırlatma Bildirimleri */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Hatırlatma Bildirimleri</Label>
          <Switch
            checked={localSettings.notification_categories.reminders}
            onCheckedChange={value => handleToggle('reminders', value)}
            disabled={isPending}
          />
        </div>
      </CardContent>
    </Card>
  );
}
