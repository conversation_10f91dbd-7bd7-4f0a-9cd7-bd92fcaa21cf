import { ChevronDown } from "lucide-react";

export function PricingFAQ() {
  const faqs = [
    {
      question: "Ücretsiz deneme süresi nasıl çalışır?",
      answer: "14 gün boyunca tüm Pro özelliklerini ücretsiz deneyebilirsiniz. Kredi kartı bilgisi gerekmez. Deneme süresi sonunda otomatik ücretlendirme yapılmaz."
    },
    {
      question: "Plan değişikliği yapabilir miyim?",
      answer: "Evet, istediğiniz zaman planınızı yükseltebilir veya düşürebilirsiniz. Değişiklik hemen geçerli olur ve ücretlendirme pro-rata olarak hesaplanır."
    },
    {
      question: "İptal politikanız nedir?",
      answer: "İstediğiniz zaman aboneliğinizi iptal edebilirsiniz. İptal işlemi sonrası mevcut dönem sonuna kadar hizmet almaya devam edersiniz."
    },
    {
      question: "<PERSON>eri güvenliği nasıl sağlanıyor?",
      answer: "Tüm verileriniz SSL şifreleme ile korunur. Düzenli yedeklemeler alınır ve KVKK uyumlu veri işleme politikalarımız vardır."
    },
    {
      question: "Kurulum desteği var mı?",
      answer: "Evet, tüm planlarımızda ücretsiz kurulum desteği bulunur. Uzman ekibimiz salonunuzu sisteme entegre etmenize yardımcı olur."
    },
    {
      question: "Mobil uygulama dahil mi?",
      answer: "Evet, hem salon yöneticileri hem de üyeler için iOS ve Android uygulamalarımız tüm planlarımızda dahildir."
    },
    {
      question: "Fatura ve ödeme seçenekleri nelerdir?",
      answer: "Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Aylık veya yıllık ödeme seçenekleri mevcuttur. Yıllık ödemede %20 indirim uygulanır."
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Sıkça Sorulan Sorular
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Fiyatlandırma ve hizmetlerimiz hakkında merak ettiğiniz sorular
            </p>
          </div>

          {/* FAQ Items */}
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <details
                key={index}
                className="bg-muted/30 rounded-xl border border-border overflow-hidden"
                {...(index === 0 ? { open: true } : {})}
              >
                <summary className="w-full p-6 text-left flex items-center justify-between hover:bg-muted/50 transition-colors cursor-pointer">
                  <h3 className="text-lg font-semibold text-foreground pr-4">
                    {faq.question}
                  </h3>
                  <ChevronDown className="h-5 w-5 text-primary flex-shrink-0" />
                </summary>
                <div
                  id={`faq-answer-${index}`}
                  className="px-6 pb-6 text-muted-foreground leading-relaxed"
                >
                  {faq.answer}
                </div>
              </details>
            ))}
          </div>

          {/* Contact CTA */}
          <div className="mt-16 text-center">
            <h3 className="text-xl font-semibold text-primary mb-4">
              Başka sorularınız mı var?
            </h3>
            <p className="text-muted-foreground mb-6">
              Uzman ekibimiz size yardımcı olmaktan mutluluk duyar.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Email Gönder
              </a>
              <a
                href="tel:+905001234567"
                className="inline-flex items-center justify-center px-6 py-3 border border-border rounded-lg hover:bg-muted/50 transition-colors"
              >
                Telefon: 0500 123 45 67
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
