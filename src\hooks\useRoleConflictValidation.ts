import { useState, useCallback } from 'react';
import { checkUserRoleConflicts, RoleConflictResult } from '@/lib/auth/role-conflict-validation';

/**
 * Rol çakışma kontrolü için hook
 */
export function useRoleConflictValidation() {
  const [conflictData, setConflictData] = useState<RoleConflictResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Kullanıcının mevcut rollerini kontrol eder
   */
  const checkConflicts = useCallback(async (userId?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await checkUserRoleConflicts(userId);
      
      if (result.success) {
        setConflictData(result.data);
      } else {
        setError(result.error || 'Rol kontrolü yapılamadı');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Beklenmeyen hata');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Seçilen rollerde çakışma var mı kontrol eder
   */
  const validateRoleSelection = useCallback((selectedRoles: string[]): {
    isValid: boolean;
    conflictMessage?: string;
  } => {
    const hasCompanyManager = selectedRoles.includes('company_manager');
    const hasGymManager = selectedRoles.includes('gym_manager');

    // Seçilen roller arasında çakışma kontrolü
    if (hasCompanyManager && hasGymManager) {
      return {
        isValid: false,
        conflictMessage: 'Aynı anda hem şirket yöneticisi hem salon yöneticisi rolü seçilemez.'
      };
    }

    // Mevcut rollerle çakışma kontrolü
    if (conflictData) {
      if (hasCompanyManager && conflictData.hasGymManagerRole) {
        return {
          isValid: false,
          conflictMessage: 'Zaten salon yöneticisisiniz. Şirket yöneticisi rolü alamazsınız.'
        };
      }

      if (hasGymManager && conflictData.hasCompanyManagerRole) {
        return {
          isValid: false,
          conflictMessage: 'Zaten şirket yöneticisisiniz. Salon yöneticisi rolü alamazsınız.'
        };
      }
    }

    return { isValid: true };
  }, [conflictData]);

  /**
   * Belirli bir rolün seçilebilir olup olmadığını kontrol eder
   */
  const isRoleSelectable = useCallback((role: string): boolean => {
    if (!conflictData) return true;

    if (role === 'company_manager' && conflictData.hasGymManagerRole) {
      return false;
    }

    if (role === 'gym_manager' && conflictData.hasCompanyManagerRole) {
      return false;
    }

    return true;
  }, [conflictData]);

  /**
   * Rol için uyarı mesajı döndürür
   */
  const getRoleWarningMessage = useCallback((role: string): string | null => {
    if (!conflictData) return null;

    if (role === 'company_manager' && conflictData.hasGymManagerRole) {
      return 'Salon yöneticisi olduğunuz için şirket yöneticisi rolü alamazsınız.';
    }

    if (role === 'gym_manager' && conflictData.hasCompanyManagerRole) {
      return 'Şirket yöneticisi olduğunuz için salon yöneticisi rolü alamazsınız.';
    }

    return null;
  }, [conflictData]);

  return {
    conflictData,
    isLoading,
    error,
    checkConflicts,
    validateRoleSelection,
    isRoleSelectable,
    getRoleWarningMessage,
  };
}
