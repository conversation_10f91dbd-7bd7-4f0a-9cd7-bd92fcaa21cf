'use client';

import { useMemo, useState } from 'react';
import { useParams, usePathname } from 'next/navigation';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { type SidebarMode } from '@/lib/constants';
import { MobileNavigation } from '../mobile-navigation';
import { MobileBreadcrumbs } from '../mobile-breadcrumbs';
import { useDashboard } from '@/context/dashboard-context';

/**
 * Dashboard Mobile Navigation
 * - Sadece dashboard sayfalarında kullanılır
 * - Role-based navigation ve gym selection içerir
 * - Breadcrumbs ve sidebar navigation'ı birleştirir
 */
export function DashboardMobileNavigation() {
  const {
    roleOptions,
    isCompanyManager,
    isGymManager,
    isTrainer,
    isMember,
  } = useDashboard();
  const pathname = usePathname();
  const params = useParams();
  const [isOpen, setIsOpen] = useState(false);

  const currentGymId = params.gymId as string;

  const sidebarMode = useMemo(() => {
    // Path öncelikli seçim: bulunduğun sayfa hangi alanı temsil ediyorsa onu zorla
    if (pathname.includes('/dashboard/company')) return 'company_manager';
    if (pathname.includes('/dashboard/trainer')) return 'trainer';
    if (pathname.includes('/dashboard/gym')) {
      if (isGymManager) return 'gym_manager';
      if (isTrainer) return 'trainer';
      if (isCompanyManager) return 'company_manager';
    }
    if (pathname.includes('/dashboard/member') || isMember) return 'member';
    // Fallback: mevcut rollere göre belirle
    if (isCompanyManager) return 'company_manager';
    if (isGymManager) return 'gym_manager';
    if (isTrainer) return 'trainer';
    if (isMember) return 'member';
    return (roleOptions[0]?.role as SidebarMode) || 'member';
  }, [pathname, isCompanyManager, isGymManager, isTrainer, isMember, roleOptions]) as SidebarMode;

  const handleNavigate = () => {
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          aria-label="Dashboard navigasyon menüsünü aç"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <SheetHeader className="sr-only">
          <SheetTitle>Dashboard Navigasyon Menüsü</SheetTitle>
          <SheetDescription>
            Dashboard navigasyonu, rol ve salon seçimi
          </SheetDescription>
        </SheetHeader>

        <div className="flex h-full flex-col">
          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Dashboard Navigation Menu */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Dashboard Menü</h3>
                <MobileNavigation
                  mode={sidebarMode}
                  gymId={currentGymId}
                  onNavigate={handleNavigate}
                />
              </div>

              {/* Role and Gym Selection */}
              <div className="space-y-4">
                <h3 className="text-muted-foreground text-sm font-medium">
                  Rol ve Salon Seçimi
                </h3>
                <MobileBreadcrumbs />
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
