import { Metadata } from 'next';
import { StructuredData } from '@/components/seo/structured-data';

// Force dynamic rendering due to auth checks in header

import { getSiteUrl } from '@/lib/utils/url-utils';
import { FeaturesHero } from './features-hero';
import { FeaturesGrid } from './features-grid';
import { FeaturesCTA } from './features-cta';

export const metadata: Metadata = {
  title: 'Özellikler | Sportiva - Spor Salonu Yönetim Sistemi',
  description:
    'Sportiva spor salonu yönetim sisteminin tüm özelliklerini keşfedin. Üye yönetimi, ödeme sistemi, analitik raporlar, randevu sistemi ve daha fazlası.',
  keywords: [
    'spor salonu yönetimi',
    'üye yönetimi',
    'ödeme sistemi',
    'analitik raporlar',
    'randevu sistemi',
    'salon keşfi',
    'fitness yönetimi',
    'spor salonu yazılımı',
    'dijital dönüşüm',
    'sportiva özellikler',
  ],
  openGraph: {
    title: 'Özellikler | Sportiva - Spor Salonu Yönetim Sistemi',
    description:
      'Sportiva spor salonu yönetim sisteminin tüm özelliklerini keşfedin. Üye yönetimi, ödeme sistemi, analitik raporlar ve daha fazlası.',
    url: `${getSiteUrl()}/features`,
    siteName: 'Sportiva',
    images: [
      {
        url: '/og-features.jpg',
        width: 1200,
        height: 630,
        alt: 'Sportiva Özellikler',
      },
    ],
    locale: 'tr_TR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Özellikler | Sportiva',
    description:
      'Sportiva spor salonu yönetim sisteminin tüm özelliklerini keşfedin. Üye yönetimi, ödeme sistemi, analitik raporlar ve daha fazlası.',
    images: ['/og-features.jpg'],
  },
  alternates: {
    canonical: '/features',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function FeaturesPage() {
  return (
    <>
      <StructuredData
        type="WebPage"
        data={{
          name: 'Sportiva Özellikler',
          description:
            'Sportiva spor salonu yönetim sisteminin tüm özelliklerini keşfedin.',
          url: `${getSiteUrl()}/features`,
        }}
      />
      <main className="flex-1">
        <FeaturesHero />
        <FeaturesGrid />
        <FeaturesCTA />
      </main>
    </>
  );
}
