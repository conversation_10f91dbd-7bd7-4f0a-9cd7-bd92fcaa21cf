'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { Search, Send, Inbox } from 'lucide-react';
import { useCallback } from 'react';

// Empty State Component for Incoming Invitations
export function EmptyIncomingInvitations() {
  const handleSearchClick = useCallback(() => {
    // Analytics veya tracking için kullanılabilir
  }, []);

  return (
    <Card className="bg-muted/30 rounded-xl border-2 border-dashed border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:border-blue-800 dark:from-blue-950/30 dark:to-blue-900/30">
      <CardContent className="px-8 py-16 text-center">
        <div className="mx-auto max-w-md space-y-6">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50">
            <Inbox className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>

          <div className="space-y-3">
            <h3 className="text-foreground text-xl font-semibold">
              Henüz Davet Almadınız
            </h3>
            <p className="text-muted-foreground">
              Salonlar size davet gönderdiğinde burada görünecek. Yeni salonları
              keşfetmek için salon arama sayfasını ziyaret edin.
            </p>
          </div>

          <Link href="/findGym" onClick={handleSearchClick}>
            <Button className="shadow-lg duration-300">
              <Search className="mr-2 h-4 w-4" />
              Salon Ara
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

// Empty State Component for Outgoing Invitations
export function EmptyOutgoingInvitations() {
  const handleSearchClick = useCallback(() => {
    // Analytics veya tracking için kullanılabilir
  }, []);

  return (
    <Card className="bg-muted/30 rounded-xl border-2 border-dashed border-green-200 bg-gradient-to-br from-green-50 to-green-100 dark:border-green-800 dark:from-green-950/30 dark:to-green-900/30">
      <CardContent className="px-8 py-16 text-center">
        <div className="mx-auto max-w-md space-y-6">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/50">
            <Send className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>

          <div className="space-y-3">
            <h3 className="text-foreground text-xl font-semibold">
              Henüz Davet Göndermediniz
            </h3>
            <p className="text-muted-foreground">
              Beğendiğiniz salonlara katılım isteği gönderebilirsiniz. Salon
              arama sayfasından başlayın.
            </p>
          </div>

          <Link href="/findGym" onClick={handleSearchClick}>
            <Button className="shadow-lg duration-300">
              <Search className="mr-2 h-4 w-4" />
              Salon Ara
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
