/**
 * Dashboard Types & Interfaces
 *
 * Centralized type definitions for manager dashboard functionality
 * Following Clean Code principles - single responsibility for type definitions
 */

export type GymStats = {
  activeMembers: number;
  totalPackages: number;
  totalRevenue: number;
};

export type GymAnalyticsData = {
  totalMembers: number;
  activeMembers: number;
  monthlyRevenue: number;
  totalRevenue: number;
  activePackages: number;
  memberGrowthRate: number;
  revenueGrowthRate: number;
  memberGrowthData: Array<{
    month: string;
    members: number;
    newMembers: number;
  }>;
  revenueData: Array<{
    month: string;
    revenue: number;
  }>;
  topPackages: Array<{
    name: string;
    sales: number;
    revenue: number;
  }>;
};

export type GymRevenueRecord = {
  id: string;
  memberName: string;
  packageName: string;
  purchasePrice: number;
  startDate: string;
  endDate: string | null;
  status: string;
  createdAt: string | null;
};

export type GymRevenueReport = {
  totalRevenue: number;
  totalRecords: number;
  records: GymRevenueRecord[];
};

export type ManagerFinancialSummary = {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  revenueByGym: Array<{
    gymId: string;
    gymName: string;
    revenue: number;
    percentage: number;
  }>;
  revenueByPeriod: Array<{
    period: string;
    revenue: number;
    memberCount: number;
  }>;
  topPackages: Array<{
    packageName: string;
    sales: number;
    revenue: number;
  }>;
};

export type ManagerOverview = {
  totalGyms: number;
  totalMembers: number;
  totalRevenue: number;
  monthlyGrowth: number;
  unreadNotifications: number;
  pendingActions: number;
  topPerformingGym: {
    id: string;
    name: string;
    revenue: number;
    memberCount: number;
  } | null;
};
