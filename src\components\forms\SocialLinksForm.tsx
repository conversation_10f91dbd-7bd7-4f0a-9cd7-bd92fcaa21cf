'use client';

import { useState } from 'react';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGlobe,
  faPlus,
  faTrash,
} from '@fortawesome/free-solid-svg-icons';
import {
  faInstagram,
  faFacebook,
  faXTwitter,
  faYoutube,
  faTiktok,
} from '@fortawesome/free-brands-svg-icons';
import { SOCIAL_PLATFORMS, SocialPlatform, INPUT_RULES } from '@/lib/utils/form-validation';

interface SocialLinksData {
  [key: string]: string;
}

interface SocialLinksFormProps {
  initialData?: SocialLinksData;
  onChange: (data: SocialLinksData) => void;
  disabled?: boolean;
}

export function SocialLinksForm({ initialData = {}, onChange, disabled = false }: SocialLinksFormProps) {
  const [socialLinks, setSocialLinks] = useState<SocialLinksData>(initialData);
  const [activePlatforms, setActivePlatforms] = useState<SocialPlatform[]>(
    Object.keys(initialData).filter(key => initialData[key]?.trim()) as SocialPlatform[]
  );

  // FontAwesome icon render fonksiyonu
  const renderIcon = (iconName: string) => {
    const className = 'w-6 h-6';
    switch (iconName) {
      case 'Website':
        return <FontAwesomeIcon icon={faGlobe} className={className} />;
      case 'Instagram':
        return <FontAwesomeIcon icon={faInstagram} className={className} />;
      case 'Facebook':
        return <FontAwesomeIcon icon={faFacebook} className={className} />;
      case 'X':
        return <FontAwesomeIcon icon={faXTwitter} className={className} />;
      case 'Youtube':
        return <FontAwesomeIcon icon={faYoutube} className={className} />;
      case 'Tiktok':
        return <FontAwesomeIcon icon={faTiktok} className={className} />;
      default:
        return <FontAwesomeIcon icon={faGlobe} className={className} />;
    }
  };

  const handleLinkChange = (platform: SocialPlatform, value: string) => {
    const updatedLinks = {
      ...socialLinks,
      [platform]: value,
    };

    // If value is empty, remove the platform
    if (!value.trim()) {
      delete updatedLinks[platform];
      setActivePlatforms(prev => prev.filter(p => p !== platform));
    }

    setSocialLinks(updatedLinks);
    onChange(updatedLinks);
  };

  const addPlatform = (platform: SocialPlatform) => {
    if (!activePlatforms.includes(platform)) {
      setActivePlatforms(prev => [...prev, platform]);
      setSocialLinks(prev => ({ ...prev, [platform]: '' }));
    }
  };

  const removePlatform = (platform: SocialPlatform) => {
    setActivePlatforms(prev => prev.filter(p => p !== platform));
    const updatedLinks = { ...socialLinks };
    delete updatedLinks[platform];
    setSocialLinks(updatedLinks);
    onChange(updatedLinks);
  };

  const availablePlatforms = Object.keys(SOCIAL_PLATFORMS).filter(
    platform => !activePlatforms.includes(platform as SocialPlatform)
  ) as SocialPlatform[];

  const getSocialValidationMessage = (platform: SocialPlatform): string => {
    switch (platform) {
      case 'website':
        return 'Geçerli bir web sitesi adresi giriniz';
      case 'instagram':
        return 'Instagram profil linkini giriniz (örn: https://instagram.com/kullaniciadi)';
      case 'facebook':
        return 'Facebook sayfa linkini giriniz (örn: https://facebook.com/sayfaadi)';
      case 'x':
        return 'X profil linkini giriniz (örn: https://x.com/kullaniciadi)';
      case 'youtube':
        return 'YouTube kanal linkini giriniz';
      case 'tiktok':
        return 'TikTok profil linkini giriniz';
      default:
        return 'Geçerli bir URL giriniz';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FontAwesomeIcon icon={faGlobe} className="w-5 h-5 text-primary" />
          Sosyal Medya Hesapları
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Active Platforms */}
        {activePlatforms.map(platform => {
          const config = SOCIAL_PLATFORMS[platform];
          return (
            <div key={platform} className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor={platform} className="flex items-center gap-2">
                  {renderIcon(config.icon)}
                  {config.label}
                </Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removePlatform(platform)}
                  disabled={disabled}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                </Button>
              </div>
              <EnhancedInput
                id={platform}
                name={`social_${platform}`}
                type="url"
                placeholder={config.placeholder}
                value={socialLinks[platform] || ''}
                onChange={(value) => handleLinkChange(platform, value)}
                formatter={INPUT_RULES.URL.formatter}
                validator={config.validator}
                maxLength={INPUT_RULES.URL.maxLength}
                validationMessage={getSocialValidationMessage(platform)}
                disabled={disabled}
              />
            </div>
          );
        })}

        {/* Add Platform Buttons */}
        {availablePlatforms.length > 0 && (
          <div className="pt-4 border-t">
            <Label className="text-sm font-medium mb-3 block">
              Sosyal medya hesabı ekle:
            </Label>
            <div className="flex flex-wrap gap-2">
              {availablePlatforms.map(platform => {
                const config = SOCIAL_PLATFORMS[platform];
                return (
                  <Button
                    key={platform}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addPlatform(platform)}
                    disabled={disabled}
                    className="flex items-center gap-2"
                  >
                    {renderIcon(config.icon)}
                    {config.label}
                    <FontAwesomeIcon icon={faPlus} className="h-3 w-3" />
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {activePlatforms.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FontAwesomeIcon icon={faGlobe} className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p className="text-sm">
              Henüz sosyal medya hesabı eklenmemiş.
            </p>
            <p className="text-xs mt-1">
              Yukarıdaki butonları kullanarak hesaplarınızı ekleyebilirsiniz.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
