'use client';

import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLef<PERSON>, Settings } from 'lucide-react';
import { useSettingsTitle } from '@/hooks/use-settings-title';

export function SettingsHeader() {
  const title = useSettingsTitle();

  return (
    <div className="container mx-auto">
      <div className="flex h-16 items-center justify-between px-4 lg:px-8">
        {/* Sol taraf - Panel'e dön butonu (sadece desktop'ta görünür) */}
        <div className="hidden lg:flex items-center gap-4">
          <Link href="/dashboard">
            <Button variant="outline" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Panel&apos;e Dön
            </Button>
          </Link>
        </div>

        {/* Merkez - Dinamik Başlık */}
        <div className="flex items-center gap-3 flex-1 justify-center">
          <div className="bg-primary/10 rounded-full p-2">
            <Settings className="text-primary h-5 w-5" />
          </div>
          <div>
            <h1 className="text-lg lg:text-xl font-semibold">{title}</h1>
          </div>
        </div>
      </div>
    </div>
  );
}
