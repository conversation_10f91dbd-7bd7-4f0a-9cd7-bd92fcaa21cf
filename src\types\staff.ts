// Types for staff management
export interface Staff {
  id: string;
  gym_id: string;
  name: string;
  surname: string;
  hire_date: string;
  salary_amount: number | null;
  staff_type: string;
  created_at: string | null;
  updated_at: string | null;
}

export interface CreateStaffData {
  name: string;
  surname: string;
  hire_date: string;
  salary_amount?: number;
  staff_type: string;
}

export interface UpdateStaffData {
  name?: string;
  surname?: string;
  hire_date?: string;
  salary_amount?: number;
  staff_type?: string;
}
