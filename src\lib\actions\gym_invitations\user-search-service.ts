'use server';
import { getSupabaseAdmin } from '@/lib/supabase/admin';
import { ApiResponse } from '@/types';
import { createAction } from '../core';
import { logger } from '@/lib/logger';
import { createAdminAction } from '../core/core';
import { UserSearchResult } from './invitation-types';

export interface TrainerSearchResult {
  profile_id: string;
  invite_code: string;
  specialization?: string | null;
  certification_level?: string | null;
  experience_years?: number | null;
  bio?: string | null;
  profiles: {
    full_name: string;
    email: string;
  };
}

export interface MemberSearchResult {
  profile_id: string;
  invite_code: string;
  age?: number | null;
  gender?: string | null;
  height_cm?: number | null;
  weight_kg?: number | null;
  fitness_goal?: string | null;
  profiles: {
    full_name: string;
    email: string;
  };
}

/**
 * Search users for invitation
 */
export async function searchUsers(
  searchTerm: string,
  gymId: string
): Promise<ApiResponse<UserSearchResult[]>> {
  return await createAdminAction(async (_, __, authUserId, adminClient) => {
    // Kullanıcıları ara (email veya full_name ile)
    const { data: users, error } = await adminClient
      .from('profiles')
      .select('id, full_name, email, avatar_url')
      .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      .limit(20);

    if (error) {
      logger.error('Kullanıcı arama hatası', error, {
        searchTerm,
        gymId,
        authUserId,
      });
      throw new Error('Kullanıcı arama sırasında hata oluştu.');
    }

    // Zaten üye olan kullanıcıları filtrele
    const userIds = users?.map(user => user.id) || [];
    if (userIds.length === 0) {
      return [];
    }

    const { data: existingMembers } = await adminClient
      .from('gym_memberships')
      .select('profile_id')
      .eq('gym_id', gymId)
      .in('profile_id', userIds);

    const existingMemberIds = new Set(
      existingMembers?.map(m => m.profile_id) || []
    );

    // Bekleyen davetleri kontrol et (gym_invite type)
    const { data: pendingInvitations } = await adminClient
      .from('gym_invitations')
      .select('profile_id')
      .eq('gym_id', gymId)
      .eq('status', 'pending')
      .eq('type', 'gym_invite')
      .in('profile_id', userIds);

    const pendingInvitationIds = new Set(
      pendingInvitations?.map(i => i.profile_id) || []
    );

    // Zaten üye olan veya bekleyen daveti olan kullanıcıları filtrele
    const filteredUsers = users?.filter(
      user =>
        !existingMemberIds.has(user.id) &&
        !pendingInvitationIds.has(user.id) &&
        user.id !== authUserId // Kendisini de filtrele
    );

    return filteredUsers || [];
  });
}


/**
 * Trainer koduna göre trainer arar
 */
export async function findTrainerByCode(
  trainerCode: string
): Promise<ApiResponse<TrainerSearchResult | null>> {
  return await createAction(async () => {
    const adminClient = getSupabaseAdmin();

    const { data: trainerData, error } = await adminClient
      .from('trainer_details')
      .select(
        `
        profile_id,
        invite_code,
        specialization,
        certification_level,
        experience_years,
        bio,
        profiles!trainer_details_profile_id_fkey(
          full_name,
          email
        )
      `
      )
      .eq('invite_code', trainerCode.toUpperCase())
      .eq('status', 'active')
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Trainer bulunamadı
      }
      throw new Error('Trainer arama sırasında hata oluştu: ' + error.message);
    }

    return {
      profile_id: trainerData.profile_id,
      invite_code: trainerData.invite_code,
      specialization: trainerData.specialization,
      certification_level: trainerData.certification_level,
      experience_years: trainerData.experience_years,
      bio: trainerData.bio,
      profiles: Array.isArray(trainerData.profiles)
        ? trainerData.profiles[0]
        : trainerData.profiles,
    };
  });
}

/**
 * Üye koduna göre üye arar
 */
export async function findMemberByCode(
  memberCode: string
): Promise<ApiResponse<MemberSearchResult | null>> {
  return await createAction(async () => {
    const adminClient = getSupabaseAdmin();

    const { data: memberData, error } = await adminClient
      .from('member_details')
      .select(
        `
        profile_id,
        invite_code,
        age,
        gender,
        height_cm,
        weight_kg,
        fitness_goal,
        profiles!member_details_profile_id_fkey(
          full_name,
          email
        )
      `
      )
      .eq('invite_code', memberCode.toUpperCase())
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Üye bulunamadı
      }
      throw new Error('Üye arama sırasında hata oluştu: ' + error.message);
    }

    return {
      profile_id: memberData.profile_id,
      invite_code: memberData.invite_code,
      age: memberData.age,
      gender: memberData.gender,
      height_cm: memberData.height_cm,
      weight_kg: memberData.weight_kg,
      fitness_goal: memberData.fitness_goal,
      profiles: Array.isArray(memberData.profiles)
        ? memberData.profiles[0]
        : memberData.profiles,
    };
  });
}