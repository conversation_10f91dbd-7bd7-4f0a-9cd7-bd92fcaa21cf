'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import {
  Users,
  Crown,
  Dumbbell,
  Plus,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react';
import Link from 'next/link';
import { useRoleConflictValidation } from '@/hooks/useRoleConflictValidation';

interface UserRole {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  badge: string;
  badgeVariant: 'default' | 'secondary' | 'destructive' | 'outline';
  isActive: boolean;
  features?: string[];
  color?: string;
}

const roleDefinitions = [
  {
    id: 'member',
    title: 'Üye',
    description: '<PERSON>lara katıl, antrenmanlarını takip et ve hedeflerine ulaş.',
    icon: <Users className="h-5 w-5" />,
    badge: '<PERSON><PERSON><PERSON><PERSON>',
    badgeVariant: 'secondary' as const,
    features: ['Salon üyeliği', '<PERSON><PERSON><PERSON><PERSON> takibi', '<PERSON><PERSON><PERSON><PERSON> raporları'],
    color: 'bg-blue-500',
  },
  {
    id: 'trainer',
    title: 'Antrenör',
    description: 'Müşterilerine rehberlik et, antrenman programları oluştur.',
    icon: <Dumbbell className="h-5 w-5" />,
    badge: 'Popüler',
    badgeVariant: 'default' as const,
    features: ['Müşteri yönetimi', 'Program oluşturma', 'Gelir takibi'],
    color: 'bg-green-500',
  },
  {
    id: 'company_manager',
    title: 'Şirket Sahibi',
    description: 'Salon şirketi işletmeciliği ve çoklu şube yönetimi.',
    icon: <Crown className="h-5 w-5" />,
    badge: 'Premium',
    badgeVariant: 'destructive' as const,
    features: ['Şirket yönetimi', 'Çoklu şube kontrolü', 'Finansal raporlar'],
    color: 'bg-purple-500',
  },
];

interface RoleManagementProps {
  userRoles: string[];
}

export function RoleManagement({ userRoles }: RoleManagementProps) {
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Rol çakışma kontrolü hook'u
  const {
    checkConflicts,
    isRoleSelectable,
    getRoleWarningMessage,
    conflictData,
  } = useRoleConflictValidation();

  useEffect(() => {
    const processUserRoles = () => {
      try {
        const isMember = userRoles.includes('member');
        const isTrainer = userRoles.includes('trainer');
        const isManager = userRoles.includes('company_manager');

        const processedRoles = roleDefinitions.map(role => ({
          ...role,
          isActive:
            (role.id === 'member' && isMember) ||
            (role.id === 'trainer' && isTrainer) ||
            (role.id === 'company_manager' && isManager),
        }));

        setRoles(processedRoles);
      } catch (error) {
        console.error('Rol kontrolü hatası:', error);
      } finally {
        setIsLoading(false);
      }
    };

    processUserRoles();
  }, [userRoles]);

  // Sayfa yüklendiğinde rol çakışma kontrolü yap
  useEffect(() => {
    checkConflicts();
  }, [checkConflicts]);

  if (isLoading) {
    return <div>Roller yükleniyor...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Rol çakışma uyarısı */}
      {conflictData?.hasConflict && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/30">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <span className="font-medium text-red-700 dark:text-red-300">
              Rol Çakışması Tespit Edildi
            </span>
          </div>
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">
            {conflictData.conflictMessage}
          </p>
        </div>
      )}

      {/* Rol Kartları */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {roles.map(role => {
          const canSelectRole = isRoleSelectable(role.id);
          const warningMessage = getRoleWarningMessage(role.id);

          return (
            <div key={role.id} className="space-y-2">
              <Card
                className={`relative transition-all duration-200 ${
                  role.isActive
                    ? 'ring-2 ring-primary shadow-lg'
                    : 'hover:shadow-md'
                } ${!canSelectRole && !role.isActive ? 'opacity-60' : ''}`}
              >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`rounded-full p-2 ${role.color} text-white`}>
                    {role.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{role.title}</CardTitle>
                    <Badge variant={role.badgeVariant} className="mt-1">
                      {role.badge}
                    </Badge>
                  </div>
                </div>
                {role.isActive && (
                  <CheckCircle className="text-primary h-6 w-6" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground text-sm">{role.description}</p>
              
              {/* Özellikler */}
              {role.features && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Özellikler:</h4>
                  <ul className="space-y-1">
                    {role.features.map((feature, index) => (
                      <li key={index} className="text-muted-foreground flex items-center gap-2 text-xs">
                        <div className="bg-primary/20 h-1.5 w-1.5 rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Rol Ekleme Butonu */}
              {!role.isActive && canSelectRole && (
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link href="/onboarding">
                    <Plus className="mr-2 h-4 w-4" />
                    {role.id === 'company_manager' ? 'Şirket Oluştur' : 'Rol Ekle'}
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Rol uyarı mesajı */}
          {warningMessage && (
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 text-center dark:border-amber-800 dark:bg-amber-950/30">
              <span className="text-sm text-amber-700 dark:text-amber-300">
                ⚠️ {warningMessage}
              </span>
            </div>
          )}
        </div>
        );
        })}
      </div>
    </div>
  );
}
