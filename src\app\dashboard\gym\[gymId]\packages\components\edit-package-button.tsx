'use client';

import { But<PERSON> } from '@/components/ui/button';
import { GymPackages } from '@/types/database/tables';
import { Edit } from 'lucide-react';
import { GymPackageForm } from '../../../../company/components/gym-package-form';

interface EditPackageButtonProps {
  gymId: string;
  package: GymPackages;
  variant?:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
    | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

export function EditPackageButton({
  gymId,
  package: pkg,
  variant = 'outline',
  size = 'default',
  className = 'w-full',
  children,
}: EditPackageButtonProps) {
  return (
    <GymPackageForm
      gymId={gymId}
      editPackage={pkg}
      trigger={
        <Button variant={variant} size={size} className={className}>
          <Edit className="mr-2 h-4 w-4" />
          {children || 'Dü<PERSON>le'}
        </Button>
      }
    />
  );
}
