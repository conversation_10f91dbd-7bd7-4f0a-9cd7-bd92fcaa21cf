'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, <PERSON>, Dumb<PERSON>, Crown } from 'lucide-react';

interface WelcomeStepProps {
  onContinue: () => void;
}

export function WelcomeStep({ onContinue }: WelcomeStepProps) {
  return (
    <div className="bg-card relative overflow-hidden rounded-xl border p-6 sm:p-10">
      <div className="pointer-events-none absolute inset-0 opacity-10">
        <Sparkles className="absolute -top-4 -right-4 h-20 w-20" />
      </div>

      <div className="grid gap-8 sm:grid-cols-2">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold">Sportiva ile Daha Fazlasını Yapın</h2>
          <p className="text-muted-foreground">
            Rolünüzü seçin ve deneyiminizi kişiselleştirelim. Üyelik, antrenörlük
            ve şirket yönetimi seçenekleriyle esnek bir yapı sunuyoruz.
          </p>

          <ul className="mt-4 space-y-3 text-sm">
            <li className="flex items-center gap-2">
              <Users className="text-primary h-4 w-4" /> Üye olarak salonlara
              katılın ve hedeflerinize ulaşın
            </li>
            <li className="flex items-center gap-2">
              <Dumbbell className="text-primary h-4 w-4" /> Antrenör olarak
              müşterilerinizle çalışın
            </li>
            <li className="flex items-center gap-2">
              <Crown className="text-primary h-4 w-4" /> Şirket kurarak şubelerinizi
              yönetin
            </li>
          </ul>

          <Button onClick={onContinue} size="lg" className="mt-6">
            Başlayalım
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        <div className="rounded-lg border bg-background p-4">
          <div className="grid grid-cols-3 gap-3">
            <div className="bg-muted h-16 rounded" />
            <div className="bg-muted h-16 rounded" />
            <div className="bg-muted h-16 rounded" />
            <div className="bg-muted h-16 rounded" />
            <div className="bg-muted h-16 rounded" />
            <div className="bg-muted h-16 rounded" />
          </div>
          <p className="text-muted-foreground mt-3 text-xs">
            Hızlı bir tur: Adım adım kurulum ile dakikalar içinde hazırsınız.
          </p>
        </div>
      </div>
    </div>
  );
}


