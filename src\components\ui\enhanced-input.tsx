'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Check, AlertCircle, Info } from 'lucide-react';
import { ValidationState, getValidationState } from '@/lib/utils/form-validation';

export interface EnhancedInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  validator?: (value: string) => boolean;
  formatter?: (value: string) => string;
  validationMessage?: string;
  showValidationIcon?: boolean;
  required?: boolean;
  maxLength?: number;
  characterCount?: boolean;
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  (
    {
      className,
      type = 'text',
      value,
      onChange,
      validator,
      formatter,
      validationMessage,
      showValidationIcon = true,
      required = false,
      maxLength,
      characterCount = false,
      ...props
    },
    ref
  ) => {
    const [validationState, setValidationState] = React.useState<ValidationState>('idle');
    const [displayValue, setDisplayValue] = React.useState(value);

    // Update validation state when value changes
    React.useEffect(() => {
      if (validator) {
        const state = getValidationState(value, validator, required);
        setValidationState(state);
      }
    }, [value, validator, required]);

    // Handle input change with formatting
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newValue = e.target.value;

      // Apply formatter if provided
      if (formatter) {
        newValue = formatter(newValue);
      }

      // Apply maxLength if provided
      if (maxLength && newValue.length > maxLength) {
        newValue = newValue.slice(0, maxLength);
      }

      setDisplayValue(newValue);
      onChange(newValue);
    };

    // Sync display value with prop value
    React.useEffect(() => {
      setDisplayValue(value);
    }, [value]);

    const getValidationIcon = () => {
      if (!showValidationIcon || validationState === 'idle') return null;

      switch (validationState) {
        case 'valid':
          return <Check className="h-4 w-4 text-green-500" />;
        case 'invalid':
          return <AlertCircle className="h-4 w-4 text-red-500" />;
        case 'warning':
          return <Info className="h-4 w-4 text-yellow-500" />;
        default:
          return null;
      }
    };

    const getInputClassName = () => {
      const baseClass = 'pr-10';
      
      switch (validationState) {
        case 'valid':
          return cn(baseClass, 'border-green-500 focus:border-green-500 focus:ring-green-500');
        case 'invalid':
          return cn(baseClass, 'border-red-500 focus:border-red-500 focus:ring-red-500');
        case 'warning':
          return cn(baseClass, 'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500');
        default:
          return baseClass;
      }
    };

    return (
      <div className="relative">
        <Input
          type={type}
          className={cn(getInputClassName(), className)}
          ref={ref}
          value={displayValue}
          onChange={handleChange}
          {...props}
        />
        
        {/* Validation Icon */}
        {showValidationIcon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {getValidationIcon()}
          </div>
        )}

        {/* Character Count */}
        {characterCount && maxLength && (
          <div className="mt-1 text-xs text-gray-500 text-right">
            {displayValue.length}/{maxLength}
          </div>
        )}

        {/* Validation Message */}
        {validationMessage && validationState === 'invalid' && (
          <div className="mt-1 text-xs text-red-600 dark:text-red-400">
            {validationMessage}
          </div>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = 'EnhancedInput';

export { EnhancedInput };
