// Centralized types for Onboarding flow

import { PlatformRoles } from '@/types/database/enums';

// Personal info (used when user has no full_name yet)
export interface PersonalData {
  first_name: string;
  last_name: string;
}

// Member specific form data
export interface MemberData {
  age: string; // keep as string for controlled inputs; parse on submit/validation
  gender: string;
  height_cm: string;
  weight_kg: string;
  fitness_goal: string;
}

// Trainer specific form data
export interface TrainerData {
  specialization: string;
  certification_level: string;
  experience_years: string; // keep as string for inputs; parse on submit
  bio: string;
}

// Manager specific form data
export interface ManagerData {
  companyName: string;
  companyPhone: string;
  companyEmail: string;
  logoUrl?: string;
  socialLinks?: Record<string, string>;
}

// Common error map used by state hook
export interface OnboardingFieldErrors {
  personal?: string | null;
  member?: string | null;
  trainer?: string | null;
  manager?: string | null;
}

// Step machine for onboarding
// Expanded to align with new multi-step onboarding UX
export type OnboardingStep = 'welcome' | 'roles' | 'forms' | 'review' | 'payment';

// Props used across small UI components
export interface StepHeaderProps {
  stepIndex: number; // 1-based index
  totalSteps: number;
  title: string;
  description?: string;
  progress: number; // 0-100
}

export interface ErrorAlertProps {
  message: string;
}

// Roles used in UI cards
export interface RoleMeta {
  id: PlatformRoles;
  title: string;
  description: string;
  features: string[];
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}
