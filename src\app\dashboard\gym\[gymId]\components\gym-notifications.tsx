import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getGymNotifications, getGymUnreadNotificationCount, markGymNotificationAsRead } from '@/lib/actions/notifications/notification-actions';
import { formatRelativeTime } from '@/lib/utils';
import { Check, Bell } from 'lucide-react';
import { revalidatePath } from 'next/cache';

export async function GymNotifications({ gymId }: { gymId: string }) {
  const [listRes, unreadRes] = await Promise.all([
    getGymNotifications(gymId, 10),
    getGymUnreadNotificationCount(gymId),
  ]);

  const notifications = listRes.data || [];
  const unreadCount = unreadRes.data || 0;

  async function markReadAction(formData: FormData) {
    'use server';
    const id = formData.get('id') as string;
    if (!id) return;
    await markGymNotificationAsRead(id, gymId);
    revalidatePath(`/dashboard/gym/${gymId}`);
  }

  return (
    <Card>
      <CardHeader className="flex items-start justify-between gap-3">
        <div>
          <CardTitle className="">
            <Bell className="h-4 w-4" />
            Salon Bildirimleri
          </CardTitle>
        </div>
        {unreadCount > 0 && (
          <Badge variant="secondary" className="self-start">{unreadCount} yeni</Badge>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        {notifications.length === 0 ? (
          <div className="text-muted-foreground text-sm">Henüz bildirim yok.</div>
        ) : (
          <ul className="space-y-3">
            {notifications.map((n) => (
              <li
                key={n.id}
                className={`rounded-md border p-2 sm:p-3 ${
                  !n.is_read ? 'bg-blue-50/40 dark:bg-blue-950/20 border-blue-200/50' : 'bg-background'
                }`}
              >
                <div className="flex items-start justify-between gap-2 sm:gap-3">
                  <div className="min-w-0 flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      <p className="truncate text-xs sm:text-sm font-semibold">{n.title}</p>
                      {!n.is_read && (
                        <span className="bg-primary h-1.5 w-1.5 sm:h-2 sm:w-2 rounded-full shrink-0" />
                      )}
                    </div>
                    {n.message && (
                      <p className="text-muted-foreground text-xs line-clamp-2">{n.message}</p>
                    )}
                    {n.created_at && (
                      <p className="text-muted-foreground mt-1 text-[10px] sm:text-[11px]">{formatRelativeTime(n.created_at)}</p>
                    )}
                  </div>
                  {!n.is_read && (
                    <form action={markReadAction} className="shrink-0">
                      <input type="hidden" name="id" value={n.id} />
                      <Button variant="ghost" size="sm" className="h-6 sm:h-7 px-1.5 sm:px-2 text-[10px] sm:text-xs">
                        <Check className="mr-0.5 sm:mr-1 h-2.5 w-2.5 sm:h-3 sm:w-3" />
                        <span className="hidden sm:inline">Okundu</span>
                        <span className="sm:hidden">✓</span>
                      </Button>
                    </form>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
