/**
 * Auth-related constants
 */

import { getAppUrl } from '@/lib/env';

// Error messages
export const AUTH_ERRORS = {
  USER_CREATION_FAILED: 'Kullanıcı oluşturulamadı.',
  SIGNUP_FAILED: 'E-posta ile kayıt işlemi başarısız.',
  SIGNIN_FAILED: 'E-posta ile giriş yapılamadı.',
  SIGNOUT_FAILED: 'Kullanıcı çıkış yapılamadı.',
} as const;

// Success messages
export const AUTH_MESSAGES = {
  VERIFICATION_EMAIL_SENT: 'Doğrulama e-postası gönderildi.',
  SIGNIN_SUCCESS: 'E-posta ile giriş başarılı.',
  SIGNOUT_SUCCESS: 'Kullanıcı çıkış yapıldı.',
} as const;

// Login page specific messages
export const LOGIN_ERRORS = {
  EMAIL_LOGIN_FAILED: 'E-posta ile giriş yapılamadı',
  PHONE_LOGIN_FAILED: 'Telefon ile giriş yapılamadı',
  UNEXPECTED_ERROR: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.',
} as const;

// Register page specific messages
export const REGISTER_ERRORS = {
  EMAIL_REGISTER_FAILED: 'E-posta ile kayıt olurken bir hata oluştu',
  PHONE_REGISTER_FAILED: 'Telefon ile kayıt olurken bir hata oluştu',
  UNEXPECTED_ERROR: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.',
} as const;

// Password reset specific messages
export const PASSWORD_RESET_ERRORS = {
  EMAIL_NOT_FOUND: 'Bu e-posta adresi ile kayıtlı bir hesap bulunamadı',
  RESET_FAILED: 'Şifre sıfırlama linki gönderilemedi',
  INVALID_TOKEN: 'Geçersiz veya süresi dolmuş sıfırlama linki',
  PASSWORD_UPDATE_FAILED: 'Şifre güncellenirken bir hata oluştu',
  UNEXPECTED_ERROR: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.',
} as const;

// Password reset success messages
export const PASSWORD_RESET_MESSAGES = {
  RESET_LINK_SENT: 'Şifre sıfırlama linki e-posta adresinize gönderildi',
  PASSWORD_UPDATED: 'Şifreniz başarıyla güncellendi',
} as const;

// URLs and paths
export const AUTH_URLS = {
  get DEFAULT_SITE_URL() {
    return getAppUrl();
  },
  CALLBACK_PATH: '/auth/callback',
} as const;

// Revalidation paths
export const AUTH_REVALIDATION_PATHS = {
  DASHBOARD: ['/dashboard'],
  HOME_AND_AUTH: ['/', '/auth'],
};

// UI Text Constants
export const AUTH_UI_TEXT = {
  LOGIN_TITLE: 'Tekrar Hoş Geldiniz',
  LOGIN_SUBTITLE: 'E-posta veya telefon ile giriş yapın',
  NO_ACCOUNT: 'Hesabınız yok mu?',
  REGISTER_LINK: 'Kayıt olun',
  REGISTER_TITLE: "Sportiva'ya Hoş Geldin!",
  REGISTER_SUBTITLE: 'E-posta veya telefon ile kayıt ol',
  HAVE_ACCOUNT: 'Zaten hesabın var mı?',
  LOGIN_LINK: 'Giriş yap',
  FORGOT_PASSWORD: 'Şifremi Unuttum?',
  FORGOT_PASSWORD_TITLE: 'Şifrenizi Sıfırlayın',
  FORGOT_PASSWORD_SUBTITLE:
    'E-posta adresinizi girin, size sıfırlama linki gönderelim',
  RESET_PASSWORD_TITLE: 'Yeni Şifre Belirleyin',
  RESET_PASSWORD_SUBTITLE: 'Hesabınız için yeni bir şifre oluşturun',
  SEND_RESET_LINK: 'Sıfırlama Linki Gönder',
  RESET_PASSWORD_BUTTON: 'Şifreyi Sıfırla',
  BACK_TO_LOGIN: 'Giriş sayfasına dön',
  RESET_LINK_SENT: 'Sıfırlama linki e-posta adresinize gönderildi',
  PASSWORD_RESET_SUCCESS: 'Şifreniz başarıyla güncellendi',
} as const;
