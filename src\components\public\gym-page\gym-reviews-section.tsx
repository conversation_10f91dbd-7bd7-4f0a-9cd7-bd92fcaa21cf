import { memo, useMemo } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { StarRating } from "@/components/ui/star-rating";
import { MessageSquare, Quote, ThumbsUp } from "lucide-react";
import { ReviewWithUser } from "@/lib/actions/gymPage/gym-utils";

interface GymReviewsSectionProps {
  initialReviews: ReviewWithUser[];
}

export const GymReviewsSection = memo(function GymReviewsSection({
  initialReviews
}: GymReviewsSectionProps) {

  const averageRating = useMemo(() => {
    if (!initialReviews?.length) return 0;
    const sum = initialReviews.reduce((acc, review) => acc + (review.rating || 0), 0);
    return Number((sum / initialReviews.length).toFixed(1));
  }, [initialReviews]);

  return (
    <section className="py-24 bg-gradient-to-b from-background to-muted/20" aria-labelledby="reviews-heading">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6" aria-label="Üye yorumları etiketi">
            <MessageSquare className="h-4 w-4" aria-hidden="true" />
            <span>Üye Yorumları</span>
          </div>
          <h2 id="reviews-heading" className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Üyelerimiz <span className="text-primary">Ne Diyor</span>?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Gerçek üyelerimizin deneyimlerini okuyun ve salonumuz hakkında daha fazla bilgi edinin
          </p>

          {/* Stats */}
          {initialReviews.length > 0 && (
            <div className="flex items-center justify-center gap-8 mt-8" aria-label="Yorum istatistikleri">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">{averageRating}</div>
                <div className="flex items-center justify-center gap-1 mb-1">
                  <StarRating rating={averageRating} readonly size="sm" />
                </div>
                <div className="text-sm text-muted-foreground">Ortalama puan</div>
              </div>
              <div className="w-px h-12 bg-border" />
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">{initialReviews.length}</div>
                <div className="text-sm text-muted-foreground">Toplam değerlendirme</div>
              </div>
            </div>
          )}
        </div>

        {/* Reviews Content */}
        {initialReviews.length > 0 ? (
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2" role="list">
              {initialReviews.slice(0, 4).map((review, index) => (
                <Card
                  key={review.id}
                  role="listitem"
                  className="group relative p-8 bg-card/80 backdrop-blur-sm rounded-3xl border border-border/50 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:-translate-y-1"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  {/* Quote Icon */}
                  <div className="absolute top-6 right-6 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center opacity-50 group-hover:opacity-100 transition-opacity">
                    <Quote className="h-6 w-6 text-primary" />
                  </div>

                    <div className="space-y-6">
                      {/* Rating */}
                      <div className="flex items-center gap-2" aria-label={`Kullanıcı puanı: ${review.rating}/5`}>
                        <StarRating
                          rating={review.rating || 0}
                          readonly
                          size="sm"
                        />
                        <span className="text-sm font-medium text-muted-foreground">
                          {review.rating}/5
                        </span>
                      </div>

                    {/* Comment */}
                    {review.comment && (
                      <blockquote className="text-lg text-foreground leading-relaxed font-medium">
                        &quot;{review.comment}&quot;
                      </blockquote>
                    )}

                    {/* User Info */}
                    <div className="flex items-center gap-4 pt-4 border-t border-border/50">
                      <Avatar className="h-14 w-14 ring-2 ring-primary/20">
                        <AvatarImage
                          src="/placeholder.svg"
                          alt={review.user?.full_name || "Üye"}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg" aria-hidden="true">
                          {review.user?.full_name?.split(" ").map((name) => name.charAt(0)).join("").toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-semibold text-foreground text-lg">
                          {review.user?.full_name || "İsimsiz Üye"}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {review.created_at
                            ? new Date(review.created_at).toLocaleDateString("tr-TR", {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })
                            : "Tarih bilinmiyor"
                          }
                        </div>
                      </div>
                      <div className="w-8 h-8 bg-green-500/10 rounded-full flex items-center justify-center" aria-hidden="true">
                        <ThumbsUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {initialReviews.length > 4 && (
              <div className="text-center mt-12">
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 rounded-xl border-2 hover:border-primary/50 hover:bg-primary/5 transition-all duration-300"
                  aria-label={`Tüm değerlendirmeleri gör (${initialReviews.length})`}
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Tüm Değerlendirmeleri Gör ({initialReviews.length})
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-br from-muted/50 to-muted/30 rounded-3xl flex items-center justify-center mx-auto mb-8 border border-border/50" aria-hidden="true">
                <MessageSquare className="h-16 w-16 text-muted-foreground" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary/20 rounded-full border border-primary/30 flex items-center justify-center animate-pulse" aria-hidden="true">
                <Quote className="h-4 w-4 text-primary" />
              </div>
            </div>
            <h3 className="text-3xl font-bold text-foreground mb-4">
              İlk Yorumu Sen Yap
            </h3>
            <p className="text-muted-foreground text-lg max-w-md mx-auto mb-8">
              Bu salon için henüz değerlendirme bulunmuyor. İlk yorumu yapan sen ol!
            </p>
            <Button className="px-8 py-4 rounded-xl" aria-label="Yorum yap">
              <MessageSquare className="h-5 w-5 mr-2" />
              Yorum Yap
            </Button>
          </div>
        )}
      </div>
    </section>
  );
});
