import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { ResetPasswordForm } from '@/components/auth/reset-password-form';
import { confirmPasswordReset } from '@/lib/actions/auth/auth-actions';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';

export const metadata: Metadata = {
  title: 'Şifre Sıfırla | Sportiva',
  description: 'Sportiva yeni şifre belirleme sayfası.',
};

interface ResetPasswordPageProps {
  searchParams: {
    error?: string;
    message?: string;
    password_error?: string;
    confirm_password_error?: string;
  };
}

export default function ResetPasswordPage({
  searchParams,
}: ResetPasswordPageProps) {
  const { error, message, password_error, confirm_password_error } =
    searchParams;

  async function handlePasswordUpdate(formData: FormData) {
    'use server';

    try {
      const result = await confirmPasswordReset(formData);

      if (!result.success) {
        // Hata durumunda aynı sayfada kal ve hatayı göster
        redirect(
          `/reset-password?error=${encodeURIComponent(
            result.error || '<PERSON><PERSON>re güncellenirken bir hata oluştu'
          )}`
        );
      }

      // Başarılı durumda login sayfasına yönlendir
      redirect(
        `/auth/login?message=${encodeURIComponent(
          result.message ||
            'Şifreniz başarıyla güncellendi. Giriş yapabilirsiniz.'
        )}`
      );
    } catch (error) {
      console.error('Password update error:', error);
      redirect(
        `/reset-password?error=${encodeURIComponent(
          'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.'
        )}`
      );
    }
  }

  return (
    <div className="animate-in slide-in-from-bottom-4 w-full duration-700">
      {/* Header */}
      <div className="mb-8 text-center">
        <h1 className="text-foreground mb-2 text-2xl font-bold lg:text-3xl">
          {AUTH_UI_TEXT.RESET_PASSWORD_TITLE}
        </h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          {AUTH_UI_TEXT.RESET_PASSWORD_SUBTITLE}
        </p>
      </div>

      {/* Success Message */}
      {message && (
        <div
          className="animate-in slide-in-from-top-2 mb-6 flex w-full items-center space-x-3 rounded-xl border border-green-500/30 bg-green-50/50 p-4 text-green-700 duration-500 dark:bg-green-950/20 dark:text-green-400"
          role="alert"
          aria-live="polite"
        >
          <span className="text-sm font-medium">{message}</span>
        </div>
      )}

      {/* Form Card */}
      <div className="bg-card/80 border-border/50 shadow-primary/5 hover:shadow-primary/10 rounded-2xl border p-6 shadow-2xl backdrop-blur-sm transition-all duration-500 hover:scale-[1.01] lg:p-8">
        <ResetPasswordForm
          onSubmit={handlePasswordUpdate}
          error={error}
          passwordError={password_error}
          confirmPasswordError={confirm_password_error}
        />
      </div>

      {/* Footer */}
      <div className="mt-6 flex flex-col items-center gap-3 sm:flex-row sm:justify-center sm:gap-2">
        <span className="text-sm sm:text-base">Şifrenizi hatırladınız mı?</span>
        <Link
          href="/auth/login"
          className="text-primary hover:text-primary/80 hover:bg-primary/10 focus:ring-primary/50 inline-flex min-h-[44px] items-center justify-center rounded-lg px-4 py-2 font-semibold transition-all duration-200 hover:underline focus:outline-none focus:ring-2 active:scale-95"
        >
          {AUTH_UI_TEXT.BACK_TO_LOGIN}
        </Link>
      </div>
    </div>
  );
}
