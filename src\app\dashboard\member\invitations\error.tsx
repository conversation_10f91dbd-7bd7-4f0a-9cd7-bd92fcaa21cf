'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { logger } from '@/lib/logger';
import { AlertCircle, RefreshCw } from 'lucide-react';

export default function InvitationsError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    logger.error('Invitations page error', error, {
      digest: error.digest,
      component: 'InvitationsPage'
    });
  }, [error]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Salon Davetleri</h1>
        <p className="text-muted-foreground">
          Aldığınız ve gönderdiğiniz salon davetlerini görüntüleyin ve yönetin
        </p>
      </div>

      {/* Error Card */}
      <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                Davetler Yüklenemedi
              </h3>
              <p className="mt-1 text-red-700 dark:text-red-300">
                Salon davetleriniz yüklenirken bir hata oluştu. Lütfen tekrar
                deneyin.
              </p>
              {error.message && (
                <p className="mt-2 font-mono text-sm text-red-600 dark:text-red-400">
                  Hata: {error.message}
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 flex gap-3">
            <Button
              onClick={reset}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Tekrar Dene
            </Button>
            <Button
              onClick={() => (window.location.href = '/dashboard/member')}
              variant="secondary"
            >
              Ana Sayfaya Dön
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
