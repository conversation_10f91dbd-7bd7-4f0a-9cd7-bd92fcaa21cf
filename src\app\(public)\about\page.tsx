import { AboutCTA } from '@/components/public/about/about-cta';
import { AboutHero } from '@/components/public/about/about-hero';
import { AboutMission } from '@/components/public/about/about-mission';
import { AboutStats } from '@/components/public/about/about-stats';
import { AboutStory } from '@/components/public/about/about-story';
import { AboutTeam } from '@/components/public/about/about-team';
import { AboutValues } from '@/components/public/about/about-values';
import { StructuredData } from '@/components/seo/structured-data';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Hakkımızda | Sportiva',
  description:
    "Sportiva'nın hikayesi, misyonu, vizyonu ve ekibi. Spor salonu yönetiminde dijital dönüşümün öncüsü olarak nasıl fark yaratıyoruz öğrenin.",
  keywords: [
    'sportiva hakkında',
    'spor salonu teknolojisi',
    'dijital dönüşüm',
    'startup hikayesi',
    'spor teknolojileri',
    'gym management',
    'fitness technology',
  ],
  openGraph: {
    title: 'Hakkımızda | Sportiva',
    description:
      "Sportiva'nın hikayesi, misyonu, vizyonu ve ekibi. Spor salonu yönetiminde dijital dönüşümün öncüsü olarak nasıl fark yaratıyoruz öğrenin.",
    type: 'website',
    url: 'https://sportiva.com.tr/about',
    images: [
      {
        url: '/og-about.jpg',
        width: 1200,
        height: 630,
        alt: 'Sportiva Hakkımızda',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Hakkımızda | Sportiva',
    description:
      "Sportiva'nın hikayesi, misyonu, vizyonu ve ekibi. Spor salonu yönetiminde dijital dönüşümün öncüsü olarak nasıl fark yaratıyoruz öğrenin.",
    images: ['/og-about.jpg'],
  },
  alternates: {
    canonical: '/about',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function AboutPage() {
  return (
    <>
      <StructuredData type="AboutPage" />
      <main className="flex-1">
        <AboutHero />
        <AboutMission />
        <AboutStats />
        <AboutStory />
        <AboutValues />
        <AboutTeam />
        <AboutCTA />
      </main>
    </>
  );
}
