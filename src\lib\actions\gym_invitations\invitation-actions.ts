'use server';

import { logger } from '@/lib/logger';
import { ApiResponse } from '@/types/global/api';
import {
  InvitationType,
  InvitationRole,
  InvitationCreateParams,
  InvitationQueryParams,
  INVITATION_CONFIGS,
} from './invitation-types';
import { createAdminAction } from '../core';

import { CORE_ERROR_MESSAGES } from '../core/core-constants';
import { GymInvitations } from '@/types';
import { createNotification } from '../notifications/notification-actions';
import { createNotificationMessage } from '../notifications/notification-utils';
import { createLimitReachedError } from '@/lib/utils/error-types';

// Hata mesajları
const ERRORS = {
  VALIDATION_FAILED: CORE_ERROR_MESSAGES.VALIDATION_FAILED ?? 'Geçersiz veri',
  ACCESS_DENIED: '<PERSON><PERSON><PERSON><PERSON> reddedildi',
  FETCH_FAILED: 'Veri getirilemedi',
  CREATE_FAILED: 'Oluşturma başarısız',
  UPDATE_FAILED: 'Güncelleme başarısız',
  DELETE_FAILED: 'Silme başarısız',
  TARGET_NOT_FOUND: 'Hedef bulunamadı',
  INVALID_OPERATION: 'Geçersiz işlem',
  ALREADY_EXISTS: 'Zaten mevcut',
};


/**
 * Davet oluşturma - Yeni minimal yapı
 */
export async function createInvitation(
  params: InvitationCreateParams
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, _authUserId, adminClient) => {
      const { gymId, profileId, type, role, message, expirationDays } = params;
      // Konfigürasyon al
      const configKey = `${type}_${role}`;
      const config = INVITATION_CONFIGS[configKey];
      if (!config) {
        throw new Error(`${ERRORS.INVALID_OPERATION}: Geçersiz davet türü`);
      }

      const { data: profile, error: profileError } = await adminClient
        .from('profiles')
        .select('id, full_name, email')
        .eq('id', profileId)
        .single();

      if (profileError || !profile) {
        throw new Error(ERRORS.TARGET_NOT_FOUND);
      }

      // Mevcut davet kontrolü
      await checkExistingInvitation(adminClient, gymId, profileId, type, role);

      // Mevcut üyelik kontrolü (role'e göre)
      if (role === 'member') {
        await checkExistingMembership(adminClient, profileId, gymId);
      } else if (role === 'trainer') {
        await checkExistingTrainerStatus(adminClient, profileId, gymId);
      }

      // Kapasite kontrolü: Üye/Antrenör limitlerini aşma
      await enforceGymCapacityLimits(adminClient, gymId, role);

      // Mesaj ve süre ayarları
      const finalMessage = message || config.defaultMessage || '';
      const expiration = expirationDays || config.expirationDays || 7;
      const expiresAt = new Date(
        Date.now() + expiration * 24 * 60 * 60 * 1000
      ).toISOString();

      // Davet oluştur
      const { data: invitation, error: createError } = await adminClient
        .from('gym_invitations')
        .insert({
          gym_id: gymId,
          profile_id: profileId,
          type,
          role,
          status: 'pending',
          message: finalMessage,
          expires_at: expiresAt,
        })
        .select(`
          *,
          gym:gyms(id, name, address, gym_phone, company:companies(manager_profile_id)),
          profile:profiles(id, full_name, email)
        `)
        .single();

      if (createError) {
        logger.error('Davet oluşturulurken hata', createError, {
          gymId,
          profileId,
          type,
          role,
        });
        throw new Error(`${ERRORS.CREATE_FAILED}: Davet gönderilirken hata oluştu.`);
      }

      // Bildirim gönder
      await sendInvitationNotificationNew(invitation);

      return invitation;
    },
    {
      revalidatePaths: [
        '/dashboard/member/invitations',
        '/dashboard/trainer/invitations',
        `/dashboard/gym/${params.gymId}/invitations`,
      ],
    }
  );
}

/**
 * Davet kabul etme
 */
export async function acceptInvitation(
  invitationId: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, _authUserId, adminClient) => {
      // Daveti al
      const { data: invitation, error: fetchError } = await adminClient
        .from('gym_invitations')
        .select(`
          *,
          gym:gyms(id, name, company:companies(manager_profile_id)),
          profile:profiles(id, full_name, email)
        `)
        .eq('id', invitationId)
        .eq('status', 'pending')
        .single();

      if (fetchError || !invitation) {
        throw new Error(ERRORS.TARGET_NOT_FOUND);
      }

      // Kapasite kontrolü: kabulden önce limiti doğrula
      await enforceGymCapacityLimits(adminClient, invitation.gym_id, invitation.role as InvitationRole);

      const { error: updateError } = await adminClient
        .from('gym_invitations')
        .update({
          status: 'accepted',
          responded_at: new Date().toISOString(),
        })
        .eq('id', invitationId);

      if (updateError) {
        logger.error('Davet kabul edilirken hata', updateError, { invitationId });
        throw new Error(`${ERRORS.UPDATE_FAILED}: Davet kabul edilemedi.`);
      }

      // Üyelik/Antrenörlük kaydı oluştur
      if (invitation.role === 'member') {
        await createMembershipRecord(adminClient, invitation.profile_id, invitation.gym_id);
      } else if (invitation.role === 'trainer') {
        await createTrainerRecord(adminClient, invitation.profile_id, invitation.gym_id);
      }

      // Bildirim gönder: kabul
      try {
        if (invitation.type === 'gym_invite') {
          // Üye/Antrenör daveti kabul etti -> Salon (gym entity) bildirilir
          const { title, message } = createNotificationMessage('accept', 'to_gym', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

          // Salona bildirim gönder
          await createNotification({
            gymId: invitation.gym_id,
            title,
            message,
          });
        } else if (invitation.type === 'join_request') {
          // İstek kabul: üye/antrenöre bildir
          const { title, message } = createNotificationMessage('accept', 'to_user', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

          // Kullanıcıya bildirim gönder
          await createNotification({
            recipientUserId: invitation.profile_id,
            title,
            message,
          });
        }
      } catch (e) {
        logger.error('Kabul bildirimi gönderilirken hata', e, { invitationId });
      }

      return { success: true, message: 'Davet başarıyla kabul edildi' };
    },
    {
      revalidatePaths: [
        '/dashboard/member',
        '/dashboard/trainer',
        '/dashboard/company',
      ],
    }
  );
}

/**
 * Davet reddetme
 */
export async function rejectInvitation(
  invitationId: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, authUserId, adminClient) => {
      // Daveti al
      const { data: invitation, error: fetchError } = await adminClient
        .from('gym_invitations')
        .select(`
          *,
          gym:gyms(company:companies(manager_profile_id))
        `)
        .eq('id', invitationId)
        .eq('status', 'pending')
        .single();

      if (fetchError || !invitation) {
        throw new Error(ERRORS.TARGET_NOT_FOUND);
      }

      // Yetki kontrolü
      const canReject = 
        (invitation.type === 'gym_invite' && invitation.profile_id === authUserId) ||
        (invitation.type === 'join_request' && invitation.gym.company.manager_profile_id === authUserId);

      if (!canReject) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      // Daveti reddet
      const { error: updateError } = await adminClient
        .from('gym_invitations')
        .update({
          status: 'rejected',
          responded_at: new Date().toISOString(),
        })
        .eq('id', invitationId);

      if (updateError) {
        logger.error('Davet reddedilirken hata', updateError, { invitationId });
        throw new Error(`${ERRORS.UPDATE_FAILED}: Davet reddedilemedi.`);
      }

      // Bildirim gönder: reddetme
      try {
        if (invitation.type === 'gym_invite') {
          // Kullanıcı salon davetini reddetti -> Salon (gym entity) bildirilir
          const { title, message } = createNotificationMessage('reject', 'to_gym', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

          // Salona bildirim gönder
          
          await createNotification({
            gymId: invitation.gym_id,
            title,
            message,
          });
        } else if (invitation.type === 'join_request') {
          // Yöneticinin reddi -> kullanıcıya bildir
          const { title, message } = createNotificationMessage('reject', 'to_user', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

          // Kullanıcıya bildirim gönder
          const { createNotification } = await import('../notifications/notification-actions');
          await createNotification({
            recipientUserId: invitation.profile_id,
            title,
            message,
            
          });
        }
      } catch (e) {
        logger.error('Reddetme bildirimi gönderilirken hata', e, { invitationId });
      }

      return { success: true, message: 'Davet reddedildi' };
    },
    {
      revalidatePaths: [
        '/dashboard/member/invitations',
        '/dashboard/trainer/invitations',
      ],
    }
  );
}

/**
 * Davet iptal etme
 */
export async function cancelInvitation(
  invitationId: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, authUserId, adminClient) => {
      // Daveti al
      const { data: invitation, error: fetchError } = await adminClient
        .from('gym_invitations')
        .select(`
          *,
          gym:gyms(id, name, company:companies(manager_profile_id)),
          profile:profiles(id, full_name, email)
        `)
        .eq('id', invitationId)
        .eq('status', 'pending')
        .single();

      if (fetchError || !invitation) {
        throw new Error(ERRORS.TARGET_NOT_FOUND);
      }

      // Yetki kontrolü
      const isManager = invitation?.gym?.company?.manager_profile_id === authUserId;
      const isOwner = invitation.profile_id === authUserId;

      const canCancel =
        (invitation.type === 'gym_invite' && isManager) ||
        (invitation.type === 'join_request' && isOwner);

      if (!canCancel) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      // Daveti sil
      const { data: deleted, error: deleteError } = await adminClient
        .from('gym_invitations')
        .delete()
        .eq('id', invitationId)
        .eq('status', 'pending')
        .select()
        .single();

      if (deleteError || !deleted) {
        logger.error('Davet iptal edilirken hata', deleteError, { invitationId });
        throw new Error(`${ERRORS.DELETE_FAILED}: Davet iptal edilemedi.`);
      }

      // Bildirim gönder: iptal
      try {
        if (invitation.type === 'gym_invite' && isManager) {
          // Yönetici daveti iptal etti: üyeye/antrenöre kullanıcı bildirimi
          const { title, message } = createNotificationMessage('cancel', 'to_user', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

     
          await createNotification({
            recipientUserId: invitation.profile_id,
            title,
            message,
            
          });
        } else if (invitation.type === 'join_request' && isOwner) {
          // Üye isteği iptal etti: SALONA (gym entity) bildirim
          const { title, message } = createNotificationMessage('cancel', 'to_gym', {
            userName: invitation.profile?.full_name || 'Kullanıcı',
            gymName: invitation.gym?.name || 'Salon',
            userRole: invitation.role as 'member' | 'trainer',
          });

          // Salona bildirim gönder
          
          await createNotification({
            gymId: invitation.gym_id,
            title,
            message,
          });
        } else {
          logger.warn('İptal bildirimi için alıcı belirlenemedi', { invitationId });
        }
      } catch (e) {
        logger.error('İptal bildirimi gönderilirken hata', e, { invitationId });
      }

      return { success: true, message: 'Davet iptal edildi' };
    },
    {
      revalidatePaths: [
        '/dashboard/member/invitations',
        '/dashboard/trainer/invitations',
        '/dashboard/company',
      ],
    }
  );
}

/**
 * Davet sorgulama - Yeni yapı
 * Erişim mantığı düzeltildi: gymId verildiğinde yöneticilik kontrolü yapılır ve
 * or(...) filtresi uygulanmaz; profileId verildiğinde sadece kendi davetleri gösterilir.
 */
export async function queryInvitations(
  params: InvitationQueryParams
): Promise<ApiResponse<any[]>> {
  return await createAdminAction(
    async (_, __, authUserId, adminClient) => {
      // Erişim kontrolü
      if (params.profileId && params.profileId !== authUserId) {
        throw new Error(ERRORS.ACCESS_DENIED);
      }

      if (params.gymId) {
        const { data: gym, error: gymError } = await adminClient
          .from('gyms')
          .select('id, manager_profile_id, ...companies(company_manager_id:manager_profile_id)')
          .eq('id', params.gymId)
          .single();

        if (gymError || !gym) {
          throw new Error(ERRORS.ACCESS_DENIED);
        }

        const isCompanyManager = gym.company_manager_id === authUserId;
        const isGymManager = gym.manager_profile_id === authUserId;

        let isTrainer = false;
        if (!isCompanyManager && !isGymManager) {
          const { count } = await adminClient
            .from('gym_trainers')
            .select('*', { count: 'exact', head: true })
            .eq('gym_id', params.gymId)
            .eq('trainer_profile_id', authUserId);
          isTrainer = (count ?? 0) > 0;
        }

        if (!isCompanyManager && !isGymManager && !isTrainer) {
          throw new Error(ERRORS.ACCESS_DENIED);
        }
      }

      let query = adminClient.from('gym_invitations').select(`
        *,
        gym:gyms(id, name, address, gym_phone),
        profile:profiles(id, full_name, email)
      `);

      // Filtreler
      if (params.gymId) {
        query = query.eq('gym_id', params.gymId);
      }

      if (params.profileId) {
        query = query.eq('profile_id', params.profileId);
      }

      if (params.type) {
        query = query.eq('type', params.type);
      }

      if (params.role) {
        query = query.eq('role', params.role);
      }

      if (params.status) {
        query = query.eq('status', params.status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        logger.error('Davetler sorgulanırken hata', error, { ...params });
        throw new Error(`${ERRORS.FETCH_FAILED}: Davetler yüklenemedi.`);
      }

      return data || [];
    }
  );
}

async function checkExistingInvitation(
  adminClient: any,
  gymId: string,
  profileId: string,
  type: InvitationType,
  role: InvitationRole
): Promise<void> {
  const { data: existing } = await adminClient
    .from('gym_invitations')
    .select('id')
    .eq('gym_id', gymId)
    .eq('profile_id', profileId)
    .eq('type', type)
    .eq('role', role)
    .eq('status', 'pending')
    .single();

  if (existing) {
    throw new Error(`${ERRORS.ALREADY_EXISTS}: Bekleyen davet zaten mevcut`);
  }
}

async function checkExistingMembership(
  adminClient: any,
  profileId: string,
  gymId: string
): Promise<void> {
  const { data: membership } = await adminClient
    .from('gym_memberships')
    .select('id')
    .eq('profile_id', profileId)
    .eq('gym_id', gymId)
    .single();

  if (membership) {
    throw new Error(`${ERRORS.ALREADY_EXISTS}: Kullanıcı zaten üye`);
  }
}

async function checkExistingTrainerStatus(
  adminClient: any,
  profileId: string,
  gymId: string
): Promise<void> {
  const { data: trainer } = await adminClient
    .from('gym_trainers')
    .select('id')
    .eq('trainer_profile_id', profileId)
    .eq('gym_id', gymId)
    .eq('role', 'trainer')
    .single();

  if (trainer) {
    throw new Error(`${ERRORS.ALREADY_EXISTS}: Kullanıcı zaten antrenör`);
  }
}

// Yardımcı: Gym kapasite limitlerini kontrol et
async function enforceGymCapacityLimits(
  adminClient: any,
  gymId: string,
  role: InvitationRole
): Promise<void> {
  const limits = await getGymLimits(adminClient, gymId);

  if (role === 'member') {
    if (limits.maxMembers != null) {
      const activeMembers = await countActiveMembers(adminClient, gymId);
      if (activeMembers >= limits.maxMembers) {
        throw createLimitReachedError('Üye limiti dolu', {
          role: 'member',
          gymId,
          current: activeMembers,
          max: limits.maxMembers,
        });
      }
    }
  } else if (role === 'trainer') {
    if (limits.maxTrainers != null) {
      const activeTrainers = await countActiveTrainers(adminClient, gymId);
      if (activeTrainers >= limits.maxTrainers) {
        throw createLimitReachedError('Antrenör limiti dolu', {
          role: 'trainer',
          gymId,
          current: activeTrainers,
          max: limits.maxTrainers,
        });
      }
    }
  }
}

// Yardımcı: Gym -> Company -> Platform Package üzerinden limitleri getir
interface PlatformPackageFeatures {
  max_trainers?: number;
  [key: string]: any;
}

interface PlatformPackage {
  max_members?: number | null;
  features?: PlatformPackageFeatures | null;
}

async function getGymLimits(
  adminClient: any,
  gymId: string
): Promise<{ maxMembers: number | null; maxTrainers: number | null }> {
  // 1) Gym'den company_id al
  const { data: gym, error: gymError } = await adminClient
    .from('gyms')
    .select('id, company_id')
    .eq('id', gymId)
    .single();

  if (gymError || !gym) {
    throw new Error(ERRORS.FETCH_FAILED);
  }

  // 2) Company'den platform_package_id al
  const { data: company, error: companyError } = await adminClient
    .from('companies')
    .select('id, platform_package_id')
    .eq('id', gym.company_id)
    .single();

  if (companyError || !company) {
    throw new Error(ERRORS.FETCH_FAILED);
  }

  if (!company.platform_package_id) {
    // Paket yoksa limitsiz kabul edilir
    return { maxMembers: null, maxTrainers: null };
  }

  // 3) Platform paketinden limitleri al
  const { data: pkg, error: pkgError } = await adminClient
    .from('platform_packages')
    .select('max_members, features')
    .eq('id', company.platform_package_id)
    .single();

  if (pkgError || !pkg) {
    throw new Error(ERRORS.FETCH_FAILED);
  }

  const typedPkg = pkg as PlatformPackage;
  const features = typedPkg.features || {};
  const maxTrainers: number | null =
    features && typeof features.max_trainers === 'number'
      ? features.max_trainers
      : null;

  return {
    maxMembers: typedPkg.max_members ?? null,
    maxTrainers,
  };
}

// Yardımcı: Aktif üye sayısını getir
async function countActiveMembers(adminClient: any, gymId: string): Promise<number> {
  const { count, error } = await adminClient
    .from('gym_memberships')
    .select('id', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .eq('status', 'active');

  if (error) {
    throw new Error(ERRORS.FETCH_FAILED);
  }

  return count ?? 0;
}

// Yardımcı: Aktif antrenör sayısını getir
async function countActiveTrainers(adminClient: any, gymId: string): Promise<number> {
  const { count, error } = await adminClient
    .from('gym_trainers')
    .select('id', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .eq('status', 'active');

  if (error) {
    throw new Error(ERRORS.FETCH_FAILED);
  }

  return count ?? 0;
}
async function createMembershipRecord(
  adminClient: any,
  profileId: string,
  gymId: string
): Promise<void> {
  const { error } = await adminClient
    .from('gym_memberships')
    .insert({
      profile_id: profileId,
      gym_id: gymId,
      status: 'active',
      created_at: new Date().toISOString(),
    });

  if (error) {
    logger.error('Üyelik kaydı oluşturulurken hata', error, { profileId, gymId });
    throw new Error(`${ERRORS.CREATE_FAILED}: Üyelik kaydı oluşturulamadı`);
  }
}

async function createTrainerRecord(
  adminClient: any,
  profileId: string,
  gymId: string
): Promise<void> {
  // Default permissions
  const defaultPermissions = {
    members: { read: true, create: true, update: false },
    packages: { create: false, update: false },
    appointments: { read: true, create: false, delete: false, update: false }
  };

  // Antrenör kaydını oluştur - permissions ile birlikte
  const { error } = await adminClient
    .from('gym_trainers')
    .insert({
      trainer_profile_id: profileId,
      gym_id: gymId,
      status: 'active',
      permissions: defaultPermissions,
    });

  if (error) {
    logger.error('Antrenör kaydı oluşturulurken hata', error, { profileId, gymId });
    throw new Error(`${ERRORS.CREATE_FAILED}: Antrenör kaydı oluşturulamadı`);
  }
}


/**
 * Basit bildirim sistemi ile davet bildirimi gönderir
 */
async function sendInvitationNotificationNew(
  invitation: GymInvitations & { gym?: { name: string }; profile?: { full_name: string } }
): Promise<void> {
  try {
    const configKey = `${invitation.type}_${invitation.role}` as const;
    const config = INVITATION_CONFIGS[configKey];

    if (!config) {
      logger.warn('Bilinmeyen davet konfigürasyonu, bildirim atlandı', {
        type: invitation?.type,
        role: invitation?.role,
      });
      return;
    }

    const direction = config.notificationDirection;
    const gymName = invitation?.gym?.name || 'Salon';
    const userName = invitation?.profile?.full_name || 'Kullanıcı';

    // Davet türüne göre event type belirle
    let eventType: 'request' | 'invite';
    if (invitation.type === 'join_request') {
      eventType = 'request';
    } else {
      eventType = 'invite';
    }

    // Bildirim mesajını oluştur
    const notificationDirection = direction === 'to_gym' ? 'to_gym' : 'to_user';
    const { title, message } = createNotificationMessage(eventType, notificationDirection, {
      userName,
      gymName,
      userRole: invitation.role as 'member' | 'trainer',
      customMessage: invitation.message || undefined,
    });

    // Bildirim gönder
    if (notificationDirection === 'to_gym') {
      // Salona bildirim gönder
      
      await createNotification({
        gymId: invitation.gym_id,
        title,
        message,
      });
    } else {
      // Kullanıcıya bildirim gönder
      await createNotification({
        recipientUserId: invitation.profile_id,
        title,
        message,
      });
    }
  } catch (err) {
    logger.error('Davet bildirimi gönderilirken hata', err, {
      invitationId: invitation?.id,
    });
  }
}

/**
 * Spesifik davet türleri için wrapper fonksiyonlar
 */

// Üye katılma isteği gönder
export async function sendMemberJoinRequest(
  gymId: string,
  message?: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, authUserId) => {
      return await createInvitation({
        gymId,
        profileId: authUserId!,
        type: 'join_request',
        role: 'member',
        message,
      });
    }
  );
}

// Antrenör katılma isteği gönder
export async function sendTrainerJoinRequest(
  gymId: string,
  message?: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(
    async (_, __, authUserId) => {
      return await createInvitation({
        gymId,
        profileId: authUserId!,
        type: 'join_request',
        role: 'trainer',
        message,
      });
    }
  );
}

// Salon → Üye davet gönder
export async function sendGymMemberInvite(
  gymId: string,
  profileId: string,
  message?: string
): Promise<ApiResponse<any>> {
  return await createInvitation({
    gymId,
    profileId,
    type: 'gym_invite',
    role: 'member',
    message,
  });
}

// Salon → Antrenör davet gönder
export async function sendGymTrainerInvite(
  gymId: string,
  profileId: string,
  message?: string
): Promise<ApiResponse<any>> {
  return await createInvitation({
    gymId,
    profileId,
    type: 'gym_invite',
    role: 'trainer',
    message,
  });
}

/**
 * Kullanıcı türüne göre davet listesi alma
 */

// Kullanıcının aldığı davetler (gym_invite)
export async function getUserIncomingInvites(
  role?: InvitationRole
): Promise<ApiResponse<any[]>> {
  return await createAdminAction(
    async (_, __, authUserId, adminClient) => {
      // Trainer invitations için özel query - gym manager bilgisi gerekli
      if (role === 'trainer') {
        let query = adminClient.from('gym_invitations').select(`
          *,
          gym:gyms(
            id,
            name,
            address,
            gym_phone,
            company:companies(
              manager_profile_id,
              manager:profiles!companies_manager_profile_id_fkey(
                id,
                full_name,
                email
              )
            )
          )
        `);

        query = query
          .eq('profile_id', authUserId!)
          .eq('type', 'gym_invite')
          .eq('role', 'trainer')
          .order('created_at', { ascending: false });

        const { data, error } = await query;

        if (error) {
          logger.error('Trainer davetleri alınırken hata', error);
          throw new Error(`${ERRORS.FETCH_FAILED}: Trainer davetleri yüklenemedi.`);
        }

        // Veriyi trainer component'inin beklediği formata dönüştür
        // Güvenli mapping: manager bilgisi gelmezse fallback kullan
        const transformedData = (data || []).map((invitation: any) => {
          const manager = invitation?.gym?.company?.manager;
          return {
            ...invitation,
            profile: manager
              ? {
                  id: manager.id,
                  full_name: manager.full_name,
                  email: manager.email,
                }
              : {
                  id: invitation?.gym?.company?.manager_profile_id ?? 'unknown',
                  full_name: 'Salon Yöneticisi',
                  email: null,
                },
          };
        });

        return transformedData;
      }

      // Diğer roller için normal query
      const result = await queryInvitations({
        profileId: authUserId!,
        type: 'gym_invite',
        role,
      });
      return result.success ? (result.data || []) : [];
    }
  );
}

// Kullanıcının gönderdiği istekler (join_request)
export async function getUserOutgoingRequests(
  role?: InvitationRole
): Promise<ApiResponse<any[]>> {
  return await createAdminAction(
    async (_, __, authUserId) => {
      const result = await queryInvitations({
        profileId: authUserId!,
        type: 'join_request',
        role,
      });
      return result.success ? (result.data || []) : [];
    }
  );
}

// Gym manager'ın aldığı istekler (join_request)
export async function getGymIncomingRequests(
  gymId: string,
  role?: InvitationRole
): Promise<ApiResponse<any[]>> {
  return await queryInvitations({
    gymId,
    type: 'join_request',
    role,
  });
}

// Gym manager'ın gönderdiği davetler (gym_invite)
export async function getGymOutgoingInvites(
  gymId: string,
  role?: InvitationRole
): Promise<ApiResponse<any[]>> {
  return await queryInvitations({
    gymId,
    type: 'gym_invite',
    role,
  });
}

/**
 * Toplu işlemler
 */

// Süresi dolmuş davetleri temizle
export async function cleanExpiredInvitations(): Promise<ApiResponse<number>> {
  return await createAdminAction(async (_, __, _authUserId, adminClient) => {
    const { data, error } = await adminClient
      .from('gym_invitations')
      .update({ status: 'expired' })
      .lt('expires_at', new Date().toISOString())
      .eq('status', 'pending')
      .select('id');

    if (error) {
      logger.error('Süresi dolmuş davetler temizlenirken hata', error);
      throw new Error(`${ERRORS.UPDATE_FAILED}: Süresi dolmuş davetler temizlenemedi.`);
    }

    return data?.length || 0;
  });
}

// Kullanıcının davet istatistikleri
export async function getUserInvitationStats(
  userId?: string
): Promise<ApiResponse<any>> {
  return await createAdminAction(async (_, __, authUserId, adminClient) => {
    const targetUserId = userId || authUserId!;

    if (authUserId !== targetUserId) {
      throw new Error(ERRORS.ACCESS_DENIED);
    }

    const { data, error } = await adminClient
      .from('gym_invitations')
      .select('status, type, role')
      .eq('profile_id', targetUserId);

    if (error) {
      logger.error('Davet istatistikleri alınırken hata', error, { userId: targetUserId });
      throw new Error(`${ERRORS.FETCH_FAILED}: İstatistikler yüklenemedi.`);
    }

    const stats = {
      total: data?.length || 0,
      pending: data?.filter(i => i.status === 'pending').length || 0,
      accepted: data?.filter(i => i.status === 'accepted').length || 0,
      rejected: data?.filter(i => i.status === 'rejected').length || 0,
      expired: data?.filter(i => i.status === 'expired').length || 0,
      byType: {
        join_request: data?.filter(i => i.type === 'join_request').length || 0,
        gym_invite: data?.filter(i => i.type === 'gym_invite').length || 0,
      },
      byRole: {
        member: data?.filter(i => i.role === 'member').length || 0,
        trainer: data?.filter(i => i.role === 'trainer').length || 0,
      },
    };

    return stats;
  });
}
