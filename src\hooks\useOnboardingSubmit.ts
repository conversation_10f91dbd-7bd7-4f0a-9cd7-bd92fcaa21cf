import { useState } from 'react';
import { PlatformRoles } from '@/types/database/enums';
import type { PersonalData, MemberData, TrainerData, ManagerData } from '@/types/onboarding';
import { updateFullname } from '@/lib/actions/user/profile-actions';
import { upsertMemberDetails } from '@/lib/actions/user/member-actions';
import { createTrainerDetails } from '@/lib/actions/user/create-user-actions';
import { createInactiveCompany } from '@/lib/actions/dashboard/company/company-actions';
import { validateOnboardingRoleSelection } from '@/lib/auth/role-conflict-validation';

/**
 * Onboarding submission hook
 * Following Clean Code principles - single responsibility for form submission
 */

export function useOnboardingSubmit(
  selectedRoles: PlatformRoles[],
  hasFullName: boolean
) {
  const [isPending, setIsPending] = useState(false);

  /**
   * Updates user profile with full name
   * Single responsibility: only handles profile update
   */
  const updateUserProfile = async (personalData: PersonalData): Promise<void> => {
    if (hasFullName) return;

    const fullName = `${personalData.first_name.trim()} ${personalData.last_name.trim()}`;
    const formData = new FormData();
    formData.append('full_name', fullName);

    const result = await updateFullname(formData);
    if (!result.success) {
      throw new Error(result.error || 'Profil bilgileri güncellenemedi');
    }
  };

  /**
   * Creates FormData for member details
   * DRY principle: reusable FormData creation
   */
  const createMemberFormData = (memberData: MemberData): FormData => {
    const formData = new FormData();
    formData.append('age', memberData.age);
    formData.append('gender', memberData.gender);

    if (memberData.height_cm)
      formData.append('height_cm', memberData.height_cm);
    if (memberData.weight_kg)
      formData.append('weight_kg', memberData.weight_kg);
    if (memberData.fitness_goal)
      formData.append('fitness_goal', memberData.fitness_goal);

    return formData;
  };

  /**
   * Saves member details
   * Single responsibility: only handles member data saving
   */
  const saveMemberDetails = async (memberData: MemberData): Promise<void> => {
    if (!selectedRoles.includes('member')) return;

    const memberFormData = createMemberFormData(memberData);
    const result = await upsertMemberDetails(memberFormData);

    if (!result.success) {
      throw new Error(result.error || 'Üye bilgileri kaydedilemedi');
    }
  };

  /**
   * Saves trainer details
   * Single responsibility: only handles trainer data saving
   */
  const saveTrainerDetails = async (trainerData: TrainerData): Promise<void> => {
    if (!selectedRoles.includes('trainer')) return;

    const result = await createTrainerDetails({
      specialization: trainerData.specialization,
      certification_level: trainerData.certification_level,
      experience_years: trainerData.experience_years
        ? parseInt(trainerData.experience_years)
        : null,
      bio: trainerData.bio || null,
    });

    if (!result.success) {
      throw new Error(result.error || 'Antrenör bilgileri kaydedilemedi');
    }
  };

  /**
   * Saves company details
   * Single responsibility: only handles company data saving
   */
  const saveManagerDetails = async (managerData: ManagerData): Promise<void> => {
    if (!selectedRoles.includes('company_manager')) return;

    const formData = new FormData();
    formData.append('name', managerData.companyName);
    formData.append('phone', managerData.companyPhone);
    formData.append('email', managerData.companyEmail);
    // Logo onboarding'de yüklendiyse gönder
    if (managerData.logoUrl) {
      formData.append('logo_url', managerData.logoUrl);
    }

    const result = await createInactiveCompany(formData);

    if (!result.success) {
      throw new Error(result.error || 'Şirket bilgileri kaydedilemedi');
    }
  };

  // Navigation handled by caller (onboarding client) to support in-flow payment

  /**
   * Main form submission handler
   * Clean, focused function following single responsibility
   */
  const submitForm = async (
    personalData: PersonalData,
    memberData: MemberData,
    trainerData: TrainerData,
    managerData: ManagerData,
    onError: (error: string) => void
  ): Promise<boolean> => {
    setIsPending(true);
    try {
      // Önce rol seçimi validasyonu yap
      const roleValidation = await validateOnboardingRoleSelection(selectedRoles);
      if (!roleValidation.success) {
        throw new Error(roleValidation.error || 'Rol seçimi geçersiz');
      }

      await updateUserProfile(personalData);
      await saveMemberDetails(memberData);
      await saveTrainerDetails(trainerData);
      await saveManagerDetails(managerData);
      return true;
    } catch (error) {
      console.error('Form gönderim hatası:', error);
      onError(error instanceof Error ? error.message : 'Beklenmeyen bir hata oluştu');
      return false;
    } finally {
      setIsPending(false);
    }
  };

  return {
    isPending,
    submitForm,
  };
}
