import { type MemberMembership } from '@/lib/actions/dashboard/member/membership-actions';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, Calendar, Phone } from 'lucide-react';
import Link from 'next/link';
import { MembershipActions } from '../../components/membership-actions';

export function MemberMemberships({
  memberships,
}: {
  memberships: MemberMembership[];
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Üyeliklerim
        </CardTitle>
        <CardDescription>
          Aktif ve pasif üyeliklerinizi görüntüleyin
        </CardDescription>
      </CardHeader>
      <CardContent>
        {memberships.length === 0 ? (
          <div className="py-8 text-center">
            <Building2 className="text-muted-foreground mx-auto h-12 w-12" />
            <h3 className="mt-4 text-lg font-semibold">Henüz üyeliğiniz yok</h3>
            <p className="text-muted-foreground">
              Spor salonlarına üye olmak için arama yapın
            </p>
            <Button asChild className="mt-4">
              <Link href="/findGym">Salon Ara</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {memberships.map(membership => (
              <div
                key={membership.id}
                className="hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">
                    <Building2 className="text-primary h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{membership.gym.name}</h3>
                    <p className="text-muted-foreground flex items-center gap-1 text-sm">
                      <Building2 className="h-3 w-3" />
                      {membership.gym.address}
                    </p>
                    {membership.gym.phone && (
                      <p className="text-muted-foreground flex items-center gap-1 text-sm">
                        <Phone className="h-3 w-3" />
                        {membership.gym.phone}
                      </p>
                    )}
                    <p className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
                      <Calendar className="h-3 w-3" />
                      Üyelik:{' '}
                      {new Date(membership.created_at).toLocaleDateString(
                        'tr-TR'
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      membership.status === 'active' ? 'default' : 'secondary'
                    }
                  >
                    {membership.status === 'active' ? 'Aktif' : 'Pasif'}
                  </Badge>
                  {membership.gym.slug && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/gym/${membership.gym.slug}`}>Detay</Link>
                    </Button>
                  )}
                  <MembershipActions
                    membershipId={membership.id}
                    currentStatus={membership.status}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
