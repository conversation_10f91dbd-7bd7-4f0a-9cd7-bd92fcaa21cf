'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Search,
  UserPlus,
  Loader2,
  Mail,
  Award,
  Briefcase,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  getTrainerSpecializationLabel,
  getTrainerCertificationLabel,
} from '@/lib/constants/trainer-constants';

import { sendGymTrainerInvite } from '@/lib/actions/gym_invitations/invitation-actions';
import { findTrainerByCode, TrainerSearchResult } from '@/lib/actions/gym_invitations/user-search-service';


// Form validation schema
const inviteTrainerSchema = z.object({
  trainerCode: z.string().min(1, 'Antrenör kodu gereklidir'),
  message: z
    .string()
    .max(500, 'Mesaj en fazla 500 karakter olabilir')
    .optional(),
});

interface InviteTrainerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTrainerInvited: () => void;
  gymId: string;
}

type InviteTrainerFormValues = z.infer<typeof inviteTrainerSchema>;

export function InviteTrainerDialog({
  isOpen,
  onClose,
  onTrainerInvited,
  gymId,
}: InviteTrainerDialogProps) {
  const [isSearching, setIsSearching] = useState(false);
  const [isInviting, setIsInviting] = useState(false);
  const [foundTrainer, setFoundTrainer] = useState<TrainerSearchResult | null>(
    null
  );

  const form = useForm<InviteTrainerFormValues>({
    resolver: zodResolver(inviteTrainerSchema),
    defaultValues: {
      trainerCode: '',
      message: '',
    },
  });

  const handleSearch = async () => {
    const trainerCode = form.getValues('trainerCode');
    if (!trainerCode.trim()) {
      toast.error('Lütfen antrenör kodunu girin');
      return;
    }

    setIsSearching(true);
    try {
      const result = await findTrainerByCode(trainerCode.trim());

      if (result.success && result.data) {
        setFoundTrainer(result.data);
        toast.success('Antrenör bulundu!');
      } else {
        setFoundTrainer(null);
        toast.error('Bu kodla antrenör bulunamadı');
      }
    } catch (error) {
      toast.error('Arama sırasında bir hata oluştu');
      setFoundTrainer(null);
    } finally {
      setIsSearching(false);
    }
  };

  const handleInvite = async (values: InviteTrainerFormValues) => {
    if (!foundTrainer) {
      toast.error('Önce antrenör araması yapın');
      return;
    }

    setIsInviting(true);
    try {
      const result = await sendGymTrainerInvite(
        gymId,
        foundTrainer.profile_id,
        values.message
      );

      if (result.success) {
        toast.success(
          `${foundTrainer.profiles?.full_name || 'Antrenör'} antrenörüne davet gönderildi`
        );
        onTrainerInvited();
        handleClose();
      } else {
        toast.error(result.error || 'Davet gönderilirken bir hata oluştu');
      }
    } catch (error) {
      toast.error('Davet gönderilirken bir hata oluştu');
    } finally {
      setIsInviting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setFoundTrainer(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Antrenör Davet Et
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleInvite)}
            className="space-y-6"
          >
            {/* Trainer Search */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="trainerCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Antrenör Kodu</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input
                          placeholder="**********"
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            setFoundTrainer(null); // Clear previous search
                          }}
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleSearch}
                        disabled={isSearching}
                      >
                        {isSearching ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Search className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Trainer Search Result */}
              {foundTrainer && (
                <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">
                            {foundTrainer.profiles?.full_name ||
                              'Bilinmeyen Kullanıcı'}
                          </h3>
                          <div className="text-muted-foreground flex items-center gap-2 text-sm">
                            <Mail className="h-3 w-3" />
                            {foundTrainer.profiles?.email || 'Email bulunamadı'}
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {foundTrainer.invite_code}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 gap-3 text-sm md:grid-cols-2">
                        <div>
                          <div className="flex items-center gap-1">
                            <Award className="h-3 w-3" />
                            <span className="font-medium">Uzmanlık:</span>
                          </div>
                          <p className="text-muted-foreground ml-4">
                            {getTrainerSpecializationLabel(
                              foundTrainer.specialization
                            )}
                          </p>
                        </div>

                        <div>
                          <div className="flex items-center gap-1">
                            <Briefcase className="h-3 w-3" />
                            <span className="font-medium">Sertifika:</span>
                          </div>
                          <p className="text-muted-foreground ml-4">
                            {getTrainerCertificationLabel(
                              foundTrainer.certification_level
                            )}
                          </p>
                        </div>
                      </div>

                      {foundTrainer.experience_years && (
                        <div className="text-sm">
                          <span className="font-medium">Deneyim:</span>
                          <span className="text-muted-foreground ml-1">
                            {foundTrainer.experience_years} yıl
                          </span>
                        </div>
                      )}

                      {foundTrainer.bio && (
                        <div className="text-sm">
                          <span className="font-medium">Hakkında:</span>
                          <p className="text-muted-foreground mt-1">
                            {foundTrainer.bio}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Invitation Message */}
            {foundTrainer && (
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Davet Mesajı (Opsiyonel)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Salonumuzda çalışmak ister misiniz? Detayları görüşmek için bekliyoruz."
                        rows={3}
                        maxLength={500}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isInviting}
              >
                İptal
              </Button>
              <Button type="submit" disabled={!foundTrainer || isInviting}>
                {isInviting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Davet Gönderiliyor...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Davet Gönder
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
