export default function PricingLoading() {
  return (
    <main className="flex-1">
      {/* Hero Section Skeleton */}
      <section className="bg-background relative overflow-hidden py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <div className="mb-8 flex justify-center">
              <div className="bg-muted h-8 w-32 animate-pulse rounded-full"></div>
            </div>
            <div className="mb-12 space-y-4">
              <div className="bg-muted h-16 w-full animate-pulse rounded-lg"></div>
              <div className="bg-muted mx-auto h-6 w-3/4 animate-pulse rounded"></div>
            </div>
            <div className="mb-12 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-muted h-6 w-full animate-pulse rounded"
                ></div>
              ))}
            </div>
            <div className="mb-16 flex flex-col justify-center gap-4 sm:flex-row">
              <div className="bg-muted h-12 w-40 animate-pulse rounded-lg"></div>
              <div className="bg-muted h-12 w-32 animate-pulse rounded-lg"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Plans Skeleton */}
      <section className="bg-background py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-5xl">
            {/* Toggle Skeleton */}
            <div className="mb-12 flex justify-center">
              <div className="bg-muted h-12 w-64 animate-pulse rounded-lg"></div>
            </div>

            {/* Plans Grid Skeleton */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {Array.from({ length: 2 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-muted/30 border-border rounded-xl border p-6"
                >
                  <div className="mb-6 text-center">
                    <div className="bg-muted mx-auto mb-4 h-12 w-12 animate-pulse rounded-full"></div>
                    <div className="bg-muted mx-auto mb-2 h-6 w-24 animate-pulse rounded"></div>
                    <div className="bg-muted mx-auto mb-4 h-4 w-48 animate-pulse rounded"></div>
                    <div className="bg-muted mx-auto h-12 w-32 animate-pulse rounded"></div>
                  </div>

                  <div className="mb-6 space-y-3">
                    {Array.from({ length: 5 }).map((_, j) => (
                      <div key={j} className="flex items-center gap-3">
                        <div className="bg-muted h-5 w-5 animate-pulse rounded"></div>
                        <div className="bg-muted h-4 flex-1 animate-pulse rounded"></div>
                      </div>
                    ))}
                  </div>

                  <div className="bg-muted h-12 w-full animate-pulse rounded-lg"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Table Skeleton */}
      <section className="bg-muted/30 py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="mb-16 text-center">
              <div className="bg-muted mx-auto mb-4 h-10 w-64 animate-pulse rounded-lg"></div>
              <div className="bg-muted mx-auto h-6 w-96 animate-pulse rounded"></div>
            </div>

            <div className="bg-background border-border overflow-hidden rounded-xl border">
              <div className="p-6">
                <div className="mb-4 grid grid-cols-3 gap-4">
                  <div className="bg-muted h-8 animate-pulse rounded"></div>
                  <div className="bg-muted h-8 animate-pulse rounded"></div>
                  <div className="bg-muted h-8 animate-pulse rounded"></div>
                </div>

                {Array.from({ length: 8 }).map((_, i) => (
                  <div
                    key={i}
                    className="border-border grid grid-cols-3 gap-4 border-t py-3"
                  >
                    <div className="bg-muted h-4 animate-pulse rounded"></div>
                    <div className="bg-muted mx-auto h-4 w-6 animate-pulse rounded"></div>
                    <div className="bg-muted mx-auto h-4 w-6 animate-pulse rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Skeleton */}
      <section className="bg-background py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="mb-16 text-center">
              <div className="bg-muted mx-auto mb-4 h-10 w-64 animate-pulse rounded-lg"></div>
              <div className="bg-muted mx-auto h-6 w-80 animate-pulse rounded"></div>
            </div>

            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-muted/30 border-border rounded-xl border p-6"
                >
                  <div className="bg-muted h-6 w-3/4 animate-pulse rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
