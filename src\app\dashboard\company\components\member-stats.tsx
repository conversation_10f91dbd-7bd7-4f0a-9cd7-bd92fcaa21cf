import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, UserCheck, UserX, TrendingUp } from 'lucide-react';
import type { GymMemberWithDetails } from '@/types/business/gym';

interface MemberStatsProps {
  membersByStatus: {
    all: GymMemberWithDetails[];
    active: GymMemberWithDetails[];
    passive: GymMemberWithDetails[];
  };
}

export function MemberStats({ membersByStatus }: MemberStatsProps) {
  const totalMembers = membersByStatus.all.length;
  const activeMembers = membersByStatus.active.length;
  const passiveMembers = membersByStatus.passive.length;

  // Calculate percentage of active members
  const activePercentage =
    totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
          <Users className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalMembers}</div>
          <p className="text-muted-foreground text-xs">Kayıtlı üye sayısı</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
          <UserCheck className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeMembers}</div>
          <p className="text-muted-foreground text-xs">Aktif üyelik durumu</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Pasif Üye</CardTitle>
          <UserX className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{passiveMembers}</div>
          <p className="text-muted-foreground text-xs">Pasif üyelik durumu</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Aktiflik Oranı</CardTitle>
          <TrendingUp className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">%{activePercentage}</div>
          <p className="text-muted-foreground text-xs">Aktif üye oranı</p>
        </CardContent>
      </Card>
    </div>
  );
}
