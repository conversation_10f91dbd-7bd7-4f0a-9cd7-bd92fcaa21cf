'use server';

import { ApiResponse } from '@/types/global/api';
import {
  createAction,
  upsertMemberDetailsSchema,
  validateFormData,
} from '../core';
import { MemberDetails } from '@/types/database/tables';

/**
 * Kullanıcının member details bilgilerini günceller
 */
export async function upsertMemberDetails(
  formData: FormData
): Promise<ApiResponse<MemberDetails>> {
  return createAction<MemberDetails>(async (_, supabase, userId) => {
    // Form verilerini doğrula
    const validation = await validateFormData(
      formData,
      upsertMemberDetailsSchema
    );
    if (validation.error) {
      throw new Error(validation.error);
    }

    const { age, gender, height_cm, weight_kg, fitness_goal } =
      validation.data!;

    // Upsert ile hem güncelleme hem de ekleme işlemini tek seferde yap
    const { error: upsertError } = await supabase
      .from('member_details')
      .upsert({
        profile_id: userId,
        age,
        gender,
        height_cm,
        weight_kg,
        fitness_goal,
        updated_at: new Date().toISOString(),
      })
      .eq('profile_id', userId);

    if (upsertError) {
      throw new Error(
        'Detay bilgileri kaydedilirken hata oluştu: ' + upsertError.message
      );
    }

    // Güncellenmiş profil bilgilerini getir
    const profileResult = await getMemberDetails();
    if (!profileResult.success || !profileResult.data) {
      throw new Error('Güncellenmiş profil bilgileri alınamadı');
    }

    return profileResult.data;
  });
}

/**
 * Kullanıcının member detaylarını ve üyelik durumunu getirir
 * @param userId - Kullanıcı ID'si (opsiyonel, verilmezse authenticated user kullanılır)
 * @returns object - Üyelik durumu ve member verileri
 */

export async function getMemberDetails(
  userId?: string | null
): Promise<ApiResponse<MemberDetails | null>> {
  return createAction<MemberDetails | null>(async (_, supabase, authUserId) => {
    // userId verilmemişse authenticated user'ı kullan
    const targetUserId = userId || authUserId;

    if (!targetUserId) {
      throw new Error('Kullanıcı kimlik doğrulaması gerekli.');
    }

    try {
      // Member_details tablosundan kullanıcının tüm verilerini getir
      const { data: memberDetails, error } = await supabase
        .from('member_details')
        .select('*')
        .eq('profile_id', targetUserId)
        .single();

      if (error) {
        // Kayıt bulunamadı hatası normal bir durum (kullanıcı member değil)
        if (error.code === 'PGRST116') {
          return null;
        }
        throw new Error(`Member kontrolü hatası: ${error.message}`);
      }

      return memberDetails;
    } catch (error) {
      throw new Error(
        `Member detayları alınırken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      );
    }
  });
}
