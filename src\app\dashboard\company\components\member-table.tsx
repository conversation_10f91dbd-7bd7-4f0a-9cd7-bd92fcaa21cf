'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Loader2, RefreshCw } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import type { GymMemberWithDetails } from '@/types/business/gym';

interface MemberTableProps {
  members: GymMemberWithDetails[];
  isLoading: boolean;
  onRefresh: () => void;
}

export function MemberTable({
  members,
  isLoading,
  onRefresh,
}: MemberTableProps) {
  // const [selectedMember, setSelectedMember] =
  //   useState<MemberWithDetails | null>(null);

  const getUserInitials = (user: { name?: string; surname?: string }) => {
    if (!user?.name) return '??';
    const nameParts = `${user.name} ${user.surname || ''}`.trim().split(/\s+/);
    return nameParts
      .map(p => p[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserFullName = (user: {
    name?: string;
    surname?: string;
    email: string;
  }) => {
    if (!user) return '';
    return `${user.name || ''} ${user.surname || ''}`.trim() || user.email;
  };

  const getMembershipBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500 hover:bg-green-600">Aktif</Badge>;
      case 'passive':
        return <Badge variant="secondary">Pasif</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Üye Listesi</CardTitle>
          <p className="text-muted-foreground text-sm">
            {members.length} üye gösteriliyor
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Üye</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Kayıt Tarihi</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  <div className="flex justify-center">
                    <Loader2 className="text-muted-foreground h-5 w-5 animate-spin" />
                  </div>
                </TableCell>
              </TableRow>
            ) : members.length > 0 ? (
              members.map(member => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={member.user?.avatar_url || ''}
                          alt={getUserFullName(
                            member.user || {
                              name: '',
                              surname: '',
                              email: member.email,
                            }
                          )}
                        />
                        <AvatarFallback>
                          {getUserInitials(
                            member.user || {
                              name: '',
                              surname: '',
                              email: member.email,
                            }
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {getUserFullName(
                            member.user || {
                              name: '',
                              surname: '',
                              email: member.email,
                            }
                          )}
                        </div>
                        <div className="text-muted-foreground text-sm">
                          {member.user?.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getMembershipBadge(member.status)}</TableCell>
                  <TableCell>{formatDate(member.created_at)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm" onClick={() => { }}>
                      Detaylar
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  <div className="text-muted-foreground">
                    Henüz üye bulunmuyor
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
