'use server';

import { createClient } from '@/lib/supabase/server';
import { ApiResponse } from '@/types/global/api';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/types';

import {
  ActionOptions,
  getUserId,
  handleActionError,
  performRevalidation,
} from './core-utils';
import { PermissionRequirement } from './trainer-action-helpers';

// Trainer action handler tipi
type TrainerActionHandler<T, P = unknown> = (
  params: P,
  supabase: SupabaseClient<Database>,
  userId: string,
  gymId: string,
  gymTrainerId: string
) => Promise<T>;

// Trainer action options
export interface TrainerActionOptions extends ActionOptions {
  requiredPermission?: PermissionRequirement;
}

/**
 * Trainer için özel action creator
 * Otomatik olarak:
 * - Auth kontrolü yapar
 * - Gym erişim kontrolü yapar
 * - Permission kontrolü yapar (opsiyonel)
 * - Trainer'ın o gym'de aktif olduğunu kontrol eder
 */
export async function createTrainerAction<T = unknown, P = unknown>(
  gymId: string,
  handler: TrainerActionHandler<T, P>,
  options: TrainerActionOptions = {}
): Promise<ApiResponse<T>> {
  try {
    // Supabase client oluştur
    const supabase = await createClient();

    // User ID al ve auth kontrolü yap
    const userId = await getUserId();
    if (!userId) {
      throw new Error('Bu işlem için giriş yapmanız gerekiyor.');
    }

    // Gym ID validasyonu
    if (!gymId || typeof gymId !== 'string') {
      throw new Error('Geçersiz salon ID');
    }

    // Trainer'ın bu gym'de aktif olduğunu kontrol et
    const { data: gymTrainer, error: trainerError } = await supabase
      .from('gym_trainers')
      .select('id, permissions')
      .eq('gym_id', gymId)
      .eq('trainer_profile_id', userId)
      .eq('status', 'active')
      .single();

    if (trainerError || !gymTrainer) {
      throw new Error('Bu salona erişim yetkiniz bulunmuyor');
    }

    // Permission kontrolü (eğer gerekiyorsa)
    if (options.requiredPermission) {
      const { category, action } = options.requiredPermission;

      // gym_trainers tablosundan permissions'ı kontrol et
      const hasPermission = gymTrainer.permissions?.[category]?.[action] === true;

      if (!hasPermission) {
        throw new Error('Bu işlem için yetkiniz bulunmuyor');
      }
    }

    // Handler'ı çalıştır
    const result = await handler(
      null as P,
      supabase,
      userId,
      gymId,
      gymTrainer.id // gym_trainer_id'yi geç, permissions için kullanılabilir
    );

    // Cache revalidation
    await performRevalidation(options);

    return { success: true, data: result };
  } catch (error: unknown) {
    return await handleActionError(error, {
      requireAuth: true,
      ...options,
    });
  }
}
