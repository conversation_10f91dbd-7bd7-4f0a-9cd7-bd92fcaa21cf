import { memo } from "react";
import { Check, Crown, Calendar, Star, Gift } from "lucide-react";
import { GymPackages } from "@/types/database/tables";

interface GymPackagesSectionProps {
  packages: GymPackages[];
}

export const GymPackagesSection = memo(function GymPackagesSection({
  packages
}: GymPackagesSectionProps) {
  const getPackageTypeText = (packageType: string | null, durationDays: number | null | undefined) => {
    if (!packageType) return "";

    if (packageType === "daily" && durationDays) {
      return `${durationDays} gün`;
    }
    if (packageType === "appointment_standard") {
      return "Standart Randevu Paketi";
    }
    if (packageType === "appointment_vip") {
      return "VIP Randevu Paketi";
    }
    return "";
  };

  const getPackageIcon = (packageType: string | null) => {
    switch (packageType) {
      case "appointment_vip":
        return <Crown className="h-6 w-6" />;
      case "appointment_standard":
        return <Star className="h-6 w-6" />;
      case "daily":
        return <Calendar className="h-6 w-6" />;
      default:
        return <Calendar className="h-6 w-6" />;
    }
  };

  const getPackageGradient = (packageType: string | null) => {
    switch (packageType) {
      case "appointment_vip":
        return "from-purple-500/10 to-purple-600/5";
      case "appointment_standard":
        return "from-primary/10 to-primary/5";
      case "daily":
        return "from-green-500/10 to-green-600/5";
      default:
        return "from-green-500/10 to-green-600/5";
    }
  };

  const getPackageBorderColor = (packageType: string | null) => {
    switch (packageType) {
      case "appointment_vip":
        return "border-purple-500/20";
      case "appointment_standard":
        return "border-primary/20";
      case "daily":
        return "border-green-500/20";
      default:
        return "border-green-500/20";
    }
  };

  const isPopular = (packageType: string | null) => {
    return packageType === "appointment_standard" || packageType === "appointment_vip";
  };

  return (
    <section className="py-24 bg-gradient-to-b from-muted/20 to-background" aria-labelledby="packages-heading">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6" aria-label="Üyelik paketleri etiketi">
            <Gift className="h-4 w-4" aria-hidden="true" />
            <span>Üyelik Paketleri</span>
          </div>
          <h2 id="packages-heading" className="text-4xl md:text-6xl font-bold text-foreground mb-6">
            Size Uygun <span className="text-primary">Paketi</span> Seçin
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Fitness hedeflerinize uygun paketi seçin ve sağlıklı yaşam yolculuğunuza başlayın
          </p>
        </div>

        {packages.length > 0 ? (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-7xl mx-auto" role="list">
            {packages.map((pkg, index) => (
              <div
                role="listitem"
                key={pkg.id}
                className={`relative group bg-card/80 backdrop-blur-sm rounded-3xl shadow-lg border transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 ${
                  isPopular(pkg.package_type)
                    ? 'border-primary/30 shadow-primary/10 bg-gradient-to-br from-primary/5 to-primary/10'
                    : `border-border/50 hover:border-primary/30 bg-gradient-to-br ${getPackageGradient(pkg.package_type)}`
                }`}
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {/* Glow Effect */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/10 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Popular Badge */}
                {isPopular(pkg.package_type) && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-6 py-2 rounded-full text-sm font-semibold shadow-lg flex items-center gap-2">
                      <Star className="h-4 w-4 fill-current" />
                      <span>En Popüler</span>
                    </div>
                  </div>
                )}

                <div className="relative p-8">
                  {/* Package Header */}
                  <div className="text-center mb-8">
                    <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 ${
                      isPopular(pkg.package_type)
                        ? 'bg-primary text-primary-foreground shadow-lg shadow-primary/25'
                        : `bg-gradient-to-br ${getPackageGradient(pkg.package_type)} ${getPackageBorderColor(pkg.package_type)} border`
                    }`}>
                      {getPackageIcon(pkg.package_type)}
                    </div>

                    <h3 className="text-2xl font-bold text-card-foreground mb-3 group-hover:text-primary transition-colors">
                      {pkg.name}
                    </h3>

                    <div className="inline-flex items-center gap-2 px-3 py-1 bg-muted/50 rounded-full" aria-label="Paket süresi">
                      <Calendar className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
                      <span className="text-muted-foreground text-sm font-medium">
                        {getPackageTypeText(pkg.package_type, pkg.duration_days)}
                      </span>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-8">
                    <div className="flex items-baseline justify-center gap-1 mb-2">
                      <span className="text-5xl font-bold text-card-foreground group-hover:text-primary transition-colors">
                        {pkg.price.toLocaleString()}
                      </span>
                      <span className="text-2xl font-semibold text-muted-foreground">₺</span>
                    </div>
                    {pkg.package_type === "appointment_standard" && (
                      <p className="text-muted-foreground">grup antrenmanı</p>
                    )}
                    {pkg.package_type === "appointment_vip" && (
                      <div className="mt-2">
                        <span className="inline-flex items-center gap-1 px-3 py-1 bg-purple-500/10 text-purple-600 dark:text-purple-400 rounded-full text-sm font-medium">
                          <Crown className="h-4 w-4" />
                          Kişisel antrenman
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Description */}
                  {pkg.description && (
                    <div className="mb-8">
                      <p className="text-muted-foreground text-center leading-relaxed text-sm">
                        {pkg.description}
                      </p>
                    </div>
                  )}

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-card-foreground font-medium">Tüm ekipmanlara erişim</span>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-card-foreground font-medium">Grup dersleri dahil</span>
                    </div>

                    {pkg.package_type !== "daily" && (
                      <>
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                          </div>
                          <span className="text-card-foreground font-medium">Kişisel antrenör desteği</span>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-green-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                          </div>
                          <span className="text-card-foreground font-medium">Beslenme danışmanlığı</span>
                        </div>
                      </>
                    )}

                    {pkg.package_type === "appointment_vip" && (
                      <div className="flex items-center gap-3">
                        <div className="w-6 h-6 bg-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                          <Crown className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        </div>
                        <span className="text-card-foreground font-medium">VIP soyunma odası</span>
                      </div>
                    )}
                  </div>

                  {/* CTA Button */}
                  <button
                    className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 ${
                      isPopular(pkg.package_type)
                        ? 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl'
                        : 'bg-muted hover:bg-muted/80 text-foreground hover:text-primary border border-border hover:border-primary/30'
                    }`}
                    aria-label={`${pkg.name} paketini seç`}
                  >
                    Paketi Seç
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-br from-muted/50 to-muted/30 rounded-3xl flex items-center justify-center mx-auto mb-8 border border-border/50">
                <Calendar className="h-16 w-16 text-muted-foreground" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary/20 rounded-full border border-primary/30 flex items-center justify-center animate-pulse">
                <Gift className="h-4 w-4 text-primary" />
              </div>
            </div>
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Paketler Yakında Geliyor
            </h3>
            <p className="text-muted-foreground text-lg max-w-md mx-auto mb-8">
              Özel üyelik paketlerimiz hazırlanıyor. En iyi fırsatları kaçırmamak için bizi takip edin.
            </p>
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-primary/10 border border-primary/20 rounded-full text-primary font-medium">
              <Star className="h-4 w-4" />
              <span>Yakında duyurulacak</span>
            </div>
          </div>
        )}
      </div>
    </section>
  );
});
