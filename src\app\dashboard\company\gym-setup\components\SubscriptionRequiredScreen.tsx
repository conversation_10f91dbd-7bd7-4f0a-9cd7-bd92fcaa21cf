'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, AlertTriangle, ArrowRight } from 'lucide-react';
import Link from 'next/link';

export function SubscriptionRequiredScreen() {
  return (
    <div className="flex min-h-[600px] items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-xl">Aktif Abonelik Gerekli</CardTitle>
          <CardDescription>
            Salon oluşturmak için aktif bir aboneliğiniz olmalıdır.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20">
            <CreditCard className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            <AlertDescription className="text-orange-800 dark:text-orange-200">
              Salon oluşturma özelliği sadece ödeme yapmış kullanıcılar için kullanılabilir.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <h4 className="font-medium">Abonelik ile neler yapabilirsiniz:</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                <span>Sınırsız salon oluşturma</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                <span>Üye ve antrenör yönetimi</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                <span>Gelişmiş raporlama</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                <span>7/24 destek</span>
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-2 pt-4">
            <Button asChild className="w-full">
              <Link href="/onboarding">
                <CreditCard className="mr-2 h-4 w-4" />
                Abonelik Satın Al
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link href="/dashboard/company">
                Panel&apos;e Dön
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
