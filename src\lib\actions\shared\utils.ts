/**
 * Shared utility functions across all action modules
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import { VALIDATION_RULES, COMMON_PATTERNS } from './constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export interface PaginationResult<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate required string field
 */
export function validateRequiredString(
  value: string | null | undefined,
  fieldName: string,
  minLength?: number,
  maxLength?: number
): ValidationResult {
  if (!value || value.trim() === '') {
    return { isValid: false, error: `${fieldName} gereklidir` };
  }

  const trimmedValue = value.trim();

  if (minLength && trimmedValue.length < minLength) {
    return {
      isValid: false,
      error: `${fieldName} en az ${minLength} karakter olmalıdır`,
    };
  }

  if (maxLength && trimmedValue.length > maxLength) {
    return {
      isValid: false,
      error: `${fieldName} en fazla ${maxLength} karakter olmalıdır`,
    };
  }

  return { isValid: true };
}

/**
 * Validate age
 */
export function validateAge(age: number): ValidationResult {
  if (
    !age ||
    age < VALIDATION_RULES.AGE_MIN ||
    age > VALIDATION_RULES.AGE_MAX
  ) {
    return {
      isValid: false,
      error: `Yaş ${VALIDATION_RULES.AGE_MIN}-${VALIDATION_RULES.AGE_MAX} arasında olmalıdır`,
    };
  }

  return { isValid: true };
}

/**
 * Validate UUID format
 */
export function validateUUID(uuid: string): ValidationResult {
  if (!uuid || !COMMON_PATTERNS.UUID_REGEX.test(uuid)) {
    return { isValid: false, error: 'Geçersiz ID formatı' };
  }

  return { isValid: true };
}

// ============================================================================
// STRING UTILITIES
// ============================================================================

/**
 * Capitalize first letter of string
 */
export function capitalizeFirst(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert string to title case
 */
export function toTitleCase(str: string): string {
  if (!str) return str;
  return str
    .toLowerCase()
    .split(' ')
    .map(word => capitalizeFirst(word))
    .join(' ');
}

/**
 * Generate slug from string
 */
export function generateSlug(str: string): string {
  if (!str) return '';

  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Truncate string with ellipsis
 */
export function truncateString(str: string, maxLength: number): string {
  if (!str || str.length <= maxLength) return str;
  return str.substring(0, maxLength - 3) + '...';
}

// ============================================================================
// NUMBER UTILITIES
// ============================================================================

/**
 * Generate random number within range
 */
export function randomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Round number to specified decimal places
 */
export function roundToDecimals(num: number, decimals: number): number {
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

// ============================================================================
// DATE UTILITIES
// ============================================================================

/**
 * Format date to Turkish locale
 */
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('tr-TR');
}

/**
 * Format date and time to Turkish locale
 */
export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString('tr-TR');
}

/**
 * Check if date is in the past
 */
export function isDateInPast(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj < new Date();
}

/**
 * Add days to date
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

// ============================================================================
// ARRAY UTILITIES
// ============================================================================

/**
 * Remove duplicates from array
 */
export function removeDuplicates<T>(array: T[]): T[] {
  return [...new Set(array)];
}

/**
 * Chunk array into smaller arrays
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Shuffle array randomly
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// ============================================================================
// OBJECT UTILITIES
// ============================================================================

/**
 * Deep clone object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array)
    return obj.map(item => deepClone(item)) as unknown as T;

  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}

/**
 * Check if object is empty
 */
export function isEmptyObject(obj: object): boolean {
  return Object.keys(obj).length === 0;
}

/**
 * Pick specific keys from object
 */
export function pick<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

/**
 * Omit specific keys from object
 */
export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
}
