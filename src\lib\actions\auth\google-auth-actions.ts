'use server';

import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { getAuthCallbackUrl } from '@/lib/env';
import { AUTH_ERROR_MESSAGES } from './auth-constants';

/**
 * Google OAuth giriş işlemi - sadece server action
 * Client-side createClient kullanımı YOK!
 */
export async function signInWithGoogle() {
  const callbackUrl = getAuthCallbackUrl();

  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: callbackUrl,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    },
  });

  if (error) {
    console.error('Google OAuth error:', error);
    redirect(
      `/auth/login?error=${encodeURIComponent(AUTH_ERROR_MESSAGES.GOOGLE_OAUTH_FAILED)}&context=google_oauth`
    );
  }

  if (data.url) {
    redirect(data.url);
  }
}

/**
 * Google OAuth kayıt işlemi - sadece server action
 * Client-side createClient kullanımı YOK!
 */
export async function signUpWithGoogle() {
  const callbackUrl = getAuthCallbackUrl();
  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: callbackUrl,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    },
  });

  if (error) {
    console.error('Google OAuth registration error:', error);
    redirect(
      `/auth/register?error=${encodeURIComponent(AUTH_ERROR_MESSAGES.GOOGLE_OAUTH_FAILED)}&context=google_oauth`
    );
  }

  if (data.url) {
    redirect(data.url);
  }
}
