"use client";

import Link from "next/link";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { ArrowUpRight, Github, Mail, Instagram } from "lucide-react";

/**
 * Footer - Yeniden tasarım
 * - İnce radial + grid overlay arkaplan
 * - <PERSON>ta bölümde marka/mini açıklama
 * - Sol/sağ link grupları + sosyal ikonlar
 * - Alt şerit: telif ve hızlı bağlantılar
 */
export default function Footer() {
  return (
    <footer className="relative isolate border-t bg-background  p-12">

        {/* Top area */}
        <div className="grid gap-10 py-10 md:grid-cols-3 md:py-14">
          {/* Brand */}
          <div className="space-y-3">
            <Link href="/" className="inline-flex items-center gap-2 font-bold">
              <span className="text-primary">Sportiva</span>
            </Link>
            <p className="max-w-sm text-sm text-muted-foreground">
              Spor salonu yönetimini akıllı çözümlerle modernize eden platform.
              KVKK uyumlu, güvenli ve ölçeklenebilir.
            </p>

            <div className="flex items-center gap-2">
              <ThemeToggle variant="footer" />
              <Link
                href="/contact"
                className="inline-flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground"
                aria-label="İletişim sayfasını aç"
              >
                İletişim
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </div>
          </div>

          {/* Links group 1 */}
          <nav aria-label="Ürün" className="grid grid-cols-2 gap-6 md:grid-cols-1">
            <div className="space-y-2">
              <div className="text-xs font-semibold uppercase tracking-wide text-muted-foreground">
                Ürün
              </div>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/features" className="text-muted-foreground hover:text-foreground">
                    Özellikler
                  </Link>
                </li>
                <li>
                  <Link href="/pricing" className="text-muted-foreground hover:text-foreground">
                    Fiyatlandırma
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-muted-foreground hover:text-foreground">
                    Hakkımızda
                  </Link>
                </li>
                <li>
                  <Link href="/findGym" className="text-muted-foreground hover:text-foreground">
                    Salon Keşfet
                  </Link>
                </li>
              </ul>
            </div>
          </nav>

          {/* Links + social */}
          <div className="flex flex-col gap-6 md:items-end">
            <nav aria-label="Yasal" className="space-y-2">
              <div className="text-xs font-semibold uppercase tracking-wide text-muted-foreground">
                Yasal
              </div>
              <ul className="space-y-2 text-sm md:text-right">
                <li>
                  <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                    Kullanım Şartları
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                    Gizlilik Politikası
                  </Link>
                </li>
              </ul>
            </nav>

            <div className="flex items-center gap-3 md:justify-end">
              <Link
                href="https://github.com/Volkanmolla42/sportiva"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex h-9 w-9 items-center justify-center rounded-md border border-border text-muted-foreground hover:text-foreground"
                aria-label="GitHub"
              >
                <Github className="h-4 w-4" />
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="inline-flex h-9 w-9 items-center justify-center rounded-md border border-border text-muted-foreground hover:text-foreground"
                aria-label="E-posta"
              >
                <Mail className="h-4 w-4" />
              </Link>
              <Link
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex h-9 w-9 items-center justify-center rounded-md border border-border text-muted-foreground hover:text-foreground"
                aria-label="Twitter"
              >
                <Instagram className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="flex flex-col items-center justify-between gap-4 border-t py-6 text-xs text-muted-foreground md:flex-row">
          <p>&copy; 2025 Sportiva. Tüm hakları saklıdır.</p>
          <div className="flex flex-wrap items-center gap-4">
            <Link href="/sitemap" className="hover:text-foreground">
              Site Haritası
            </Link>
            <Link href="/cookies" className="hover:text-foreground">
              Çerez Politikası
            </Link>
            <Link href="/security" className="hover:text-foreground">
              Güvenlik
            </Link>
          </div>
        </div>
    </footer>
  );
}
