'use server';

import { getProfile } from '@/lib/actions/user/profile-actions';
import { HeaderAuthStatus, RoleOption, GymData } from '@/components/header/shared/header-types';
import { ROLE_OPTIONS } from '@/components/header/shared/header-constants';
import type { GymAccessControlResult } from '@/lib/types/gym-access-control';
import { createAction } from '@/lib/actions/core/core';
import { cache } from 'react';

// Types for internal use
type UserRolesFlags = {
  is_member: boolean | null;
  is_trainer: boolean | null;
  is_gym_manager: boolean | null;
  is_company_manager: boolean | null;
  member_gymIds: string[] | null;
  trainer_gymIds: string[] | null;
  gym_manager_gymIds: string[] | null;
  company_gymIds: string[] | null;
  company_id: string | null;
  company_status: string | null;
};


// Helper functions
const createUnauthenticatedResponse = (gymId?: string): HeaderAuthStatus & { gymAccess?: GymAccessControlResult } => ({
  authenticated: false,
  profile: null,
  roleOptions: [],
  companyGyms: [],
  trainerGyms: [],
  gymManagerGyms: [],
  isCompanyManager: false,
  isGymManager: false,
  isTrainer: false,
  isMember: false,
  ...(gymId ? { gymAccess: { hasAccess: false, role: 'none', gymId } } : {}),
});

const mapGymSimple = (gym: { id: string; name: string; slug: string | null; city: string | null; district: string | null }): GymData => ({
  id: gym.id,
  name: gym.name,
  slug: gym.slug || '',
  city: gym.city || '',
  district: gym.district || '',
});

// ---- Small helpers to reduce duplication ----
const normalizeIdArray = (
  flags: any,
  camelKey: 'trainer_gymIds' | 'gym_manager_gymIds' | 'company_gymIds'
): string[] => {
  const camel = flags?.[camelKey];
  const snakeKey = (camelKey as string).replace(/Ids$/, '_ids');
  const snake = flags?.[snakeKey];
  const value = Array.isArray(camel) ? camel : Array.isArray(snake) ? snake : [];
  return Array.isArray(value) ? value.filter(Boolean) : [];
};

const selectGymsByIds = async (supabase: any, ids: string[]): Promise<GymData[]> => {
  if (!ids || ids.length === 0) return [];
  const { data } = await supabase
    .from('gyms')
    .select('id,name,slug,city,district')
    .in('id', ids);
  return (data || []).map(mapGymSimple);
};

const selectCompanyGyms = async (
  supabase: any,
  isCompanyManager: boolean,
  companyIds: string[],
  companyId: string | null
): Promise<GymData[]> => {
  if (!isCompanyManager) return [];
  if (companyIds && companyIds.length > 0) {
    return selectGymsByIds(supabase, companyIds);
  }
  if (companyId) {
    const { data } = await supabase
      .from('gyms')
      .select('id,name,slug,city,district')
      .eq('company_id', companyId);
    return (data || []).map(mapGymSimple);
  }
  return [];
};

const createRoleOptions = (roles: string[]): RoleOption[] => {
  return roles
    .map(role => {
      const roleOption = ROLE_OPTIONS[role];
      return roleOption ? { ...roleOption } : null;
    })
    .filter((option): option is RoleOption => option !== null);
};

const getRestrictedPages = (role: GymAccessControlResult['role']): string[] => {
  switch (role) {
    case 'trainer':
      // Antrenörlerin antrenör yönetimi sayfasına erişimi kapalı olmalı
      return ['analytics', 'trainers', 'staff', 'settings'];
    case 'gym_manager':
    case 'company_manager':
      return [];
    default:
      // Genel kısıtlar: route segmentleri ile uyumlu tut
      return ['analytics', 'trainers', 'staff', 'settings'];
  }
};

const determineGymAccess = (
  gymId: string,
  companyGyms: GymData[],
  gymManagerGyms: GymData[],
  trainerGyms: GymData[]
): GymAccessControlResult => {
  const isCompanyManager = companyGyms.some(gym => gym.id === gymId);
  const isGymManager = gymManagerGyms.some(gym => gym.id === gymId);
  const isTrainer = trainerGyms.some(gym => gym.id === gymId);

  let role: GymAccessControlResult['role'] = 'none';
  if (isCompanyManager) {
    role = 'company_manager';
  } else if (isGymManager) {
    role = 'gym_manager';
  } else if (isTrainer) {
    role = 'trainer';
  }

  return {
    hasAccess: role !== 'none',
    role,
    gymId,
    restrictedPages: getRestrictedPages(role),
  };
};

export const getDashboardContext = cache(async (
  gymId?: string,
): Promise<HeaderAuthStatus & { gymAccess?: GymAccessControlResult }> => {
  try {
    const profileResult = await getProfile();

    // 1) user_roles üzerinden tüm rol ve ilişki verilerini yükle
    const rolesAndGyms = await createAction(async (_, supabase, userId) => {
      // Flags ve ID dizilerini çek
      const { data: flags } = await (supabase as any)
        .from('user_roles')
        .select('*')
        .eq('profile_id', userId)
        .maybeSingle();

      const safeFlags: UserRolesFlags = flags || {
        is_member: false,
        is_trainer: false,
        is_gym_manager: false,
        is_company_manager: false,
        member_gymIds: [],
        trainer_gymIds: [],
        gym_manager_gymIds: [],
        company_gymIds: [],
        company_id: null,
        company_status: null,
      };

      // Roller
      const roles: string[] = [
        safeFlags.is_member && 'member',
        safeFlags.is_trainer && 'trainer',
        safeFlags.is_gym_manager && 'gym_manager',
        safeFlags.is_company_manager && 'company_manager',
      ].filter(Boolean) as string[];

      // İlgili salon listeleri (IDs üzerinden gyms tablosu)
      const trainerIds = normalizeIdArray(flags, 'trainer_gymIds');
      const managerIds = normalizeIdArray(flags, 'gym_manager_gymIds');
      const companyIds = normalizeIdArray(flags, 'company_gymIds');

      const [companyGyms, gymManagerGyms, trainerGyms] = await Promise.all([
        selectCompanyGyms(supabase, !!safeFlags.is_company_manager, companyIds, safeFlags.company_id),
        safeFlags.is_gym_manager ? selectGymsByIds(supabase, managerIds) : Promise.resolve([]),
        safeFlags.is_trainer ? selectGymsByIds(supabase, trainerIds) : Promise.resolve([]),
      ]);

      return { flags: safeFlags, roles, companyGyms, gymManagerGyms, trainerGyms };
    });

    const roles = rolesAndGyms.success ? rolesAndGyms.data!.roles : [];
    const companyGyms = rolesAndGyms.success ? rolesAndGyms.data!.companyGyms : [];
    const gymManagerGyms = rolesAndGyms.success ? rolesAndGyms.data!.gymManagerGyms : [];
    const trainerGyms = rolesAndGyms.success ? rolesAndGyms.data!.trainerGyms : [];

    const isCompanyManager = roles.includes('company_manager');
    const isGymManager = roles.includes('gym_manager');
    const isTrainer = roles.includes('trainer');

    const roleOptions = createRoleOptions(roles);

    // Gym access kontrolü
    const gymAccess = gymId
      ? determineGymAccess(gymId, companyGyms, gymManagerGyms, trainerGyms)
      : undefined;

    return {
      authenticated: true,
      profile: profileResult.success && profileResult.data ? profileResult.data : null,
      roleOptions,
      companyGyms,
      trainerGyms,
      gymManagerGyms,
      isCompanyManager,
      isGymManager,
      isTrainer,
      isMember: roles.includes('member'),
      ...(gymId ? { gymAccess } : {}),
    };
  } catch (error) {
    console.error('Header auth status check failed:', error);
    return createUnauthenticatedResponse(gymId);
  }
});


