'use client';

import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Clock, Users, MoreHorizontal, X, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Appointments,
  AppointmentParticipants,
  GymMemberships,
  Profiles,
  GymMembershipPackages,
  GymPackages,
} from '@/types/database/tables';
import { AppointmentStatus } from '@/lib/supabase/types';
import { useState } from 'react';
import { cancelAppointment, deleteAppointment, updateAppointmentStatus as managerUpdateStatus } from '@/lib/actions/dashboard/company/appointment-actions';
import { updateAppointmentStatus as trainerUpdateStatus } from '@/lib/actions/appointments/appointment-actions';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export interface AppointmentWithDetails extends Appointments {
  trainer_profile?: Profiles;
  participants?: (AppointmentParticipants & {
    membership_package?: GymMembershipPackages & {
      membership?: GymMemberships & {
        profile?: Profiles;
      };
      gym_package?: GymPackages;
    };
  })[];
}

import type { TrainerPermissions } from "@/lib/actions/dashboard/company/trainer-permissions";

interface AppointmentsListProps {
  appointments: AppointmentWithDetails[];
  mode?: 'company_manager' | 'trainer';
  permissions?: TrainerPermissions;
}

const statusConfig: Record<
  AppointmentStatus,
  {
    label: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
    className: string;
    icon: string;
  }
> = {
  scheduled: {
    label: 'Planlandı',
    variant: 'default',
    className: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
    icon: '📅'
  },
  completed: {
    label: 'Tamamlandı',
    variant: 'secondary',
    className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800',
    icon: '✅'
  },
  cancelled: {
    label: 'İptal Edildi',
    variant: 'destructive',
    className: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
    icon: '❌'
  },
};

export function AppointmentsList({
  appointments,
  mode = 'company_manager',
  permissions,
}: AppointmentsListProps) {
  const router = useRouter();
  const params = useParams();
  const gymId = params.gymId as string;
  const [cancellingId, setCancellingId] = useState<string | null>(null);
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const canUpdate =
    mode === 'company_manager' || permissions?.appointments.update === true;

  const handleCancelAppointment = async (appointmentId: string) => {
    setCancellingId(appointmentId);

    try {
      const result = await cancelAppointment(
        appointmentId,
        mode === 'company_manager'
          ? 'Yönetici tarafından iptal edildi'
          : 'Antrenör tarafından iptal edildi'
      );

      if (result.success) {
        toast.success('Randevu başarıyla iptal edildi');
        router.refresh();
      } else {
        toast.error(result.error || 'Randevu iptal edilemedi');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setCancellingId(null);
    }
  };

  const handleDeleteAppointment = async (appointmentId: string) => {
    setDeletingId(appointmentId);

    try {
      const result = await deleteAppointment(appointmentId);

      if (result.success) {
        toast.success('Randevu başarıyla silindi');
        router.refresh();
      } else {
        toast.error(result.error || 'Randevu silinemedi');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setDeletingId(null);
    }
  };

  const handleUpdateStatus = async (
    appointmentId: string,
    status: AppointmentStatus
  ) => {
    setUpdatingId(appointmentId);
    try {
      if (mode === 'company_manager') {
        const fd = new FormData();
        fd.append('appointment_id', appointmentId);
        fd.append('status', status);
        const res = await managerUpdateStatus(fd as any);
        if (!res.success) throw new Error(res.error || 'Durum güncellenemedi');
      } else {
        const res = await trainerUpdateStatus(appointmentId, status);
        if (!res.success) throw new Error(res.error || 'Durum güncellenemedi');
      }
      toast.success('Durum güncellendi');
      router.refresh();
    } catch (e: any) {
      toast.error(e?.message || 'Durum güncellenemedi');
    } finally {
      setUpdatingId(null);
    }
  };

  if (appointments.length === 0) {
    return (
      <div className="py-12 text-center">
        <Calendar className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <h3 className="mb-2 text-lg font-medium">Henüz randevu yok</h3>
        <p className="text-muted-foreground mb-4">
          İlk randevuyu oluşturmak için &quot;Randevu Ekle&quot; butonuna
          tıklayın.
        </p>
        <Button asChild>
          <Link href={`/dashboard/gym/${gymId}/appointments/new`}>
            Randevu Ekle
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {appointments.map(appointment => (
        <div
          key={appointment.id}
          className="hover:bg-muted/50 rounded-lg border p-4 transition-colors"
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 space-y-3">
              {/* Header */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Calendar className="text-muted-foreground h-4 w-4" />
                  <span className="font-medium">
                    {format(
                      new Date(appointment.appointment_date),
                      'dd MMMM yyyy',
                      { locale: tr }
                    )}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground text-sm">
                    {format(new Date(appointment.appointment_date), 'HH:mm')}
                  </span>
                </div>
                <Badge
                  variant="outline"
                  className={`${statusConfig[appointment.status || 'scheduled'].className} font-medium`}
                >
                  <span className="mr-1">
                    {statusConfig[appointment.status || 'scheduled'].icon}
                  </span>
                  {statusConfig[appointment.status || 'scheduled'].label}
                </Badge>
              </div>

              {/* Trainer */}
              {appointment.trainer_profile && (
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={appointment.trainer_profile.avatar_url || undefined}
                    />
                    <AvatarFallback className="text-xs">
                      {appointment.trainer_profile.full_name
                        ?.slice(0, 2)
                        .toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">
                    {appointment.trainer_profile.full_name}
                  </span>
                  <span className="text-muted-foreground text-xs">
                    Antrenör
                  </span>
                </div>
              )}

              {/* Participants */}
              {appointment.participants &&
                appointment.participants.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Users className="text-muted-foreground h-4 w-4" />
                      <span className="text-sm font-medium">
                        Katılımcılar ({appointment.participants.length}/
                        {appointment.max_participants})
                      </span>
                    </div>
                    <div className="space-y-1 pl-6">
                      {appointment.participants.map(participant => (
                        <div
                          key={participant.id}
                          className="flex items-center gap-2 text-sm"
                        >
                          <Avatar className="h-5 w-5">
                            <AvatarImage
                              src={
                                participant.membership_package?.membership
                                  ?.profile?.avatar_url || undefined
                              }
                            />
                            <AvatarFallback className="text-xs">
                              {participant.membership_package?.membership?.profile?.full_name
                                ?.slice(0, 2)
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span>
                            {
                              participant.membership_package?.membership
                                ?.profile?.full_name
                            }
                          </span>
                          <span className="text-muted-foreground text-xs">
                            ({participant.membership_package?.gym_package?.name}
                            )
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              {/* Notes */}
              {appointment.notes && (
                <div className="text-muted-foreground text-sm">
                  <strong>Not:</strong> {appointment.notes}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {/* Durumu Güncelle - izin kontrolü */}
                  {canUpdate && (
                    <>
                      <DropdownMenuItem
                        onClick={() =>
                          handleUpdateStatus(
                            appointment.id,
                            'completed' as AppointmentStatus
                          )
                        }
                        disabled={updatingId === appointment.id}
                      >
                        <span className="mr-2 inline-block h-2 w-2 rounded-full bg-emerald-600" />
                        {updatingId === appointment.id
                          ? 'Güncelleniyor...'
                          : 'Durumu: Tamamlandı'}
                      </DropdownMenuItem>
                    </>
                  )}

                  {appointment.status === 'scheduled' && (
                    <DropdownMenuItem
                      onClick={() => handleCancelAppointment(appointment.id)}
                      disabled={cancellingId === appointment.id}
                      className="text-red-600"
                    >
                      <X className="mr-2 h-4 w-4" />
                      {cancellingId === appointment.id
                        ? 'İptal Ediliyor...'
                        : 'Randevuyu İptal Et'}
                    </DropdownMenuItem>
                  )}

                  {/* Randevu Silme - sadece manager için */}
                  {mode === 'company_manager' && (
                    <DropdownMenuItem
                      onClick={() => handleDeleteAppointment(appointment.id)}
                      disabled={deletingId === appointment.id}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {deletingId === appointment.id
                        ? 'Siliniyor...'
                        : 'Randevuyu Sil'}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
