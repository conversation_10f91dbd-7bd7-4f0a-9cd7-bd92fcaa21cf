'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { getSidebarNavigation, SidebarMode } from '@/lib/constants';

interface MobileNavigationProps {
  mode: SidebarMode;
  gymId?: string;
  onNavigate?: () => void;
}

export function MobileNavigation({
  mode,
  gymId,
  onNavigate,
}: MobileNavigationProps) {
  const pathname = usePathname();
  const navigationItems = getSidebarNavigation(mode, gymId);

  // En spesifik eşleşen path'i bul
  const activeHref =
    navigationItems
      .filter(item => pathname.startsWith(item.href))
      .sort((a, b) => b.href.length - a.href.length)[0]?.href || null;

  const isActive = (href: string) => {
    return activeHref === href;
  };

  return (
    <nav className="space-y-1">
      {navigationItems.map(item => (
        <Link
          key={item.href}
          href={item.href}
          onClick={onNavigate}
          className={cn(
            'flex w-full items-center space-x-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-colors',
            isActive(item.href)
              ? 'bg-primary/10 text-primary'
              : 'text-muted-foreground hover:bg-accent hover:text-foreground'
          )}
        >
          <div className="flex h-5 w-5 items-center justify-center">
            {item.icon}
          </div>
          <span>{item.title}</span>
        </Link>
      ))}
    </nav>
  );
}
