import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Package, TrendingUp, Clock, DollarSign, Users, Calendar } from 'lucide-react';
import { AddPackageButton } from './components/add-package-button';
import { EditPackageButton } from './components/edit-package-button';
import { getGymPackages } from '@/lib/actions/all-actions';

export default async function PackagesPage({
  params,
}: {
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;

  // Paket verilerini al
  const packagesResponse = await getGymPackages(gymId);

  const packages = packagesResponse.success ? packagesResponse.data || [] : [];
  const packagesError = packagesResponse.success
    ? null
    : packagesResponse.error;

  // Helper functions
  const getPackageTypeLabel = (type: string | null) => {
    if (!type) return 'Bilinmiyor';
    switch (type) {
      case 'appointment_standard':
        return 'Randevu - Standart';
      case 'appointment_vip':
        return 'Randevu - VIP';
      case 'daily':
        return 'Günlük Üyelik';
      default:
        return type;
    }
  };

  const getPackageTypeColor = (type: string | null) => {
    if (!type) return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    switch (type) {
      case 'appointment_standard':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'appointment_vip':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'daily':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paketler</h1>
          <p className="text-muted-foreground">Salon paket yönetimi</p>
        </div>
        <AddPackageButton gymId={gymId} />
      </div>

      {/* Salon Bilgileri */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Paket</CardTitle>
            <Package className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{packages.length}</div>
            <p className="text-muted-foreground text-xs">
              {packages.filter(p => p.is_active).length} aktif paket
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Ortalama Fiyat
            </CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {packages.length > 0
                ? Math.round(
                    packages.reduce((sum, p) => sum + p.price, 0) /
                      packages.length
                  ).toLocaleString()
                : 0}{' '}
              ₺
            </div>
            <p className="text-muted-foreground text-xs">
              Tüm paketlerin ortalaması
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Paket Türleri
            </CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {packages.filter(p => p.package_type?.startsWith('appointment')).length}
            </div>
            <p className="text-muted-foreground text-xs">
              {packages.filter(p => p.package_type === 'daily').length} günlük paket
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Paketler Bölümü */}
      <div className="space-y-4">
        {packagesError ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <h3 className="text-destructive mb-2 text-lg font-semibold">
                Paketler Yüklenemedi
              </h3>
              <p className="text-muted-foreground">{packagesError}</p>
            </CardContent>
          </Card>
        ) : packages.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">Henüz Paket Yok</h3>
              <p className="text-muted-foreground mb-4">
                İlk üyelik paketinizi oluşturmak için aşağıdaki butonu kullanın.
              </p>
              <AddPackageButton gymId={gymId}>
                İlk Paketi Oluştur
              </AddPackageButton>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {packages.map(pkg => (
              <Card
                key={pkg.id}
                className={`relative transition-shadow hover:shadow-lg ${!pkg.is_active ? 'opacity-60' : ''}`}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-xl">{pkg.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge
                          className={getPackageTypeColor(pkg.package_type)}
                        >
                          {getPackageTypeLabel(pkg.package_type)}
                        </Badge>
                        {!pkg.is_active && (
                          <Badge variant="secondary">Pasif</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-primary text-3xl font-bold">
                      {pkg.price.toLocaleString()} ₺
                    </div>
                    {pkg.duration_days && (
                      <div className="text-muted-foreground mt-1 flex items-center justify-center gap-1 text-sm">
                        <Clock className="h-4 w-4" />
                        {pkg.duration_days} gün
                      </div>
                    )}
                  </div>

                  {/* Paket Detayları */}
                  <div className="space-y-2 text-sm">
                    {/* Randevu bazlı paketler için detaylar */}
                    {(pkg.package_type === 'appointment_standard' || pkg.package_type === 'appointment_vip') && (
                      <>
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Seans Sayısı:
                          </span>
                          <span className="font-medium">{pkg.session_count || 1}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            Seans Süresi:
                          </span>
                          <span className="font-medium">{pkg.session_duration_minutes || 60} dk</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            Max Katılımcı:
                          </span>
                          <span className="font-medium">{pkg.max_participants || 1}</span>
                        </div>
                      </>
                    )}

                    {/* Günlük paketler için sadece süre */}
                    {pkg.package_type === 'daily' && pkg.duration_days && (
                      <div className="flex items-center justify-center">
                        <span className="text-muted-foreground flex items-center gap-1 text-lg">
                          <Clock className="h-5 w-5" />
                          {pkg.duration_days} gün üyelik
                        </span>
                      </div>
                    )}
                  </div>

                  {pkg.description && (
                    <p className="text-muted-foreground text-center text-sm border-t pt-3">
                      {pkg.description}
                    </p>
                  )}

                  <div className="border-t pt-4">
                    <EditPackageButton gymId={gymId} package={pkg} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
