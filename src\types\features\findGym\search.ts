/**
 * FindGym search types
 *
 * Extracted from src/app/findGym/types/shared.ts
 */

// Search parameters data interface
export interface SearchParamsData {
  q?: string | string[];
  district?: string | string[];
  features?: string | string[];
}

// Gym filtering options
export interface GymFilterOptions {
  query?: string;
  city?: string;
  district?: string;
  features?: string[];
  limit?: number;
  offset?: number;
}

// FindGym search result (feature-specific)
export interface FindGymSearchResult {
  gyms: import('../../business/gym').GymCardData[];
  total: number;
  hasMore: boolean;
}
