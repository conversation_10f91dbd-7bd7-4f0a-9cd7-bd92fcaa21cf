import { PasswordChange } from '@/components/profile-settings/PasswordChange';
import { AccountDeletion } from '@/components/profile-settings/AccountDeletion';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default async function SecuritySettingsPage() {
  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* <PERSON><PERSON> başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">Güvenlik Ayarları</h1>
        <p className="text-muted-foreground">
          Hesabınızın güvenliğini yönetin ve şifrenizi değiştirin
        </p>
      </div>

      {/* Güvenlik ayarları içeriği */}
      <div className="space-y-8">
        {/* <PERSON>ifre Değiştirme */}
        <div className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-lg font-medium"><PERSON><PERSON><PERSON></h3>
            <p className="text-muted-foreground text-sm">
              Hesabınızın güvenliği için düzenli olarak şifrenizi değiştirin
            </p>
          </div>
          <PasswordChange />
        </div>

        {/* Hesap Silme */}
        <div className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-lg font-medium">Hesap Silme</h3>
            <p className="text-muted-foreground text-sm">
              Hesabınızı kalıcı olarak silmek istiyorsanız aşağıdaki işlemi gerçekleştirin
            </p>
          </div>
          <AccountDeletion />
        </div>
      </div>
    </div>
  );
}
