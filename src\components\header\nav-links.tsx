import Link from 'next/link';

interface NavLinksProps {
  className?: string;
}

export function NavLinks({ className }: NavLinksProps) {
  return (
    <nav className={className}>
      <Link
        href="/findGym"
        className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors"
      >
        Salon Bul
      </Link>
      <Link
        href="/features"
        className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors"
      >
        Özellikler
      </Link>
      <Link
        href="/pricing"
        className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors"
      >
        Fiyatlandırma
      </Link>
      <Link
        href="/about"
        className="text-muted-foreground hover:text-primary text-sm font-medium transition-colors"
      >
        Hakkımızda
      </Link>
    </nav>
  );
}
