/**
 * Shared constants across all action modules
 * Following Clean Code principles - centralized constants to avoid magic numbers and strings
 */

// ============================================================================
// COMMON VALIDATION RULES
// ============================================================================

export const VALIDATION_RULES = {
  // Password rules
  PASSWORD_MIN_LENGTH: 6,

  // Name rules
  FULL_NAME_MIN_LENGTH: 2,
  FULL_NAME_MAX_LENGTH: 100,

  // Age rules
  AGE_MIN: 13,
  AGE_MAX: 120,

  // Phone rules
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15,

  // Onboarding specific rules
  ONBOARDING_AGE_MIN: 16,
  HEIGHT_MIN: 100,
  HEIGHT_MAX: 250,
  WEIGHT_MIN: 30,
  WEIGHT_MAX: 300,
  EXPERIENCE_MAX: 50,
  BIO_MAX_LENGTH: 500,
} as const;

// ============================================================================
// COMMON ERROR MESSAGES
// ============================================================================

export const COMMON_ERROR_MESSAGES = {
  // Generic errors
  UNKNOWN_ERROR: 'Bilinmeyen bir hata oluştu',
  UNEXPECTED_ERROR: 'Beklenmeyen bir hata oluştu',
  INTERNAL_ERROR: 'İç sistem hatası',
  OPERATION_FAILED: 'İşlem başarısız',

  // Validation errors
  REQUIRED_FIELD: 'Bu alan zorunludur',
  INVALID_FORMAT: 'Geçersiz format',
  INVALID_DATA: 'Geçersiz veri',

  // Auth errors
  LOGIN_REQUIRED: 'Bu işlem için giriş yapmanız gerekiyor',
  INSUFFICIENT_PERMISSIONS: 'Bu işlem için yetkiniz yok',
  ACCESS_DENIED: 'Erişim reddedildi',

  // Database errors
  RECORD_NOT_FOUND: 'Kayıt bulunamadı',
  RECORD_ALREADY_EXISTS: 'Bu kayıt zaten mevcut',
  DATABASE_ERROR: 'Veritabanı hatası',
} as const;

// ============================================================================
// COMMON SUCCESS MESSAGES
// ============================================================================

export const COMMON_SUCCESS_MESSAGES = {
  OPERATION_SUCCESS: 'İşlem başarılı',
  CREATED_SUCCESS: 'Başarıyla oluşturuldu',
  UPDATED_SUCCESS: 'Başarıyla güncellendi',
  DELETED_SUCCESS: 'Başarıyla silindi',
  SAVED_SUCCESS: 'Başarıyla kaydedildi',
} as const;

// ============================================================================
// COMMON PATTERNS
// ============================================================================

export const COMMON_PATTERNS = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^[\+]?[1-9][\d]{0,15}$/,
  UUID_REGEX:
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
} as const;

// ============================================================================
// COMMON DEFAULTS
// ============================================================================

export const COMMON_DEFAULTS = {
  EMPTY_STRING: '',
  NULL_VALUE: null,
  ZERO: 0,
  FALSE: false,
  TRUE: true,
} as const;

// ============================================================================
// PAGINATION CONSTANTS
// ============================================================================

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 1,
  DEFAULT_PAGE: 1,
} as const;

// ============================================================================
// TIME CONSTANTS
// ============================================================================

export const TIME_CONSTANTS = {
  MILLISECONDS_IN_SECOND: 1000,
  SECONDS_IN_MINUTE: 60,
  MINUTES_IN_HOUR: 60,
  HOURS_IN_DAY: 24,
  DAYS_IN_WEEK: 7,
  DAYS_IN_MONTH: 30,
  DAYS_IN_YEAR: 365,
} as const;

// ============================================================================
// STATUS CONSTANTS
// ============================================================================

export const STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  EXPIRED: 'expired',
} as const;

// ============================================================================
// GENDER CONSTANTS
// ============================================================================

export const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other',
  PREFER_NOT_TO_SAY: 'prefer_not_to_say',
} as const;

// ============================================================================
// ROLE CONSTANTS
// ============================================================================

export const ROLES = {
  ADMIN: 'admin',
  MANAGER: 'company_manager',
  TRAINER: 'trainer',
  MEMBER: 'member',
  GUEST: 'guest',
} as const;
