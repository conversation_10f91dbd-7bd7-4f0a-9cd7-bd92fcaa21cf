'use client';

import { useTransition } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Activity, Pause } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { updateMembershipStatus } from '@/lib/actions/all-actions';

interface MembershipActionsProps {
  membershipId: string;
  currentStatus: 'active' | 'passive';
}

export function MembershipActions({
  membershipId,
  currentStatus,
}: MembershipActionsProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleStatusChange = (newStatus: 'active' | 'passive') => {
    startTransition(async () => {
      try {
        const result = await updateMembershipStatus(membershipId, newStatus);

        if (result.success) {
          toast.success(result.data?.message || 'Üyelik durumu güncellendi');
          router.refresh(); // Sayfayı yenile
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      } catch (error) {
        toast.error('Bir hata oluştu');
        console.error('Membership status update error:', error);
      }
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={isPending}
          className="h-8 w-8 p-0"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Menüyü aç</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {currentStatus === 'passive' ? (
          <DropdownMenuItem
            onClick={() => handleStatusChange('active')}
            disabled={isPending}
          >
            <Activity className="mr-2 h-4 w-4" />
            Üyeliği Aktifleştir
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem
            onClick={() => handleStatusChange('passive')}
            disabled={isPending}
          >
            <Pause className="mr-2 h-4 w-4" />
            Üyeliği Pasifleştir
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
