import { AnimatedSection } from '@/components/ui/animated-section'
import { Star } from 'lucide-react'

export function PremiumLogosMarquee() {
  const brands = [
    'FitClub',
    'GymPro',
    'Athletica',
    'PowerZone',
    'Stronger',
    'MoveWell',
    'Flex<PERSON>ab',
    'BodyForge',
  ]
  return (
    <section className="py-16 md:py-20 bg-gradient-to-r from-muted/30 via-background to-muted/30">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto mb-12 max-w-3xl text-center">
            <div className="mb-4 inline-flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-4 py-2 text-sm font-medium text-primary backdrop-blur-sm">
              <Star className="h-4 w-4" />
              <span>Güvenilir Ortaklar</span>
            </div>
            <h3 className="text-balance text-3xl font-bold md:text-4xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              Güvenilir Markalarla Yan Yana
            </h3>
            <p className="mt-4 text-lg text-muted-foreground">
              Türkiye&apos;nin önde gelen spor salonları Sportiva&apos;yı tercih ediyor
            </p>
          </div>
        </AnimatedSection>
        <AnimatedSection animation="fade-up" delay={150}>
          <h2 id="logos-heading" className="sr-only">
            Güvenen markalar
          </h2>
          <div className="relative overflow-hidden [mask-image:linear-gradient(to_right,transparent,black_10%,black_90%,transparent)]">
            <div className="flex w-max animate-scroll gap-16 whitespace-nowrap">
              {[...brands, ...brands].map((brand, i) => (
                <div
                  key={`${brand}-${i}`}
                  className="flex items-center justify-center rounded-xl border border-border/50 bg-gradient-to-br from-background/80 to-background/60 px-8 py-4 shadow-sm backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-md"
                >
                  <span className="text-xl font-bold tracking-wide text-foreground/80 hover:text-primary transition-colors">
                    {brand}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  )
}
