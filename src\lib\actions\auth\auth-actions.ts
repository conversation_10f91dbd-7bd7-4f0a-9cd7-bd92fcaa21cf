'use server';

import { createClient } from '@/lib/supabase/server';
import { translateAuthErrorSimple } from '@/lib/utils/auth-errors';
import { createAction } from '../core/core';
import { validateAuthFormData } from '@/lib/utils/error-types';
import { ApiResponse } from '@/types/global/api';
import {
  validatePhoneNumber,
  formatPhoneNumber as formatPhone,
} from '@/lib/utils/phone-utils';
import { redirect } from 'next/navigation';

// Import utilities and constants
import {
  validateEmailSignUpData,
  validatePhoneSignUpData,
  isPhoneRegistered,
  findUserByPhone,
  validateEmail,
  validatePasswordConfirmation,
} from './auth-utils';
import { createUserProfile } from '@/lib/actions/user/user-creation-utils';

import {
  AUTH_ERROR_MESSAGES,
  AUTH_SUCCESS_MESSAGES,
  AUTH_CONFIG,
} from './auth-constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface AuthResponse {
  success: boolean;
  error: string | null;
  message: string;
}

// ============================================================================
// EMAIL AUTH FUNCTIONS
// ============================================================================

/**
 * Email signup with improved validation and error handling
 * Following Clean Code principles - single responsibility, clear error handling
 */
export async function signUpWithEmail(
  formData: FormData
): Promise<{ email: string; success: boolean; error?: string }> {
  try {
    const supabase = await createClient();

    // Validate input data using utility function
    const { email, password } = validateEmailSignUpData(formData);

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: AUTH_CONFIG.EMAIL_REDIRECT_TO,
      },
    });

    if (error) {
      return {
        email,
        success: false,
        error: translateAuthErrorSimple(error.message),
      };
    }

    return {
      email,
      success: true,
    };
  } catch (error) {
    const email = (formData.get('email') ||
      formData.get('identifier')) as string;
    return {
      email: email || '',
      success: false,
      error:
        error instanceof Error
          ? error.message
          : AUTH_ERROR_MESSAGES.REGISTRATION_FAILED,
    };
  }
}

/**
 * Email signin with improved error handling
 * Following Clean Code principles - focused responsibility
 */
export async function signInWithEmail(
  formData: FormData
): Promise<ApiResponse<AuthResponse>> {
  return createAction<AuthResponse>(
    async (_, supabase) => {
      const { email, password } = validateAuthFormData(formData);

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(translateAuthErrorSimple(error.message));
      }

      return {
        success: true,
        error: null,
        message: AUTH_SUCCESS_MESSAGES.LOGIN_SUCCESS,
      };
    },
    { requireAuth: false }
  );
}

// ============================================================================
// EMAIL OTP FUNCTIONS
// ============================================================================

/**
 * Email OTP verification with improved validation and profile creation
 * Following Clean Code principles - clear validation, single responsibility
 */
export async function verifyEmailOTP(
  formData: FormData
): Promise<ApiResponse<{ success: boolean }>> {
  return createAction<{ success: boolean }>(
    async (_, supabase) => {
      const email = formData.get('email') as string;
      const token = formData.get('token') as string;

      if (!email || !token) {
        throw new Error(AUTH_ERROR_MESSAGES.EMAIL_AND_TOKEN_REQUIRED);
      }

      // Verify OTP
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      });

      if (error) {
        throw new Error(translateAuthErrorSimple(error.message));
      }

      if (!data.user) {
        throw new Error(AUTH_ERROR_MESSAGES.EMAIL_VERIFICATION_FAILED);
      }

      // Create profile using centralized function
      await createUserProfile({
        userId: data.user.id,
        email: data.user.email,
      });

      return { success: true };
    },
    { requireAuth: false }
  );
}

/**
 * Resend email OTP with improved validation
 * Following Clean Code principles - clear validation and error messages
 */
export async function resendEmailOTP(
  formData: FormData
): Promise<ApiResponse<{ message: string }>> {
  return createAction<{ message: string }>(
    async (_, supabase) => {
      const email = formData.get('email') as string;

      if (!email) {
        throw new Error(AUTH_ERROR_MESSAGES.EMAIL_REQUIRED);
      }

      // Send email verification code
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
      });

      if (error) {
        throw new Error(translateAuthErrorSimple(error.message));
      }

      return {
        message: AUTH_SUCCESS_MESSAGES.OTP_RESENT,
      };
    },
    { requireAuth: false }
  );
}

// ============================================================================
// PHONE AUTH FUNCTIONS
// ============================================================================

/**
 * Phone registration with improved validation and error handling
 * Following Clean Code principles - using utility functions for validation
 */
export async function registerWithPhone(
  formData: FormData
): Promise<ApiResponse<{ message: string; requiresOtp: boolean }>> {
  return createAction<{ message: string; requiresOtp: boolean }>(
    async (_, supabase) => {
      // Validate input data using utility function
      const { cleanPhone, formattedPhone } = validatePhoneSignUpData(formData);

      // Check if phone number is already registered
      const isRegistered = await isPhoneRegistered(cleanPhone);
      if (isRegistered) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_ALREADY_REGISTERED);
      }

      // Register with phone number (OTP will be sent, but user not created yet)
      const { error: authError } = await supabase.auth.signUp({
        phone: formattedPhone,
        password: validatePhoneSignUpData(formData).password,
        options: {
          channel: AUTH_CONFIG.SMS_CHANNEL,
        },
      });

      if (authError) {
        throw new Error(translateAuthErrorSimple(authError.message));
      }

      // SignUp successful, OTP sent
      // Profile creation will be done in verifyPhoneOtp
      return {
        message: AUTH_SUCCESS_MESSAGES.REGISTRATION_SUCCESS,
        requiresOtp: true, // Telefon kayıt her zaman OTP gerektirir
      };
    },
    { requireAuth: false }
  );
}

/**
 * Phone signin with password - improved validation and user lookup
 * Following Clean Code principles - using utility functions
 */
export async function signInWithPhonePassword(
  formData: FormData
): Promise<ApiResponse<AuthResponse>> {
  return createAction<AuthResponse>(
    async (_, supabase) => {
      const phone = formData.get('phone') as string;
      const password = formData.get('password') as string;

      // Validation
      if (!phone || !password) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_AND_PASSWORD_REQUIRED);
      }

      const phoneValidation = validatePhoneNumber(phone);
      if (!phoneValidation.isValid) {
        throw new Error(phoneValidation.error!);
      }
      const { clean } = formatPhone(phone);

      // Find user by phone number using utility function
      const profile = await findUserByPhone(clean);
      if (!profile) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_NOT_FOUND);
      }

      // Sign in with phone number
      const { error } = await supabase.auth.signInWithPassword({
        phone: clean,
        password,
      });

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          throw new Error(AUTH_ERROR_MESSAGES.PHONE_OR_PASSWORD_INCORRECT);
        }
        throw new Error(translateAuthErrorSimple(error.message));
      }

      return {
        success: true,
        error: null,
        message: AUTH_SUCCESS_MESSAGES.LOGIN_SUCCESS,
      };
    },
    { requireAuth: false }
  );
}

/**
 * Send phone OTP for signin - improved validation and user lookup
 * Following Clean Code principles - using utility functions
 */
export async function sendPhoneOtp(
  formData: FormData
): Promise<ApiResponse<{ message: string }>> {
  return createAction<{ message: string }>(
    async (_, supabase) => {
      const phone = formData.get('phone') as string;

      if (!phone) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_REQUIRED);
      }

      const phoneValidation = validatePhoneNumber(phone);
      if (!phoneValidation.isValid) {
        throw new Error(phoneValidation.error!);
      }
      const { clean } = formatPhone(phone);

      // Check if user is registered using utility function
      const isRegistered = await isPhoneRegistered(clean);
      if (!isRegistered) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_NOT_FOUND);
      }

      // Send OTP
      const { error } = await supabase.auth.signInWithOtp({
        phone: clean,
      });

      if (error) {
        throw new Error(translateAuthErrorSimple(error.message));
      }

      return {
        message: AUTH_SUCCESS_MESSAGES.OTP_SENT,
      };
    },
    { requireAuth: false }
  );
}

/**
 * Phone OTP verification with improved validation and profile creation
 * Following Clean Code principles - clear validation, using utility functions
 */
export async function verifyPhoneOtp(
  formData: FormData
): Promise<ApiResponse<{ userId: string; isNewUser: boolean }>> {
  return createAction<{ userId: string; isNewUser: boolean }>(
    async (_, supabase) => {
      const phone = formData.get('phone') as string;
      const token = formData.get('token') as string;

      if (!phone || !token) {
        throw new Error(AUTH_ERROR_MESSAGES.PHONE_AND_TOKEN_REQUIRED);
      }

      const phoneValidation = validatePhoneNumber(phone);
      if (!phoneValidation.isValid) {
        throw new Error(phoneValidation.error!);
      }
      const { clean, formatted } = formatPhone(phone);

      // Verify OTP - use formatted phone
      const { data, error } = await supabase.auth.verifyOtp({
        phone: formatted,
        token,
        type: 'sms',
      });

      if (error) {
        throw new Error(translateAuthErrorSimple(error.message));
      }

      if (!data.user) {
        throw new Error(AUTH_ERROR_MESSAGES.USER_CREATION_FAILED);
      }

      // Create profile using centralized function
      const profileResult = await createUserProfile({
        userId: data.user.id,
        phoneNumber: clean,
      });

      return {
        userId: data.user.id,
        isNewUser: profileResult.isNewProfile,
      };
    },
    { requireAuth: false }
  );
}

// ============================================================================
// PASSWORD RESET FUNCTIONS
// ============================================================================

/**
 * Send password reset email with improved validation
 * Following Clean Code principles - using utility functions for validation
 */
export async function resetPassword(
  formData: FormData
): Promise<{ success: boolean; error?: string; message?: string }> {
  try {
    const supabase = await createClient();
    const email = formData.get('email') as string;

    // Validate email using utility function
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      return {
        success: false,
        error: emailValidation.error,
      };
    }

    // Send password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,
    });

    if (error) {
      return {
        success: false,
        error: translateAuthErrorSimple(error.message),
      };
    }

    return {
      success: true,
      message: AUTH_SUCCESS_MESSAGES.PASSWORD_RESET_SENT,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : AUTH_ERROR_MESSAGES.PASSWORD_RESET_FAILED,
    };
  }
}

/**
 * Confirm password reset with improved validation
 * Following Clean Code principles - using utility functions for validation
 */
export async function confirmPasswordReset(
  formData: FormData
): Promise<{ success: boolean; error?: string; message?: string }> {
  try {
    const supabase = await createClient();
    const password = formData.get('password') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Validate password confirmation using utility function
    const passwordValidation = validatePasswordConfirmation(
      password,
      confirmPassword
    );
    if (!passwordValidation.isValid) {
      return {
        success: false,
        error: passwordValidation.error,
      };
    }

    // Check if user session is valid
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        error: AUTH_ERROR_MESSAGES.INVALID_RESET_SESSION,
      };
    }

    // Update password
    const { error: updateError } = await supabase.auth.updateUser({
      password: password,
    });

    if (updateError) {
      return {
        success: false,
        error: translateAuthErrorSimple(updateError.message),
      };
    }

    return {
      success: true,
      message: AUTH_SUCCESS_MESSAGES.PASSWORD_UPDATED,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : AUTH_ERROR_MESSAGES.PASSWORD_UPDATE_FAILED,
    };
  }
}

export async function getUserRole() {
  const supabase = await createClient();

  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return null;
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return null;
    }

    return profile.role;
  } catch (error) {
    console.error('Get user role error:', error);
    return null;
  }
}

export async function logoutAction() {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw new Error(error.message);
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }

  redirect('/auth/login');
}
