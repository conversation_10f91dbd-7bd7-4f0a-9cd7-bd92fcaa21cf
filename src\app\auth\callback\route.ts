import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { createUserProfile } from '@/lib/actions/user/user-creation-utils';

/**
 * Supabase auth error kodlarını kullanıcı dostu Türkçe mesajlara çevirir
 */
function translateAuthCallbackError(
  error?: string | null,
  errorCode?: string | null,
  errorDescription?: string | null
): string {
  // Önce error_code'a göre kontrol et
  if (errorCode) {
    switch (errorCode) {
      case 'otp_expired':
        return 'E-posta doğrulama linki süresi dolmuş. Lütfen yeni bir doğrulama e-postası isteyin.';
      case 'email_not_confirmed':
        return 'E-posta adresiniz henüz doğrulanmamış. Lütfen e-posta kutunuzu kontrol edin.';
      case 'invalid_request':
        return 'Geçersiz doğrulama isteği. Lütfen tekrar kayıt olmayı deneyin.';
      case 'signup_disabled':
        return 'Kayıt işlemi şu anda devre dışı. Lütfen daha sonra tekrar deneyin.';
      default:
        break;
    }
  }

  // Sonra error'a göre kontrol et
  if (error) {
    switch (error) {
      case 'access_denied':
        return 'E-posta doğrulama reddedildi. Link geçersiz veya süresi dolmuş olabilir.';
      case 'invalid_request':
        return 'Geçersiz doğrulama isteği. Lütfen doğrulama linkini tekrar kontrol edin.';
      case 'server_error':
        return 'Sunucu hatası oluştu. Lütfen birkaç dakika sonra tekrar deneyin.';
      default:
        break;
    }
  }

  // Error description varsa onu da kontrol et
  if (errorDescription) {
    const desc = errorDescription.toLowerCase();
    if (desc.includes('expired')) {
      return 'Doğrulama linki süresi dolmuş. Lütfen yeni bir doğrulama e-postası isteyin.';
    }
    if (desc.includes('invalid')) {
      return 'Doğrulama linki geçersiz. Lütfen e-posta kutunuzdaki en son linki kullanın.';
    }
    if (desc.includes('already')) {
      return 'E-posta adresi zaten doğrulanmış. Giriş yapmayı deneyin.';
    }
  }

  // Varsayılan mesaj
  return 'E-posta doğrulama başarısız. Lütfen yeni bir doğrulama e-postası isteyin.';
}

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorCode = searchParams.get('error_code');
  const errorDescription = searchParams.get('error_description');
  const next = searchParams.get('next');

  // Eğer error parametreleri varsa, bunları handle et
  if (error || errorCode || errorDescription) {
    const errorMessage = translateAuthCallbackError(
      error,
      errorCode,
      errorDescription
    );
    console.error('Auth callback error:', {
      error,
      errorCode,
      errorDescription,
    });

    return NextResponse.redirect(
      `${origin}/auth/login?error=${encodeURIComponent(errorMessage)}&context=email_verification`
    );
  }

  if (code) {
    const supabase = await createClient();

    try {
      const { data, error: exchangeError } =
        await supabase.auth.exchangeCodeForSession(code);

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError);
        const errorMessage = translateAuthCallbackError(
          exchangeError.message,
          null,
          'Code exchange failed'
        );
        return NextResponse.redirect(
          `${origin}/auth/login?error=${encodeURIComponent(errorMessage)}&context=code_exchange`
        );
      }

      if (data.user) {
        // Create profile using centralized function
        await createUserProfile({
          userId: data.user.id,
          email: data.user.email,
          fullName: data.user.user_metadata?.full_name ||
            data.user.user_metadata?.name,
          avatarUrl: data.user.user_metadata?.avatar_url ||
            data.user.user_metadata?.picture,
        });


        // Yönlendirme kararı
        const redirectPath = next ?? '/dashboard';
        // Başarılı doğrulama - belirlenen path'e yönlendir
        return NextResponse.redirect(`${origin}${redirectPath}`);
      }
    } catch (error) {
      console.error('Auth callback unexpected error:', error);
      return NextResponse.redirect(
        `${origin}/auth/login?error=${encodeURIComponent('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.')}&context=unexpected_error`
      );
    }
  }

  // Code yoksa ama error da yoksa - geçersiz istek
  return NextResponse.redirect(
    `${origin}/auth/login?error=${encodeURIComponent('Geçersiz doğrulama isteği. Lütfen tekrar kayıt olmayı deneyin.')}&context=invalid_request`
  );
}
