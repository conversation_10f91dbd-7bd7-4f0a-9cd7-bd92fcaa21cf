'use client';

import { useState } from 'react';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageUpload } from '@/components/ui/image-upload';
import type { ManagerData } from '@/types/onboarding';
import { INPUT_RULES } from '@/lib/utils/form-validation';
import { uploadCompanyLogo } from '@/lib/actions/all-actions';
import { toast } from 'sonner';
import { Image } from 'lucide-react';
import { SocialLinksForm } from '@/components/forms/SocialLinksForm';

interface CompanyFormProps {
  data: ManagerData;
  onChange: (data: ManagerData) => void;
}

export function CompanyForm({ data, onChange }: CompanyFormProps) {
  const [logoUploading, setLogoUploading] = useState(false);

  const handleInputChange = (field: keyof ManagerData, value: string) => {
    onChange({
      ...data,
      [field]: value,
    });
  };

  const handleLogoUpload = async (file: File) => {
    setLogoUploading(true);
    try {
      const result = await uploadCompanyLogo(file);
      if (result.success && result.url) {
        handleInputChange('logoUrl', result.url);
        toast.success('Logo başarıyla yüklendi!');
      } else {
        toast.error(result.error || 'Logo yüklenirken hata oluştu');
      }
    } catch (error) {
      console.error('Logo upload error:', error);
      toast.error('Logo yüklenirken beklenmeyen hata oluştu');
    } finally {
      setLogoUploading(false);
    }
  };

  const handleLogoRemove = () => {
    handleInputChange('logoUrl', '');
    toast.success('Logo kaldırıldı');
  };

  const handleSocialLinksChange = (links: Record<string, string>) => {
    onChange({
      ...data,
      socialLinks: links,
    });
  };

  return (
    <div className="space-y-6">
      {/* Company Information */}
      <Card>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="companyName" className="text-sm font-medium">
              Şirket Adı *
            </Label>
            <EnhancedInput
              id="companyName"
              type="text"
              value={data.companyName}
              onChange={value => handleInputChange('companyName', value)}
              placeholder="Örn: Sportiva Fitness Center"
              formatter={INPUT_RULES.COMPANY_NAME.formatter}
              validator={INPUT_RULES.COMPANY_NAME.validator}
              maxLength={INPUT_RULES.COMPANY_NAME.maxLength}
              validationMessage="Şirket adı en az 2 karakter olmalıdır"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyPhone" className="text-sm font-medium">
              Şirket Telefonu
            </Label>
            <EnhancedInput
              id="companyPhone"
              type="tel"
              value={data.companyPhone}
              onChange={value => handleInputChange('companyPhone', value)}
              placeholder="5551234567"
              formatter={INPUT_RULES.PHONE.formatter}
              validator={INPUT_RULES.PHONE.validator}
              maxLength={INPUT_RULES.PHONE.maxLength}
              validationMessage="Geçerli bir telefon numarası giriniz (10-11 haneli)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyEmail" className="text-sm font-medium">
              Şirket E-postası
            </Label>
            <EnhancedInput
              id="companyEmail"
              type="email"
              value={data.companyEmail}
              onChange={value => handleInputChange('companyEmail', value)}
              placeholder="<EMAIL>"
              validator={INPUT_RULES.EMAIL.validator}
              maxLength={INPUT_RULES.EMAIL.maxLength}
              validationMessage="Geçerli bir e-posta adresi giriniz"
            />
          </div>
        </CardContent>
      </Card>

      {/* Logo Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {/* eslint-disable-next-line jsx-a11y/alt-text */}
            <Image className="text-primary h-5 w-5" />
            Şirket Logosu
          </CardTitle>
          <CardContent>
            <div className="space-y-2">
              <Label>Logo (Opsiyonel)</Label>
              <div className="mx-auto w-full max-w-xs">
                <ImageUpload
                  currentImageUrl={data.logoUrl}
                  onImageUpload={handleLogoUpload}
                  onImageRemove={handleLogoRemove}
                  isUploading={logoUploading}
                  disabled={logoUploading}
                  aspectRatio="square"
                  maxSizeMB={2}
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
                  uploadText="Logo Yükle"
                />
              </div>
              <p className="text-muted-foreground text-center text-xs">
                Önerilen boyut: 200x200px, Max: 2MB
              </p>
            </div>
          </CardContent>
        </CardHeader>
      </Card>

      {/* Social Links */}
      <SocialLinksForm
        initialData={data.socialLinks || {}}
        onChange={handleSocialLinksChange}
        disabled={logoUploading}
      />
    </div>
  );
}
