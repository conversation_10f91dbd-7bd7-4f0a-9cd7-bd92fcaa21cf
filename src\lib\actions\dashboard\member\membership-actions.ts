'use server';

import { ApiResponse } from '@/types/global/api';
import { GymPackages } from '@/types/database/tables';
import {
  createAction,
  createAdminAction,
} from '../../core';

// Import utilities and constants
import {
  validateUserId,
  validateGymId,
  createMembershipRecordData,
  transformGymData,
  handleMembershipDatabaseError,
} from './membership-utils';

import {
  MEMBERSHIP_ERROR_MESSAGES,
  MEMBERSHIP_SUCCESS_MESSAGES,
} from './membership-constants';
import { createLimitReachedError } from '@/lib/utils/error-types';

// Limit enforcement helpers (admin-only)
interface PlatformPackage {
  max_members: number | null;
}

async function getGymLimitsForMembers(
  adminClient: any,
  gymId: string
): Promise<{ maxMembers: number | null }> {
  // 1) Gym -> company_id
  const { data: gym, error: gymError } = await adminClient
    .from('gyms')
    .select('id, company_id')
    .eq('id', gymId)
    .single();

  if (gymError || !gym) {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED);
  }

  // 2) Company -> platform_package_id
  const { data: company, error: companyError } = await adminClient
    .from('companies')
    .select('id, platform_package_id')
    .eq('id', gym.company_id)
    .single();

  if (companyError || !company) {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED);
  }

  if (!company.platform_package_id) {
    return { maxMembers: null };
  }

  // 3) Platform package limits
  const { data: pkg, error: pkgError } = await adminClient
    .from('platform_packages')
    .select('max_members')
    .eq('id', company.platform_package_id)
    .single();

  if (pkgError || !pkg) {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED);
  }

  const platformPackage = pkg as PlatformPackage;
  return { maxMembers: platformPackage.max_members ?? null };
}

async function countActiveMembers(adminClient: any, gymId: string): Promise<number> {
  const { count, error } = await adminClient
    .from('gym_memberships')
    .select('id', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .eq('status', 'active');

  if (error) {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED);
  }

  return count ?? 0;
}

export async function enforceGymMemberLimitForAdmin(
  adminClient: any,
  gymId: string
): Promise<void> {
  const limits = await getGymLimitsForMembers(adminClient, gymId);
  if (limits.maxMembers != null) {
    const active = await countActiveMembers(adminClient, gymId);
    if (active >= limits.maxMembers) {
      throw createLimitReachedError('Üye limiti dolu', {
        role: 'member',
        gymId,
        current: active,
        max: limits.maxMembers,
      });
    }
  }
}

/**
 * Common gym membership creation helper
 * Following Clean Code principles - improved validation and error handling
 */
export async function createGymMembershipRecord(
  supabase: any,
  userId: string,
  gymId: string
): Promise<void> {
  try {
    // Create membership record data using utility function
    const membershipData = createMembershipRecordData(userId, gymId);

    const { error: membershipError } = await supabase
      .from('gym_memberships')
      .insert(membershipData);

    if (membershipError) {
      handleMembershipDatabaseError(membershipError, 'create membership');
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED);
  }
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

/**
 * Member dashboard statistics
 * Following Clean Code principles - clear interface definition
 */
export interface MemberDashboardStats {
  totalMemberships: number;
  activeMemberships: number;
  passiveMemberships: number;
  totalSpent: number;
}

/**
 * Member membership information
 * Following Clean Code principles - well-defined data structure
 */
export interface MemberMembership {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string;
    phone: string;
    slug?: string;
  };
  status: 'active' | 'passive';
  approved_at: string | null;
  request_date: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Member membership with package information
 * Following Clean Code principles - extends base interface clearly
 */
export interface MemberMembershipWithPackages extends MemberMembership {
  packages: (GymPackages & {
    packageName: string;
    packageType: string | null;
    isActive: boolean | null;
    isExpired: boolean;
    purchasePrice: number;
    startDate: string;
    endDate: string | null;
  })[];
}

/**
 * Member package information
 * Following Clean Code principles - comprehensive package data structure
 */
export interface MemberPackageInfo {
  id: string;
  name: string;
  package_type: string;
  duration_days: number | null;
  price: number;
  start_date: string;
  end_date: string;
  purchase_price: number;
  status: string;
}

// ============================================================================
// ACTION FUNCTIONS
// ============================================================================

/**
 * Get member dashboard statistics
 * Following Clean Code principles - focused responsibility, clear error handling
 */
export async function getMemberDashboardStats(): Promise<
  ApiResponse<MemberDashboardStats>
> {
  return await createAction<MemberDashboardStats>(
    async (_, supabase, userId) => {
      try {
        // Parallel queries for better performance
        const [total, active, passive] = await Promise.all([
          supabase
            .from('gym_memberships')
            .select('id', { count: 'exact', head: true })
            .eq('profile_id', userId),
          supabase
            .from('gym_memberships')
            .select('id', { count: 'exact', head: true })
            .eq('profile_id', userId)
            .eq('status', 'active'),
          supabase
            .from('gym_memberships')
            .select('id', { count: 'exact', head: true })
            .eq('profile_id', userId)
            .eq('status', 'passive'),
        ]);

        // Check for errors in parallel queries
        if (total.error) throw new Error(total.error.message);
        if (active.error) throw new Error(active.error.message);
        if (passive.error) throw new Error(passive.error.message);

        // Get user memberships for spending calculation
        const { data: userMemberships, error: membershipError } = await supabase
          .from('gym_memberships')
          .select('id')
          .eq('profile_id', userId);

        if (membershipError) {
          throw new Error(membershipError.message);
        }

        const membershipIds = userMemberships?.map((m: any) => m.id) || [];

        // Calculate total spent using utility function
        let totalSpent = 0;
        if (membershipIds.length > 0) {
          const { data: packagesData, error: packagesError } = await supabase
            .from('gym_membership_packages')
            .select('purchase_price')
            .in('membership_id', membershipIds);

          if (packagesError) {
            throw new Error(packagesError.message);
          }

          totalSpent =
            packagesData?.reduce(
              (sum, p) => sum + (p.purchase_price || 0),
              0
            ) || 0;
        }

        return {
          totalMemberships: total.count || 0,
          activeMemberships: active.count || 0,
          passiveMemberships: passive.count || 0,
          totalSpent,
        };
      } catch (error) {
        throw new Error(
          error instanceof Error
            ? error.message
            : MEMBERSHIP_ERROR_MESSAGES.STATS_FETCH_FAILED
        );
      }
    }
  );
}

/**
 * Get member memberships
 * Following Clean Code principles - focused responsibility, clear error handling
 */
export async function getMemberMemberships(): Promise<
  ApiResponse<MemberMembership[]>
> {
  return await createAction<MemberMembership[]>(async (_, supabase, userId) => {
    try {
      const { data, error } = await supabase
        .from('gym_memberships')
        .select(
          `
            id,
            status,
            approved_at,
            request_date,
            created_at,
            updated_at,
            gym:gyms(id,name,address,gym_phone,slug)
          `
        )
        .eq('profile_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(
          `${MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND}: ${error.message}`
        );
      }

      return (data || []).map((m: any) => ({
        id: m.id,
        gym: transformGymData(m.gym),
        status: m.status || 'passive',
        approved_at: m.approved_at,
        request_date: m.request_date,
        created_at: m.created_at || new Date().toISOString(),
        updated_at: m.updated_at || new Date().toISOString(),
      }));
    } catch (error) {
      throw new Error(
        error instanceof Error
          ? error.message
          : MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND
      );
    }
  });
}

/**
 * Get member memberships with package information
 * Following Clean Code principles - focused responsibility, clear error handling
 */
export async function getMemberMembershipsWithPackages(): Promise<
  ApiResponse<MemberMembershipWithPackages[]>
> {
  return await createAction<MemberMembershipWithPackages[]>(
    async (_, supabase, userId) => {
      try {
        // First, get memberships
        const { data: memberships, error: membershipsError } = await supabase
          .from('gym_memberships')
          .select(
            `
            id,
            status,
            approved_at,
            request_date,
            created_at,
            updated_at,
            gym:gyms(id,name,address,gym_phone,slug)
          `
          )
          .eq('profile_id', userId)
          .order('created_at', { ascending: false });

        if (membershipsError) {
          throw new Error(
            `${MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND}: ${membershipsError.message}`
          );
        }

        if (!memberships || memberships.length === 0) {
          return [];
        }

        // Get package information for each membership
        const membershipIds = memberships.map(m => m.id);
        const { data: packageAssignments, error: packagesError } =
          await supabase
            .from('gym_membership_packages')
            .select(
              `
            membership_id,
            start_date,
            end_date,
            purchase_price,
            status,
            gym_packages!inner(
              id,
              name,
              description,
              price,
              duration_days,
              package_type,
              is_active,
              created_at,
              updated_at,
              gym_id,
              max_participants,
              session_count
            )
          `
            )
            .in('membership_id', membershipIds);

        if (packagesError) {
          console.error('Error fetching package information:', packagesError);
        }

        // Combine memberships with package information
        return memberships.map((m: any) => {
          const membershipPackages = processMembershipPackages(
            packageAssignments || [],
            m.id
          );

          return {
            id: m.id,
            gym: transformGymData(m.gym),
            status: m.status || 'passive',
            approved_at: m.approved_at,
            request_date: m.request_date,
            created_at: m.created_at || new Date().toISOString(),
            updated_at: m.updated_at || new Date().toISOString(),
            packages: membershipPackages,
          };
        });
      } catch (error) {
        throw new Error(
          error instanceof Error
            ? error.message
            : MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND
        );
      }
    }
  );
}

/**
 * Process membership packages data
 * Following Clean Code principles - extracted helper function for complex logic
 */
function processMembershipPackages(
  packageAssignments: any[],
  membershipId: string
): any[] {
  return (
    packageAssignments
      ?.filter(p => p.membership_id === membershipId)
      ?.map(p => {
        // gym_packages might be returned as array, take the first one
        const gymPackage = Array.isArray(p.gym_packages)
          ? p.gym_packages[0]
          : p.gym_packages;

        if (!gymPackage) return null;

        const now = new Date();
        const endDate = p.end_date ? new Date(p.end_date) : null;
        const isExpired = endDate ? endDate < now : false;

        return {
          id: gymPackage.id,
          gym_id: gymPackage.gym_id,
          name: gymPackage.name,
          description: gymPackage.description,
          price: gymPackage.price,
          duration_days: gymPackage.duration_days,
          is_active: gymPackage.is_active,
          created_at: gymPackage.created_at,
          updated_at: gymPackage.updated_at,
          package_type: gymPackage.package_type,
          max_participants: gymPackage.max_participants,
          session_count: gymPackage.session_count,
          // Additional information from package assignment
          packageName: gymPackage.name,
          packageType: gymPackage.package_type,
          isActive: gymPackage.is_active,
          isExpired,
          purchasePrice: p.purchase_price,
          startDate: p.start_date,
          endDate: p.end_date,
        };
      })
      .filter(Boolean) || []
  ); // Filter out null values
}

/**
 * Update membership status (active/passive)
 * Following Clean Code principles - focused responsibility, clear validation
 */
export async function updateMembershipStatus(
  membershipId: string,
  status: 'active' | 'passive'
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  return await createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      try {
        // Validate membership ID
        validateUserId(membershipId);

        // Check if membership belongs to this user (protected by RLS policy)
        const { data: membership, error: checkError } = await supabase
          .from('gym_memberships')
          .select('id')
          .eq('id', membershipId)
          .eq('profile_id', userId)
          .single();

        if (checkError || !membership) {
          throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND);
        }

        // Update status
        const { error } = await supabase
          .from('gym_memberships')
          .update({
            status,
            updated_at: new Date().toISOString(),
          })
          .eq('id', membershipId);

        if (error) {
          throw new Error(
            `${MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_UPDATE_FAILED}: ${error.message}`
          );
        }

        return {
          success: true,
          message: MEMBERSHIP_SUCCESS_MESSAGES.MEMBERSHIP_UPDATED,
        };
      } catch (error) {
        throw new Error(
          error instanceof Error
            ? error.message
            : MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_UPDATE_FAILED
        );
      }
    }
  );
}

/**
 * Cancel membership (make it passive)
 * Following Clean Code principles - focused responsibility, clear validation
 */
export async function cancelMembership(
  membershipId: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  return await createAdminAction<{ success: boolean; message: string }>(
    async (_, _supabase, userId, adminClient) => {
      try {
        // Validate membership ID
        validateUserId(membershipId);

        // Check if membership belongs to this user
        const { data: membership, error: checkError } = await adminClient
          .from('gym_memberships')
          .select('id, status, gym:gyms(name)')
          .eq('id', membershipId)
          .eq('profile_id', userId)
          .single();

        if (checkError || !membership) {
          throw new Error(MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND);
        }

        if (membership.status === 'passive') {
          throw new Error('Üyelik zaten pasif durumda');
        }

        // Make membership passive
        const { error } = await adminClient!
          .from('gym_memberships')
          .update({
            status: 'passive',
            updated_at: new Date().toISOString(),
          })
          .eq('id', membershipId);

        if (error) {
          throw new Error(
            `${MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CANCEL_FAILED}: ${error.message}`
          );
        }

        const gym = Array.isArray(membership.gym)
          ? membership.gym[0]
          : membership.gym;
        const gymName = gym?.name;

        return {
          success: true,
          message: `${gymName} ${MEMBERSHIP_SUCCESS_MESSAGES.MEMBERSHIP_CANCELLED}`,
        };
      } catch (error) {
        throw new Error(
          error instanceof Error
            ? error.message
            : MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CANCEL_FAILED
        );
      }
    }
  );
}

/**
 * Create gym membership only
 * Following Clean Code principles - focused responsibility, clear validation
 */
export async function createGymMembership(
  gymId: string,
  profileId: string
): Promise<
  ApiResponse<{
    membership: { id: string; status: string; created_at: string };
  }>
> {
  return await createAdminAction<{
    membership: { id: string; status: string; created_at: string };
  }>(async (_, _supabase, _userId, adminClient) => {
    try {

      // Validate input parameters
      validateGymId(gymId);
      validateUserId(profileId);

      // Enforce capacity limits (admin context)
      await enforceGymMemberLimitForAdmin(adminClient, gymId);

      // Prevent duplicate membership
      const { data: existing } = await adminClient!
        .from('gym_memberships')
        .select('id, status, created_at')
        .eq('gym_id', gymId)
        .eq('profile_id', profileId)
        .maybeSingle();
      if (existing) {
        return {
          membership: {
            id: existing.id,
            status: (existing as any).status,
            created_at: (existing as any).created_at,
          },
        };
      }

      // Create membership record using utility function
      const membershipData = createMembershipRecordData(profileId, gymId);

      const { data: membership, error: membershipError } = await adminClient!
        .from('gym_memberships')
        .insert(membershipData)
        .select()
        .single();

      if (membershipError) {
        handleMembershipDatabaseError(membershipError, 'create membership');
      }

      return {
        membership: {
          id: membership.id,
          status: membership.status || 'active',
          created_at: membership.created_at || new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new Error(
        error instanceof Error
          ? error.message
          : MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED
      );
    }
  });
}
