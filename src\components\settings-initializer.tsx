'use client';

import { useEffect, useState } from 'react';
import { initializeLocalSettings } from '@/hooks/use-local-settings';

/**
 * Client component to initialize local settings on app load
 * This prevents flash of unstyled content by applying stored settings immediately
 */
export function SettingsInitializer() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      initializeLocalSettings();
      setIsInitialized(true);
    }
  }, [isInitialized]);

  return null;
}
