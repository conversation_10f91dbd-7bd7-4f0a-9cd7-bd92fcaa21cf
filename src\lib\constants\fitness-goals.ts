/**
 * Fitness Goals Constants
 * 
 * Merkezi fitness hedefleri tanımları ve seçenekleri
 */

/**
 * Fitness hedefi seçenekleri - value ve label formatında
 */
export const FITNESS_GOAL_OPTIONS = [
  { value: 'weight_loss', label: 'Kilo Verme', emoji: '🔥' },
  { value: 'muscle_gain', label: '<PERSON><PERSON>', emoji: '💪' },
  { value: 'strength_training', label: '<PERSON><PERSON><PERSON>', emoji: '🏋️' },
  { value: 'endurance', label: 'Dayanıklılık', emoji: '🏃' },
  { value: 'flexibility', label: 'Esneklik', emoji: '🤸' },
  { value: 'general_fitness', label: 'Genel Fitness', emoji: '⚡' },
  { value: 'rehabilitation', label: 'Rehabilitasyon', emoji: '🩺' },
  { value: 'sports_performance', label: 'Spor Performansı', emoji: '🏆' },
  { value: 'body_shaping', label: 'Vücut Şekillendirme', emoji: '✨' },
  { value: 'health_maintenance', label: '<PERSON><PERSON><PERSON><PERSON><PERSON> Korum<PERSON>', emoji: '❤️' },
  { value: 'stress_relief', label: 'Stres Azaltmak', emoji: '🧘' },
  { value: 'other', label: 'Diğer', emoji: '📝' },
] as const;

/**
 * Sadece string array formatında fitness hedefleri (eski format uyumluluğu için)
 */
export const FITNESS_GOAL_STRINGS = [
  'Kilo vermek',
  'Kas yapmak', 
  'Kondisyon artırmak',
  'Genel sağlık',
  'Güç artırmak',
  'Esneklik artırmak',
  'Stres azaltmak',
  'Diğer',
] as const;

/**
 * Fitness hedefi açıklamaları
 */
export const FITNESS_GOAL_DESCRIPTIONS = {
  weight_loss: 'Kalori yakımına odaklanan kardiyovasküler egzersizler ve beslenme önerileri alacaksınız.',
  muscle_gain: 'Kas kütlesi artırımına yönelik ağırlık antrenmanları ve protein odaklı beslenme önerileri alacaksınız.',
  strength_training: 'Güç artırımına yönelik ağır ağırlık antrenmanları ve kuvvet egzersizleri önerilecek.',
  endurance: 'Dayanıklılık artırımına yönelik uzun süreli kardiyovasküler egzersizler önerilecek.',
  flexibility: 'Esneklik artırımına yönelik yoga, pilates ve germe egzersizleri önerilecek.',
  general_fitness: 'Genel sağlık ve fitness için dengeli bir antrenman programı önerilecek.',
  rehabilitation: 'Yaralanma sonrası iyileşme sürecine uygun özel egzersizler önerilecek.',
  sports_performance: 'Spor performansını artırmaya yönelik özel antrenman programları önerilecek.',
  body_shaping: 'Vücut şekillendirmeye yönelik tonlama ve şekillendirme egzersizleri önerilecek.',
  health_maintenance: 'Genel sağlığınızı korumaya yönelik düzenli egzersiz programları önerilecek.',
  stress_relief: 'Stres azaltmaya yönelik rahatlatıcı egzersizler ve meditasyon teknikleri önerilecek.',
  other: 'Özel ihtiyaçlarınıza göre kişiselleştirilmiş antrenman programları hazırlanacak.',
} as const;

/**
 * Fitness hedefi türleri
 */
export type FitnessGoalValue = typeof FITNESS_GOAL_OPTIONS[number]['value'];
export type FitnessGoalString = typeof FITNESS_GOAL_STRINGS[number];

/**
 * Utility fonksiyonlar
 */
export const getFitnessGoalLabel = (value: FitnessGoalValue): string => {
  const option = FITNESS_GOAL_OPTIONS.find(opt => opt.value === value);
  return option?.label || 'Bilinmeyen Hedef';
};

export const getFitnessGoalEmoji = (value: FitnessGoalValue): string => {
  const option = FITNESS_GOAL_OPTIONS.find(opt => opt.value === value);
  return option?.emoji || '📝';
};

export const getFitnessGoalDescription = (value: FitnessGoalValue): string => {
  return FITNESS_GOAL_DESCRIPTIONS[value] || 'Açıklama bulunamadı.';
};

/**
 * Eski string formatından yeni value formatına dönüştürme
 */
export const convertStringToValue = (goalString: string): FitnessGoalValue => {
  const mapping: Record<string, FitnessGoalValue> = {
    'Kilo vermek': 'weight_loss',
    'Kas yapmak': 'muscle_gain',
    'Kondisyon artırmak': 'endurance',
    'Genel sağlık': 'general_fitness',
    'Güç artırmak': 'strength_training',
    'Esneklik artırmak': 'flexibility',
    'Stres azaltmak': 'stress_relief',
    'Diğer': 'other',
  };
  
  return mapping[goalString] || 'other';
};
