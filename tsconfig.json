{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    // Strict mode ayarları - progressively enabled for better type safety
    "strict": true,
    "noImplicitAny": true, // Re-enabled: Catch implicit any types
    "noImplicitReturns": true, // Re-enabled: Ensure all code paths return a value
    "noImplicitThis": true,
    "noImplicitOverride": true, // Re-enabled: Require explicit override keyword
    "noUncheckedIndexedAccess": false, // Keep disabled for now - can break existing code
    "noPropertyAccessFromIndexSignature": false, // Keep disabled for now - can break existing code
    "exactOptionalPropertyTypes": false, // Keep disabled for now - can break existing code
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true, // Re-enabled: Catch side effect imports
    // Unused code detection
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    // Module resolution
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    // Emit ayarları
    "noEmit": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    // Interop ayarları
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    // JSX ayarları
    "jsx": "preserve",
    "jsxImportSource": "react",
    // Performance
    "incremental": true,
    "tsBuildInfoFile": ".next/cache/tsconfig.tsbuildinfo",
    // Next.js plugin
    "plugins": [
      {
        "name": "next"
      }
    ],
    // Path mapping
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"],
      "@/store/*": ["./src/store/*"],
      "@/utils/*": ["./src/lib/utils/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/*.css",
    "**/*.scss",
    "**/*.sass",
    "**/*.less",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "dist",
    "build",
    "coverage",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],
  "ts-node": {
    "esm": true
  }
}
