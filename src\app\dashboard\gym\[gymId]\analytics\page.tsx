import { Card, CardContent } from '@/components/ui/card';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';

import {
  MemberGrowthChart,
  PackageRevenueList,
  RevenueCard,
  RevenueChart,
} from './analytics-components';
import { formatCurrency } from '@/lib/utils';
import { getGymAnalytics } from '@/lib/actions/dashboard/company/gym-analytics';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';
import { UnauthorizedAccess } from '@/components/dashboard/common/UnauthorizedAccess';
export default async function AnalyticsPage({
  params,
}: {
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;

  // Dashboard context üzerinden yetki kontrolü
  const ctx = await getDashboardContext(gymId);
  if (!ctx.gymAccess?.hasAccess) {
    return <UnauthorizedAccess reason="Bu salona erişim yetkiniz bulunmuyor." />;
  }
  if (ctx.gymAccess?.restrictedPages?.includes('analytics')) {
    return <UnauthorizedAccess reason="Bu salonun analitiklerini görüntüleme yetkiniz bulunmuyor." />;
  }



  // Middleware zaten gym ownership kontrolü yaptığı için burada sadece gym data'sını alıyoruz
  const { data: gym, error } = await getGymById(gymId);

  if (error || !gym) {
    throw new Error('Salon bulunamadı');
  }

  // Analytics verilerini server'da çek
  const analyticsResponse = await getGymAnalytics(gymId);
  const analyticsData = analyticsResponse.data!;

  if (!analyticsResponse.success || !analyticsData) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="mb-2 text-lg font-semibold">
                Analytics Verisi Yüklenemedi
              </h3>
              <p className="text-muted-foreground">{analyticsResponse.error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  // Revenue cards verilerini hazırla
  const revenueCards = [
    {
      title: 'Toplam Gelir',
      value: formatCurrency(analyticsData.totalRevenue),
      description: 'Tüm zamanların toplam geliri',
      icon: 'DollarSign' as const,
    },
    {
      title: 'Aylık Gelir',
      value: formatCurrency(analyticsData.monthlyRevenue),
      description: 'Bu ayın geliri',
      icon: 'TrendingUp' as const,
      trend: {
        value: analyticsData.revenueGrowthRate,
        isPositive: analyticsData.revenueGrowthRate >= 0,
      },
    },
    {
      title: 'Toplam Üye',
      value: analyticsData.totalMembers,
      description: 'Kayıtlı toplam üye sayısı',
      icon: 'Users' as const,
    },
    {
      title: 'Aktif Üye',
      value: analyticsData.activeMembers,
      description: 'Aktif paketli üye sayısı',
      icon: 'UserCheck' as const,
    },
  ];
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Salon Analitikleri
        </h1>
        <p className="text-muted-foreground">
          {gym.name} salonunuzun gelir ve üye istatistikleri
        </p>
      </div>

      {/* Revenue Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {revenueCards.map((card, index) => (
          <RevenueCard
            key={index}
            title={card.title}
            value={card.value}
            description={card.description}
            icon={
              card.icon as 'DollarSign' | 'TrendingUp' | 'Users' | 'UserCheck'
            }
            trend={card.trend}
          />
        ))}
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        <RevenueChart data={analyticsData.revenueData} />
        <MemberGrowthChart data={analyticsData.memberGrowthData} />
      </div>

      {/* Package Revenue */}
      <PackageRevenueList data={analyticsData.topPackages} />
    </div>
  );
}
