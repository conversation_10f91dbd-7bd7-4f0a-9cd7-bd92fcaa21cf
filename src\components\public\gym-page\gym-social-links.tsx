'use client';

import { memo } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faInstagram,
  faFacebook,
  faXTwitter,
  faYoutube,
  faTiktok,
} from '@fortawesome/free-brands-svg-icons';
import { faGlobe } from '@fortawesome/free-solid-svg-icons';

interface GymSocialLinksProps {
  socialLinks: Record<string, string> | null;
  companyName: string;
}

const SOCIAL_PLATFORMS = {
  website: {
    icon: faGlobe,
    label: 'Website',
    color: 'text-blue-600 dark:text-blue-400',
    hoverColor: 'hover:text-blue-700 dark:hover:text-blue-300',
  },
  instagram: {
    icon: faInstagram,
    label: 'Instagram',
    color: 'text-pink-600 dark:text-pink-400',
    hoverColor: 'hover:text-pink-700 dark:hover:text-pink-300',
  },
  facebook: {
    icon: faFacebook,
    label: 'Facebook',
    color: 'text-blue-700 dark:text-blue-400',
    hoverColor: 'hover:text-blue-800 dark:hover:text-blue-300',
  },
  x: {
    icon: faXTwitter,
    label: 'X (Twitter)',
    color: 'text-gray-900 dark:text-gray-100',
    hoverColor: 'hover:text-gray-700 dark:hover:text-gray-300',
  },
  youtube: {
    icon: faYoutube,
    label: 'YouTube',
    color: 'text-red-600 dark:text-red-400',
    hoverColor: 'hover:text-red-700 dark:hover:text-red-300',
  },
  tiktok: {
    icon: faTiktok,
    label: 'TikTok',
    color: 'text-gray-900 dark:text-gray-100',
    hoverColor: 'hover:text-gray-700 dark:hover:text-gray-300',
  },
} as const;

export const GymSocialLinks = memo(function GymSocialLinks({
  socialLinks,
  companyName,
}: GymSocialLinksProps) {
  // Sosyal linkleri filtrele ve geçerli olanları al
  const validSocialLinks = socialLinks
    ? Object.entries(socialLinks).filter(
        ([platform, url]) =>
          url &&
          url.trim() &&
          platform in SOCIAL_PLATFORMS
      )
    : [];

  // Eğer hiç sosyal link yoksa bileşeni gösterme
  if (validSocialLinks.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-4 flex-wrap">
      <span className="text-sm font-medium text-muted-foreground">
        Bizi takip edin:
      </span>
      <div className="flex items-center gap-3">
        {validSocialLinks.map(([platform, url]) => {
          const config = SOCIAL_PLATFORMS[platform as keyof typeof SOCIAL_PLATFORMS];
          
          return (
            <a
              key={platform}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className={`
                w-10 h-10 rounded-full bg-card/80 backdrop-blur-sm border border-border/50
                flex items-center justify-center
                transition-all duration-300
                hover:scale-110 hover:shadow-lg hover:border-primary/30
                ${config.color} ${config.hoverColor}
              `}
              aria-label={`${companyName} ${config.label} sayfası`}
              title={`${config.label}'da takip edin`}
            >
              <FontAwesomeIcon 
                icon={config.icon} 
                className="h-5 w-5" 
                aria-hidden="true"
              />
            </a>
          );
        })}
      </div>
    </div>
  );
});
