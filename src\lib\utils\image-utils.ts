/**
 * Görsel işleme utility fonksiyonları
 */

/**
 * Bir resim dosyasını WebP formatına dönüştürür
 * @param file - Dönüştürülecek resim dosyası
 * @returns WebP formatında yeni File objesi
 */
export async function convertImageToWebp(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) return reject('Canvas context alınamadı');
      ctx.drawImage(img, 0, 0);
      canvas.toBlob(
        blob => {
          if (!blob) return reject('Webp blob oluşturulamadı');
          const webpFile = new File(
            [blob],
            file.name.replace(/\.[^.]+$/, '.webp'),
            { type: 'image/webp' }
          );
          resolve(webpFile);
        },
        'image/webp',
        0.92
      );
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Dosya boyutunu kontrol eder
 * @param file - Kontrol edilecek dosya
 * @param maxSizeMB - Maksimum boyut (MB cinsinden)
 * @returns Dosya boyutu uygunsa true
 */
export function validateFileSize(file: File, maxSizeMB: number = 5): boolean {
  return file.size <= maxSizeMB * 1024 * 1024;
}

/**
 * Dosya tipinin resim olup olmadığını kontrol eder
 * @param file - Kontrol edilecek dosya
 * @returns Dosya resim ise true
 */
export function validateImageType(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Desteklenen resim formatlarını kontrol eder
 * @param file - Kontrol edilecek dosya
 * @returns Desteklenen format ise true
 */
export function validateImageFormat(file: File): boolean {
  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return supportedFormats.includes(file.type);
}
