'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EnhancedAuthError } from '@/components/auth/enhanced-auth-error';
import { SubmitButton } from '@/components/auth/submit-button';
import { toast } from 'sonner';
import { ApiResponse } from '@/types/global/api';

interface RegisterFormProps {
  error?: string;
  identifierError?: string;
  passwordError?: string;
  context?: string;
  onEmailRegistration: (
    formData: FormData
  ) => Promise<{ email: string; success: boolean; error?: string }>;
  onPhoneRegistration: (
    formData: FormData
  ) => Promise<ApiResponse<{ message: string; requiresOtp: boolean }>>;
  onVerifyPhoneOtp: (
    formData: FormData
  ) => Promise<ApiResponse<{ userId: string; isNewUser: boolean }>>;
}

/**
 * Detect if input is email or phone number
 */
function detectInputType(value: string): 'email' | 'phone' {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const phonePattern = /^(\+90|0)?[5][0-9]{9}$/;
  const cleanValue = value.replace(/[\s\-\(\)]/g, '');

  if (emailPattern.test(value)) {
    return 'email';
  } else if (phonePattern.test(cleanValue)) {
    return 'phone';
  }

  return 'email';
}

export function RegisterForm({
  error,
  identifierError,
  passwordError,
  context,
  onEmailRegistration,
  onPhoneRegistration,
  onVerifyPhoneOtp,
}: RegisterFormProps) {
  const router = useRouter();
  const [clientError, setClientError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPhoneVerification, setShowPhoneVerification] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [canResend, setCanResend] = useState(true);
  const [timeUntilResend, setTimeUntilResend] = useState(0);

  const handleInitialSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const form = e.currentTarget;
    const formDataObj = new FormData(form);
    const identifier = formDataObj.get('identifier') as string;
    const password = formDataObj.get('password') as string;

    // Validation
    if (!identifier) {
      setClientError('Telefon numarası veya e-posta gereklidir');
      return;
    }

    setClientError('');

    // Detect input type
    const detectedInputType = detectInputType(identifier);

    // Şifre kontrolü - her durumda gerekli
    if (!password) {
      setClientError('Şifre gereklidir');
      return;
    }

    if (password.length < 6) {
      setClientError('Şifre en az 6 karakter olmalıdır');
      return;
    }

    if (detectedInputType === 'phone') {
      // Handle phone registration with password + OTP
      setIsSubmitting(true);
      setPhoneNumber(identifier);

      try {
        const phoneFormData = new FormData();
        phoneFormData.append('phone', identifier);
        phoneFormData.append('password', password);

        const result = await onPhoneRegistration(phoneFormData);

        if (result.success && result.data) {
          if (result.data.requiresOtp) {
            setShowPhoneVerification(true);
            toast.success(result.data.message);
          } else {
            // OTP gönderilemedi ama kayıt başarılı, manuel giriş yapabilir
            toast.success(
              'Kayıt başarılı! Giriş sayfasından şifre ile giriş yapabilirsiniz.'
            );
            router.push('/auth/login');
          }
        } else {
          setClientError(result.error || 'Telefon kayıt işlemi başarısız');
        }
      } catch (error) {
        setClientError('Telefon kayıt işlemi başarısız');
        console.error('Phone registration error:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Handle email registration
      setIsSubmitting(true);
      try {
        const result = await onEmailRegistration(formDataObj);

        if (result.success) {
          router.push(
            `/auth/verify-email?email=${encodeURIComponent(result.email)}`
          );
        } else {
          setClientError(result.error || 'E-posta kayıt işlemi başarısız');
        }
      } catch (error) {
        setClientError('E-posta kayıt işlemi başarısız');
        console.error('Email registration error:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode || !phoneNumber) {
      setClientError('Doğrulama kodu gereklidir');
      return;
    }

    setIsVerifyingCode(true);
    setClientError('');

    try {
      const verifyFormData = new FormData();
      verifyFormData.append('phone', phoneNumber);
      verifyFormData.append('token', verificationCode);

      const result = await onVerifyPhoneOtp(verifyFormData);

      if (result.success && result.data) {
        setIsVerified(true);
        toast.success('Telefon numarası doğrulandı!');

        // Yeni kullanıcı ise onboarding'e, mevcut kullanıcı ise panel'e yönlendir
        if (result.data.isNewUser) {
          router.push('/onboarding');
        } else {
          router.push('/dashboard');
        }
      } else {
        setClientError(result.error || 'Doğrulama kodu geçersiz');
      }
    } catch (error) {
      setClientError('Doğrulama kodu geçersiz');
      console.error('Verification error:', error);
    } finally {
      setIsVerifyingCode(false);
    }
  };

  const handleResendCode = async () => {
    if (!phoneNumber) return;

    setIsSendingCode(true);
    setClientError('');

    try {
      setCanResend(false);
      setTimeUntilResend(60);

      // Start countdown
      const interval = setInterval(() => {
        setTimeUntilResend(prev => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      toast.success('Doğrulama kodu tekrar gönderildi!');
    } catch (error) {
      setClientError('Kod tekrar gönderilemedi');
      console.error('Resend error:', error);
    } finally {
      setIsSendingCode(false);
    }
  };

  if (showPhoneVerification && !isVerified) {
    return (
      <div className="space-y-4">
        {/* Error Messages */}
        {clientError && (
          <EnhancedAuthError
            error={clientError}
            context={{
              action: 'register',
              method: 'phone',
              stage: 'verification',
            }}
            onRetry={() => setClientError('')}
          />
        )}

        <div className="text-center">
          <h3 className="text-lg font-semibold">Telefon Doğrulama</h3>
          <p className="text-muted-foreground mt-1 text-sm">
            {phoneNumber} numarasına gönderilen doğrulama kodunu giriniz
          </p>
          <p className="text-muted-foreground mt-2 text-xs">
            Numara kayıtlıysa giriş yapılır, değilse yeni hesap oluşturulur
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="verificationCode">Doğrulama Kodu</Label>
            <Input
              id="verificationCode"
              type="text"
              placeholder="123456"
              value={verificationCode}
              onChange={e => setVerificationCode(e.target.value)}
              maxLength={6}
              disabled={isVerifyingCode}
            />
          </div>

          <Button
            onClick={handleVerifyCode}
            disabled={isVerifyingCode || !verificationCode}
            className="w-full"
          >
            {isVerifyingCode ? 'Doğrulanıyor...' : 'Doğrula'}
          </Button>

          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResendCode}
              disabled={!canResend || isSendingCode}
            >
              {isSendingCode
                ? 'Gönderiliyor...'
                : canResend
                  ? 'Kodu Tekrar Gönder'
                  : `${timeUntilResend} saniye sonra tekrar gönderebilirsiniz`}
            </Button>
          </div>

          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowPhoneVerification(false);
                setVerificationCode('');
                setClientError('');
              }}
            >
              Geri Dön
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Error Messages */}
      {(error || clientError || identifierError || passwordError) && (
        <EnhancedAuthError
          error={error || clientError || identifierError || passwordError || ''}
          context={{
            action: 'register',
            method: context?.includes('email')
              ? 'email'
              : context?.includes('phone')
                ? 'phone'
                : undefined,
            stage: context?.includes('verification')
              ? 'verification'
              : 'initial',
          }}
          onRetry={() => setClientError('')}
        />
      )}

      <form onSubmit={handleInitialSubmit} className="space-y-4">
        <div>
          <Label htmlFor="identifier">Telefon Numarası veya E-posta</Label>
          <Input
            id="identifier"
            name="identifier"
            type="text"
            placeholder="5551234567 veya <EMAIL>"
            required
            disabled={isSubmitting}
          />
        </div>

        {/* Şifre alanı - her durumda gerekli */}
        <div>
          <Label htmlFor="password">Şifre</Label>
          <Input
            id="password"
            name="password"
            type="password"
            placeholder="En az 6 karakter"
            required
            minLength={6}
            disabled={isSubmitting}
          />
        </div>

        <SubmitButton disabled={isSubmitting}>
          {isSubmitting
            ? 'İşleniyor...'
            : isSendingCode
              ? 'Kod gönderiliyor...'
              : 'Kayıt Ol'}
        </SubmitButton>
      </form>
    </div>
  );
}
