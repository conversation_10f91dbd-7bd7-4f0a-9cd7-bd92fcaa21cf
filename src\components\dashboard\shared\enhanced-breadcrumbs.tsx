'use client';
import React from 'react';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { getCityNameById } from '@/lib/constants/cityConstants';
import {
  ChevronsLeftRight,
  Crown,
  Dumbbell,
  User,
  Building2,
  Plus,
} from 'lucide-react';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';
import { getGymNavigation } from '@/lib/constants/navConstants';
import { useDashboard } from '@/context/dashboard-context';

// Role icon mapping
const roleIconMap = {
  member: User,
  trainer: Dumbbell,
  company_manager: Crown,
  gym_manager: Building2, // gym_manager için ikon eklendi
};

const getRoleIcon = (role: string) => {
  return roleIconMap[role as keyof typeof roleIconMap] || User;
};

interface BreadcrumbItemType {
  label: string;
  href?: string;
  isActive?: boolean;
  isGym?: boolean;
  gymName?: string;
  gymAddress?: string;
  gymSlug?: string;
}

export function EnhancedBreadcrumbs() {
  const {
    roleOptions,
    companyGyms,
    trainerGyms,
    gymManagerGyms,
    isTrainer,
    isCompanyManager,
    isGymManager,
  } = useDashboard();
  const pathname = usePathname();
  const params = useParams();
  const gymIdFromParams = params?.gymId as string;

  const activeRole = useMemo(() => {
    if (pathname.startsWith('/dashboard/company')) return 'company_manager';
    if (pathname.startsWith('/dashboard/gym_manager')) return 'gym_manager';
    if (pathname.startsWith('/dashboard/member')) return 'member';
    if (pathname.startsWith('/dashboard/trainer')) return 'trainer';
    if (pathname.startsWith('/dashboard/gym')) {
      if (isCompanyManager) return 'company_manager';
      if (isGymManager) return 'gym_manager';
      if (isTrainer) return 'trainer';
    }
    // Fallback based on available roles
    if (isCompanyManager) return 'company_manager';
    if (isGymManager) return 'gym_manager';
    if (isTrainer) return 'trainer';
    
    return roleOptions[0]?.role || 'member';

  }, [pathname, isCompanyManager, isGymManager, isTrainer, roleOptions]);

  const [gymName, setGymName] = useState<string>('');
  const [gymAddress, setGymAddress] = useState<string>('');
  const [gymSlug, setGymSlug] = useState<string>('');
  const [isLoadingGym, setIsLoadingGym] = useState(false);

  useEffect(() => {
    async function fetchGymInfo() {
      if (!gymIdFromParams) {
        setGymName('');
        setGymAddress('');
        setGymSlug('');
        return;
      }

      setIsLoadingGym(true);
      try {
        const response = await getGymById(gymIdFromParams);
        if (response.success && response.data) {
          const data = response.data;
          setGymName(data.name);
          setGymSlug(data.slug || '');
          const addressParts: string[] = [];
          if (data.city) {
            const cityName: string = getCityNameById(data.city) || data.city;
            addressParts.push(cityName);
          }
          if (data.district) addressParts.push(data.district);
          setGymAddress(addressParts.join(', '));
        } else {
          setGymName('Bilinmeyen Salon');
          setGymAddress('');
          setGymSlug('');
        }
      } catch (error) {
        console.error('Gym bilgileri alınırken hata:', error);
        setGymName('Hata');
      } finally {
        setIsLoadingGym(false);
      }
    }

    fetchGymInfo();
  }, [gymIdFromParams]);

  const breadcrumbs = useMemo(() => {
    const items: BreadcrumbItemType[] = [];
    if (!pathname.startsWith('/dashboard')) return items;

    items.push({ label: 'Panel', href: '/dashboard' });

    const pathSegments = pathname.split('/').filter(Boolean);

    if (pathSegments[1] === 'gym' && gymIdFromParams) {
      const gymLabel = isLoadingGym ? 'Yükleniyor...' : gymName || 'Salon';

      items.push({
        label: gymLabel,
        href: `/dashboard/gym/${gymIdFromParams}`,
        isGym: true,
        gymName: gymName || undefined,
        gymAddress: gymAddress || undefined,
        gymSlug: gymSlug || undefined,
      });

      const gymNavItems = getGymNavigation(gymIdFromParams, activeRole as any);
      const matchingNavItem = gymNavItems
        .filter(item => pathname.startsWith(item.href))
        .sort((a, b) => b.href.length - a.href.length)[0];

      if (matchingNavItem && matchingNavItem.href !== `/dashboard/gym/${gymIdFromParams}`) {
        items.push({
          label: matchingNavItem.title,
          href: matchingNavItem.href,
          isActive: pathname === matchingNavItem.href,
        });
      }

      const lastSegment = pathSegments[pathSegments.length - 1];
      const isSubSubPage = pathSegments.length > 4 && matchingNavItem;

      if (isSubSubPage) {
        const subSubPageLabels: Record<string, string> = {
          add: 'Ekle', edit: 'Düzenle', view: 'Görüntüle', create: 'Oluştur', addmember: 'Üye Ekle',
        };
        const prevItem = items[items.length - 1];
        if (prevItem) prevItem.isActive = false;
        items.push({ label: subSubPageLabels[lastSegment] || lastSegment, isActive: true });
      }
    }

    if (items.length > 0 && !items.some(i => i.isActive)) {
      const lastItem = items[items.length - 1];
      lastItem.isActive = true;
      delete lastItem.href;
    }

    return items;
  }, [pathname, gymIdFromParams, gymName, gymAddress, gymSlug, isLoadingGym, activeRole]);

  const currentRoleOption = roleOptions.find(option => option.role === activeRole);

  const renderRoleSelector = () => {
    if (!currentRoleOption) return null;

    return (
      <div className="flex h-14 items-center">
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Link
              href={currentRoleOption.href}
              className={`hover:text-primary hover:bg-accent/50 flex h-full items-center space-x-2 px-3 text-sm font-medium transition-colors ${
                roleOptions.length > 1 ? 'rounded-l-md' : 'rounded-md'
              }`}
            >
              {React.createElement(getRoleIcon(currentRoleOption.role), { className: 'h-4 w-4' })}
              <span>{currentRoleOption.label}</span>
            </Link>
          </TooltipTrigger>
          <TooltipContent><p>{currentRoleOption.label} ana sayfasına git</p></TooltipContent>
        </Tooltip>
        {roleOptions.length > 1 && (
          <DropdownMenu>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="hover:text-primary hover:bg-accent/50 border-border/50 h-full rounded-r-md border-l px-2 transition-colors">
                    <ChevronsLeftRight className="h-3 w-3 rotate-90" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent><p>Rol değiştir</p></TooltipContent>
            </Tooltip>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">Rol Değiştir</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {roleOptions.map(option => (
                <DropdownMenuItem key={option.role} asChild>
                  <Link
                    href={option.href}
                    className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                      option.role === activeRole ? 'bg-accent' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                    {option.role === activeRole && <Badge variant="outline" className="ml-auto">Aktif</Badge>}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  };

  const renderGymSelector = () => {
    const isCompanyRole = activeRole === 'company_manager';
    const gymsForSelector =
      activeRole === 'company_manager'
        ? companyGyms
        : activeRole === 'gym_manager'
          ? gymManagerGyms
          : activeRole === 'trainer'
            ? trainerGyms
            : [];
    const showSelector = gymsForSelector.length > 0;

    if (!showSelector) return null;

    const currentGym = gymsForSelector.find(gym => gym.id === gymIdFromParams);

    return (
      <DropdownMenu>
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="hover:text-primary hover:bg-accent/50 flex h-14 items-center space-x-2 px-3 text-sm font-medium transition-colors"
              >
                <Building2 className="h-4 w-4 flex-shrink-0" />
                <div className="flex min-w-0 flex-col items-start">
                  <span className="truncate text-sm font-medium">
                    {currentGym?.name || gymName || 'Salon Seç'}
                  </span>
                  {currentGym && (
                    <span className="text-muted-foreground truncate text-xs">
                      {getCityNameById(currentGym.city) || currentGym.city},{' '}
                      {currentGym.district}
                    </span>
                  )}
                </div>
                <ChevronsLeftRight className="h-3 w-3 flex-shrink-0 rotate-90" />
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Salon değiştir</p>
          </TooltipContent>
        </Tooltip>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
            Salon Seç
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {gymsForSelector.map(gym => (
            <DropdownMenuItem key={gym.id} asChild>
              <Link
                href={`/dashboard/gym/${gym.id}`}
                className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${gym.id === gymIdFromParams ? 'bg-accent' : ''}`}
              >
                <div className="flex flex-col items-start space-y-0.5">
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span className="truncate">{gym.name}</span>
                  </div>
                  <span className="text-muted-foreground text-xs lowercase">
                    {getCityNameById(gym.city) || gym.city}, {gym.district}
                  </span>
                </div>
                {gym.id === gymIdFromParams && (
                  <Badge variant="secondary" className="text-xs">
                    Aktif
                  </Badge>
                )}
              </Link>
            </DropdownMenuItem>
          ))}
          {isCompanyRole && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  href="/dashboard/company/gym-setup"
                  className="flex w-full cursor-pointer items-center space-x-2 px-2 py-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Yeni Salon Ekle</span>
                </Link>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  const renderBreadcrumbContent = (item: BreadcrumbItemType) => {
    if (item.isGym && item.gymName) {
      return (
        <div className="from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15 flex items-center rounded-lg bg-gradient-to-r px-3 py-2 transition-all duration-200">
          <div className="bg-primary/20 flex h-6 w-6 items-center justify-center rounded-full">
            <Building2 className="h-3.5 w-3.5" />
          </div>
          <div className="flex min-w-0 flex-col">
            <span className="text-foreground truncate text-sm leading-tight font-semibold">{item.gymName}</span>
            {item.gymAddress && <span className="text-muted-foreground truncate text-xs leading-tight">{item.gymAddress}</span>}
          </div>
        </div>
      );
    }
    return item.label;
  };

  const roleSelector = renderRoleSelector();
  const gymSelector = renderGymSelector();

  const enhancedItems = [
    roleSelector && { type: 'role-selector', content: roleSelector },
    gymSelector && { type: 'gym-selector', content: gymSelector },
  ].filter(Boolean) as { type: string; content: React.ReactNode }[];

  const filteredBreadcrumbs = breadcrumbs.filter(item => !item.isGym && item.label !== 'Panel');

  if (enhancedItems.length === 0 && filteredBreadcrumbs.length === 0) {
    return null;
  }

  return (
    <TooltipProvider>
      <Breadcrumb className="hidden md:block">
        <BreadcrumbList className="flex items-center gap-0">
          {enhancedItems.map((item, index) => (
            <div key={item.type} className="flex items-center">
              <BreadcrumbItem>{item.content}</BreadcrumbItem>
              {(index < enhancedItems.length - 1 || filteredBreadcrumbs.length > 0) && <BreadcrumbSeparator className="mx-2" />}
            </div>
          ))}
          {filteredBreadcrumbs.map((item, index) => (
            <div key={item.href || item.label} className="flex items-center">
              <BreadcrumbItem>
                {item.isActive ? (
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <BreadcrumbPage className="flex items-center">{renderBreadcrumbContent(item)}</BreadcrumbPage>
                    </TooltipTrigger>
                    <TooltipContent><p>Mevcut sayfa: {item.label}</p></TooltipContent>
                  </Tooltip>
                ) : (
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <BreadcrumbLink asChild>
                        <Link href={item.href!} className="flex items-center">{renderBreadcrumbContent(item)}</Link>
                      </BreadcrumbLink>
                    </TooltipTrigger>
                    <TooltipContent><p>{item.label} sayfasına git</p></TooltipContent>
                  </Tooltip>
                )}
              </BreadcrumbItem>
              {index < filteredBreadcrumbs.length - 1 && <BreadcrumbSeparator className="mx-2" />}
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </TooltipProvider>
  );
}
