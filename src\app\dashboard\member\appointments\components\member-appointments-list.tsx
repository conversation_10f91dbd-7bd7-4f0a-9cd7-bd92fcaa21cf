'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { cancelAppointment } from '@/lib/actions/appointments/appointment-actions';
import { toast } from 'sonner';

// Member appointment type based on appointment_participants table
interface MemberAppointmentWithDetails {
  id: string;
  appointment_id: string;
  status: string | null;
  notes?: string | null;
  created_at?: string | null;

  appointment?: {
    id: string;
    appointment_date: string;
    status: string | null;
    notes?: string | null;
    trainer_profile?: {
      id: string;
      full_name: string;
    } | null;
  } | null;

  membership_package?: {
    gym_package?: {
      name: string;
      package_type: string;
      session_duration_minutes: number;
    } | null;
  } | null;
}

interface MemberAppointmentsListProps {
  appointments: MemberAppointmentWithDetails[];
  onUpdate?: () => void;
}

export function MemberAppointmentsList({
  appointments,
  onUpdate,
}: MemberAppointmentsListProps) {
  const [cancellingId, setCancellingId] = useState<string | null>(null);

  const handleCancelAppointment = async (appointmentId: string) => {
    setCancellingId(appointmentId);
    try {
      const result = await cancelAppointment(appointmentId);
      if (result.success) {
        toast.success('Randevu başarıyla iptal edildi');
        onUpdate?.();
      } else {
        toast.error(result.error || 'Randevu iptal edilemedi');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
    } finally {
      setCancellingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: {
        label: '📅 Planlandı',
        variant: 'default' as const,
        className: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800'
      },
      completed: {
        label: '✅ Tamamlandı',
        variant: 'secondary' as const,
        className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800'
      },
      cancelled: {
        label: '❌ İptal Edildi',
        variant: 'destructive' as const,
        className: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800'
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'secondary' as const,
      className: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800'
    };

    return (
      <Badge
        variant="outline"
        className={`${config.className} font-medium`}
      >
        {config.label}
      </Badge>
    );
  };

  const canCancelAppointment = (appointment: MemberAppointmentWithDetails) => {
    if (
      appointment.status === 'cancelled' ||
      appointment.status === 'completed' ||
      appointment.appointment?.status === 'cancelled' ||
      appointment.appointment?.status === 'completed'
    ) {
      return false;
    }

    if (!appointment.appointment?.appointment_date) {
      return false;
    }

    const appointmentDate = new Date(appointment.appointment.appointment_date);
    const now = new Date();
    const hoursDiff =
      (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    return hoursDiff > 2; // 2 saatten fazla kaldıysa iptal edilebilir
  };

  if (appointments.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Calendar className="text-muted-foreground mb-4 h-12 w-12" />
          <p className="text-muted-foreground text-center">
            Henüz randevunuz bulunmuyor.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {appointments.map(appointment => (
        <Card key={appointment.id}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {appointment.membership_package?.gym_package?.name ||
                  'Paket Bilgisi Yok'}
              </CardTitle>
              {getStatusBadge(
                appointment.appointment?.status || appointment.status || 'scheduled'
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="flex items-center gap-2">
                <Calendar className="text-muted-foreground h-4 w-4" />
                <span className="text-sm">
                  {appointment.appointment?.appointment_date
                    ? format(
                        new Date(appointment.appointment.appointment_date),
                        'dd MMMM yyyy',
                        {
                          locale: tr,
                        }
                      )
                    : 'Tarih Bilgisi Yok'}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="text-muted-foreground h-4 w-4" />
                <span className="text-sm">
                  {appointment.membership_package?.gym_package
                    ?.session_duration_minutes
                    ? `${appointment.membership_package.gym_package.session_duration_minutes} dakika`
                    : 'Süre Bilgisi Yok'}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <User className="text-muted-foreground h-4 w-4" />
                <span className="text-sm">
                  {appointment.appointment?.trainer_profile?.full_name ||
                    'Eğitmen Atanmadı'}
                </span>
              </div>
            </div>

            {appointment.notes && (
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="text-muted-foreground mt-0.5 h-4 w-4" />
                  <div>
                    <p className="text-sm font-medium">Notlar</p>
                    <p className="text-muted-foreground text-sm">
                      {appointment.notes}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {canCancelAppointment(appointment) && (
              <div className="flex justify-end pt-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() =>
                    handleCancelAppointment(appointment.appointment_id)
                  }
                  disabled={cancellingId === appointment.id}
                >
                  {cancellingId === appointment.id
                    ? 'İptal Ediliyor...'
                    : 'Randevuyu İptal Et'}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
