import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { resetPassword } from '@/lib/actions/auth/auth-actions';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';

export const metadata: Metadata = {
  title: 'Şifremi Unuttum | Sportiva',
  description: 'Sportiva şifre sıfırlama sayfası.',
};

interface ForgotPasswordPageProps {
  searchParams: {
    error?: string;
    message?: string;
    email_error?: string;
  };
}

export default async function ForgotPasswordPage({
  searchParams,
}: ForgotPasswordPageProps) {
  const params = await searchParams;
  const { error, message, email_error } = params;

  async function handlePasswordReset(formData: FormData) {
    'use server';

    try {
      const result = await resetPassword(formData);

      if (!result.success) {
        const email = formData.get('email') as string;
        redirect(
          `/auth/forgot-password?error=${encodeURIComponent(
            result.error || 'Şifre sıfırlama linki gönderilemedi'
          )}&email=${encodeURIComponent(email)}`
        );
      }

      // Başarılı durumda mesaj ile birlikte aynı sayfada kal
      redirect(
        `/auth/forgot-password?message=${encodeURIComponent(
          result.message || 'Şifre sıfırlama linki gönderildi'
        )}`
      );
    } catch (error) {
      console.error('Password reset error:', error);
      redirect(
        `/auth/forgot-password?error=${encodeURIComponent(
          'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.'
        )}`
      );
    }
  }

  return (
    <div className="animate-in slide-in-from-bottom-4 w-full duration-700">
      {/* Header */}
      <div className="mb-8 text-center">
        <h1 className="text-foreground mb-2 text-2xl font-bold lg:text-3xl">
          {AUTH_UI_TEXT.FORGOT_PASSWORD_TITLE}
        </h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          {AUTH_UI_TEXT.FORGOT_PASSWORD_SUBTITLE}
        </p>
      </div>

      {/* Success Message */}
      {message && (
        <div
          className="animate-in slide-in-from-top-2 mb-6 flex w-full items-center space-x-3 rounded-xl border border-green-500/30 bg-green-50/50 p-4 text-green-700 duration-500 dark:bg-green-950/20 dark:text-green-400"
          role="alert"
          aria-live="polite"
        >
          <span className="text-sm font-medium">{message}</span>
        </div>
      )}

      {/* Form Card */}
      <div className="bg-card/80 border-border/50 shadow-primary/5 hover:shadow-primary/10 rounded-2xl border p-6 shadow-2xl backdrop-blur-sm transition-all duration-500 hover:scale-[1.01] lg:p-8">
        <ForgotPasswordForm
          onSubmit={handlePasswordReset}
          error={error}
          emailError={email_error}
        />
      </div>

      {/* Footer */}
      <div className="mt-6 flex flex-col items-center gap-3 sm:flex-row sm:justify-center sm:gap-2">
        <span className="text-sm sm:text-base">Şifrenizi hatırladınız mı?</span>
        <Link
          href="/auth/login"
          className="text-primary hover:text-primary/80 hover:bg-primary/10 focus:ring-primary/50 inline-flex min-h-[44px] items-center justify-center rounded-lg px-4 py-2 font-semibold transition-all duration-200 hover:underline focus:outline-none focus:ring-2 active:scale-95"
        >
          {AUTH_UI_TEXT.BACK_TO_LOGIN}
        </Link>
      </div>
    </div>
  );
}
