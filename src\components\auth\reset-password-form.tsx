'use client';

import { useState } from 'react';
import { ModernAuthInput } from './modern-auth-input';
import { SubmitButton } from './submit-button';
import { EnhancedAuthError } from './enhanced-auth-error';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';
import { CheckCircle, XCircle } from 'lucide-react';

interface ResetPasswordFormProps {
  onSubmit: (formData: FormData) => Promise<void>;
  error?: string;
  passwordError?: string;
  confirmPasswordError?: string;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  isValid: boolean;
}

export function ResetPasswordForm({
  onSubmit,
  error,
  passwordError,
  confirmPasswordError,
}: ResetPasswordFormProps) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleSubmit = async (formData: FormData) => {
    await onSubmit(formData);
  };

  // <PERSON><PERSON><PERSON> güçlülük kontrolü
  const checkPasswordStrength = (password: string): PasswordStrength => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 6) {
      score += 1;
    } else {
      feedback.push('En az 6 karakter olmalı');
    }

    if (password.length >= 8) {
      score += 1;
    } else if (password.length >= 6) {
      feedback.push('8+ karakter daha güvenli');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Büyük harf ekleyin');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Küçük harf ekleyin');
    }

    if (/[0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Rakam ekleyin');
    }

    if (/[^A-Za-z0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Özel karakter ekleyin');
    }

    return {
      score,
      feedback,
      isValid: score >= 3 && password.length >= 6,
    };
  };

  const passwordStrength = password ? checkPasswordStrength(password) : null;
  const passwordsMatch =
    password && confirmPassword && password === confirmPassword;

  return (
    <div className="space-y-4">
      {/* Enhanced Error Message */}
      {error && (
        <EnhancedAuthError
          error={error}
          context={{
            action: 'reset_password',
            method: 'email',
            stage: 'callback',
          }}
          onRetry={() => window.location.reload()} // Bu durumda sayfa yenileme gerekli
        />
      )}

      <form action={handleSubmit} className="space-y-6">
        {/* Yeni Şifre */}
        <div className="space-y-2">
          <ModernAuthInput
            name="password"
            type="password"
            label="Yeni Şifre"
            placeholder="Yeni şifrenizi girin"
            required
            error={passwordError}
            value={password}
            onChange={e => setPassword(e.target.value)}
          />

          {/* Şifre Güçlülük Göstergesi */}
          {password && passwordStrength && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="bg-muted h-2 flex-1 rounded-full">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      passwordStrength.score <= 2
                        ? 'bg-red-500'
                        : passwordStrength.score <= 4
                          ? 'bg-yellow-500'
                          : 'bg-green-500'
                    }`}
                    style={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                  />
                </div>
                <span className="text-muted-foreground text-xs">
                  {passwordStrength.score <= 2
                    ? 'Zayıf'
                    : passwordStrength.score <= 4
                      ? 'Orta'
                      : 'Güçlü'}
                </span>
              </div>

              {passwordStrength.feedback.length > 0 && (
                <div className="text-muted-foreground text-xs">
                  <p className="mb-1">Şifrenizi güçlendirmek için:</p>
                  <ul className="space-y-1">
                    {passwordStrength.feedback.map((item, index) => (
                      <li key={index} className="flex items-center space-x-1">
                        <XCircle className="h-3 w-3 text-red-500" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Şifre Onayı */}
        <div className="space-y-2">
          <ModernAuthInput
            name="confirmPassword"
            type="password"
            label="Şifre Onayı"
            placeholder="Şifrenizi tekrar girin"
            required
            error={confirmPasswordError}
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
          />

          {/* Şifre Eşleşme Kontrolü */}
          {confirmPassword && (
            <div className="flex items-center space-x-2 text-xs">
              {passwordsMatch ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">Şifreler eşleşiyor</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span className="text-red-600">Şifreler eşleşmiyor</span>
                </>
              )}
            </div>
          )}
        </div>

        <SubmitButton disabled={!passwordStrength?.isValid || !passwordsMatch}>
          {AUTH_UI_TEXT.RESET_PASSWORD_BUTTON}
        </SubmitButton>
      </form>

      {/* Güvenlik Bilgilendirmesi */}
      <div className="bg-muted/50 text-muted-foreground mt-6 rounded-lg p-4 text-sm">
        <p className="mb-2 font-medium">🔒 Güvenlik İpuçları</p>
        <ul className="space-y-1 text-xs">
          <li>• Güçlü bir şifre seçin (en az 8 karakter)</li>
          <li>• Büyük/küçük harf, rakam ve özel karakter kullanın</li>
          <li>• Kişisel bilgilerinizi şifrede kullanmayın</li>
          <li>• Şifrenizi kimseyle paylaşmayın</li>
        </ul>
      </div>
    </div>
  );
}
