/**
 * Telefon numarası işlemleri için merkezi utility fonksiyonları
 * Bu dosya auth ve profile işlemlerinde kullanılan telefon validasyon ve formatlama
 * fonksiyonlarını tek yerde toplar.
 */

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface PhoneFormatResult {
  /** Database'de saklanacak format (örn: "905551234567") */
  clean: string;
  /** Supabase auth için format (örn: "+905551234567") */
  formatted: string;
  /** Kullanıcıya gösterilecek format (örn: "0555 123 45 67") */
  display: string;
}

export interface PhoneValidationResult {
  isValid: boolean;
  error?: string;
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Telefon numarası validasyonu
 * Türkiye telefon numarası formatlarını destekler
 */
export function validatePhoneNumber(phone: string): PhoneValidationResult {
  if (!phone) {
    return {
      isValid: false,
      error: 'Telefon numarası gereklidir',
    };
  }

  // Boşlukları temizle
  const cleanInput = phone.replace(/\s/g, '');
  
  // Türkiye telefon numarası regex'i
  // Desteklenen formatlar:
  // - 05551234567 (11 haneli, 0 ile başlayan)
  // - 5551234567 (10 haneli)
  // - +905551234567 (13 haneli, +90 ile başlayan)
  // - 905551234567 (12 haneli, 90 ile başlayan)
  const phoneRegex = /^(\+90|90|0)?[5][0-9]{9}$/;
  
  if (!phoneRegex.test(cleanInput)) {
    return {
      isValid: false,
      error: 'Geçerli bir telefon numarası giriniz (örn: 0555 123 45 67)',
    };
  }

  return { isValid: true };
}

/**
 * Telefon numarasının benzersizliğini kontrol et
 */
export function validatePhoneUniqueness(
  existingProfile: any,
  currentUserId?: string
): PhoneValidationResult {
  if (existingProfile && existingProfile.id !== currentUserId) {
    return {
      isValid: false,
      error: 'Bu telefon numarası zaten kayıtlı',
    };
  }
  return { isValid: true };
}

// ============================================================================
// FORMATTING FUNCTIONS
// ============================================================================

/**
 * Telefon numarasını farklı formatlara çevir
 */
export function formatPhoneNumber(phone: string): PhoneFormatResult {
  // Önce validasyon yap
  const validation = validatePhoneNumber(phone);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  // Sadece rakamları al
  const cleanPhone = phone.replace(/\D/g, '');

  let dbFormat: string; // Database formatı (90xxxxxxxxxx)
  let authFormat: string; // Supabase auth formatı (+90xxxxxxxxxx)

  if (cleanPhone.startsWith('90')) {
    // 905551234567 formatı
    dbFormat = cleanPhone;
    authFormat = '+' + cleanPhone;
  } else if (cleanPhone.startsWith('0')) {
    // 05551234567 formatı
    dbFormat = '90' + cleanPhone.substring(1);
    authFormat = '+90' + cleanPhone.substring(1);
  } else if (cleanPhone.length === 10) {
    // 5551234567 formatı
    dbFormat = '90' + cleanPhone;
    authFormat = '+90' + cleanPhone;
  } else {
    throw new Error('Geçerli bir telefon numarası giriniz');
  }

  // Display formatı oluştur (0555 123 45 67)
  const displayNumber = dbFormat.substring(2); // 90'ı çıkar
  const displayFormat = `0${displayNumber.substring(0, 3)} ${displayNumber.substring(3, 6)} ${displayNumber.substring(6, 8)} ${displayNumber.substring(8)}`;

  return {
    clean: dbFormat,
    formatted: authFormat,
    display: displayFormat,
  };
}

/**
 * Database'den gelen telefon numarasını display formatına çevir
 */
export function formatPhoneForDisplay(dbPhone: string): string {
  if (!dbPhone) return '';
  
  // 905551234567 -> 0555 123 45 67
  if (dbPhone.startsWith('90') && dbPhone.length === 12) {
    const number = dbPhone.substring(2);
    return `0${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6, 8)} ${number.substring(8)}`;
  }
  
  return dbPhone; // Fallback
}

/**
 * Kullanıcı inputunu parse et ve temizle
 */
export function parsePhoneInput(input: string): string {
  if (!input) return '';
  
  // Sadece rakamları ve + işaretini bırak
  return input.replace(/[^\d+]/g, '');
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * İki telefon numarasının aynı olup olmadığını kontrol et
 */
export function isSamePhoneNumber(phone1: string, phone2: string): boolean {
  try {
    const format1 = formatPhoneNumber(phone1);
    const format2 = formatPhoneNumber(phone2);
    return format1.clean === format2.clean;
  } catch {
    return false;
  }
}

/**
 * Telefon numarasının Türkiye numarası olup olmadığını kontrol et
 */
export function isTurkishPhoneNumber(phone: string): boolean {
  try {
    const formatted = formatPhoneNumber(phone);
    return formatted.clean.startsWith('90');
  } catch {
    return false;
  }
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const PHONE_CONSTANTS = {
  /** Türkiye ülke kodu */
  COUNTRY_CODE: '90',
  /** Türkiye ülke kodu prefix */
  COUNTRY_PREFIX: '+90',
  /** Minimum telefon numarası uzunluğu (sadece rakamlar) */
  MIN_LENGTH: 10,
  /** Maximum telefon numarası uzunluğu (ülke kodu dahil) */
  MAX_LENGTH: 13,
  /** Türkiye cep telefonu başlangıç rakamları */
  MOBILE_PREFIXES: ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59'],
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const PHONE_ERROR_MESSAGES = {
  REQUIRED: 'Telefon numarası gereklidir',
  INVALID_FORMAT: 'Geçerli bir telefon numarası giriniz (örn: 0555 123 45 67)',
  ALREADY_EXISTS: 'Bu telefon numarası zaten kayıtlı',
  NOT_TURKISH: 'Sadece Türkiye telefon numaraları desteklenmektedir',
  TOO_SHORT: 'Telefon numarası çok kısa',
  TOO_LONG: 'Telefon numarası çok uzun',
} as const;
