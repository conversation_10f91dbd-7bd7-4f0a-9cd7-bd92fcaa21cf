'use server';

import { TrainerPermissions } from './server-auth';
import { getTrainerPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';
import { createAction } from '@/lib/actions/core/core';
import { redirect } from 'next/navigation';

/**
 * Kullanıcının belirli bir gym'deki permissions'larını getirir
 * RLS politikaları ile yetki kontrolü yapar
 */
export async function getUserGymPermissions(gymId: string): Promise<{
  permissions: TrainerPermissions;
  userId: string;
}> {
  const result = await createAction<{
    permissions: TrainerPermissions;
    userId: string;
  }>(
    async (_, __, userId) => {
      // Permissions'ları getir
      const permissionsResult = await getTrainerPermissions(gymId, userId);

      if (!permissionsResult.success || !permissionsResult.data) {
        throw new Error('Yet<PERSON> bilgileri alınamadı');
      }

      return {
        permissions: permissionsResult.data,
        userId: userId,
      };
    }
  );

  if (!result.success) {
    throw new Error(result.error || 'Yetki bilgileri alınamadı');
  }

  return result.data!;
}

/**
 * Kullanıcının belirli bir permission'a sahip olup olmadığını kontrol eder
 * Eğer yoksa error sayfasına yönlendirir
 */
export async function requireUserPermission(
  gymId: string,
  category: keyof TrainerPermissions,
  action: string,
  errorMessage?: string
): Promise<{
  permissions: TrainerPermissions;
  userId: string;
}> {
  const { permissions, userId } = await getUserGymPermissions(gymId);

  // Permission kontrolü
  const categoryPermissions = permissions[category] as any;
  const hasPermission =
    categoryPermissions && categoryPermissions[action] === true;

  if (!hasPermission) {
    const defaultMessage = `Bu işlem için yetkiniz bulunmuyor`;
    const message = errorMessage || defaultMessage;

    redirect(
      `/error?type=permission_denied&message=${encodeURIComponent(message)}`
    );
  }

  return { permissions, userId };
}

/**
 * Sadece belirli bir kategori yetkisini çeken optimized fonksiyon
 * Tüm yetkileri almak yerine sadece ihtiyaç olan kategoriyi getirir
 */
export async function getUserCategoryPermissions<
  T extends keyof TrainerPermissions,
>(
  gymId: string,
  category: T
): Promise<{
  permissions: TrainerPermissions[T];
  userId: string;
}> {
  const result = await createAction<{
    permissions: TrainerPermissions[T];
    userId: string;
  }>(
    async (_, __, userId) => {
      // Tüm permissions'ları al (RPC fonksiyonu tek seferde tüm yetkileri döndürür)
      const permissionsResult = await getTrainerPermissions(gymId, userId);

      if (!permissionsResult.success || !permissionsResult.data) {
        throw new Error('Yetki bilgileri alınamadı');
      }

      // Sadece istenen kategoriyi döndür
      const categoryPermissions = permissionsResult.data[category];

      return {
        permissions: categoryPermissions,
        userId: userId,
      };
    }
  );

  if (!result.success) {
    throw new Error(result.error || 'Yetki bilgileri alınamadı');
  }

  return result.data!;
}

/**
 * Permission kontrolü yapan component wrapper
 * Eğer permission yoksa hata mesajı gösterir
 */
export interface PermissionGuardProps {
  permissions: TrainerPermissions;
  category: keyof TrainerPermissions;
  action: string;
  errorMessage?: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}
