'use client';

import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  getGymTrainers,
  TrainerWithProfile,
} from '@/lib/actions/dashboard/company/appointment-actions';

interface AppointmentsFiltersProps {
  gymId: string;
  searchParams: {
    startDate?: string;
    endDate?: string;
    trainerId?: string;
    status?: string;
  };
}

export function AppointmentsFilters({
  gymId,
  searchParams,
}: AppointmentsFiltersProps) {
  const router = useRouter();
  const params = useSearchParams();
  const urlParams = useParams();
  const [trainers, setTrainers] = useState<TrainerWithProfile[]>([]);
  const [loading, setLoading] = useState(true);

  // Load trainers
  useEffect(() => {
    const loadTrainers = async () => {
      try {
        const result = await getGymTrainers(gymId);
        if (result.success) {
          setTrainers(result.data || []);
        }
      } catch (error) {
        console.error('Error loading trainers:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTrainers();
  }, [gymId]);

  const updateFilter = (key: string, value: string) => {
    const newParams = new URLSearchParams(params.toString());

    if (value && value !== 'all') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }

    router.push(
      `/dashboard/gym/${urlParams.gymId}/appointments?${newParams.toString()}`
    );
  };

  const clearFilters = () => {
    router.push(`/dashboard/gym/${urlParams.gymId}/appointments`);
  };

  const hasFilters = Object.values(searchParams).some(
    value => value && value !== 'all'
  );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        {/* Start Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Başlangıç Tarihi</label>
          <Input
            type="date"
            value={searchParams.startDate || ''}
            onChange={e => updateFilter('startDate', e.target.value)}
          />
        </div>

        {/* End Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Bitiş Tarihi</label>
          <Input
            type="date"
            value={searchParams.endDate || ''}
            onChange={e => updateFilter('endDate', e.target.value)}
          />
        </div>

        {/* Trainer Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Antrenör</label>
          <Select
            value={searchParams.trainerId || 'all'}
            onValueChange={value => updateFilter('trainerId', value)}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Antrenör seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Antrenörler</SelectItem>
              {trainers.map(trainer => (
                <SelectItem key={trainer.id} value={trainer.id}>
                  {trainer.full_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Durum</label>
          <Select
            value={searchParams.status || 'all'}
            onValueChange={value => updateFilter('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Durum seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Durumlar</SelectItem>
              <SelectItem value="scheduled">Planlandı</SelectItem>
              <SelectItem value="completed">Tamamlandı</SelectItem>
              <SelectItem value="cancelled">İptal Edildi</SelectItem>
              <SelectItem value="no_show">Gelmedi</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {hasFilters && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Filtreleri Temizle
          </Button>
        </div>
      )}
    </div>
  );
}
