import { Target, Eye, Heart, Lightbulb } from "lucide-react";

export function AboutMission() {
  const values = [
    {
      icon: Target,
      title: "Misyonumuz",
      description: "Spor salonu işletmecilerinin dijital dönüşü<PERSON>ü<PERSON>ü destekleyerek, daha verimli ve karlı işletmeler yaratmak."
    },
    {
      icon: Eye,
      title: "Vizyonumuz",
      description: "Türkiye'nin en güvenilir ve yenilikçi spor salonu yönetim platformu olmak."
    },
    {
      icon: Heart,
      title: "Değ<PERSON>lerimiz",
      description: "<PERSON><PERSON><PERSON><PERSON><PERSON> odaklılık, sü<PERSON><PERSON> gelişim, şeffaflık ve güvenilirlik temel değerlerimizdir."
    },
    {
      icon: Lightbulb,
      title: "İnovasyonumuz",
      description: "Teknolojinin gücünü kullanarak spor endüstrisinde sürekli yenilik yaratıyoruz."
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Neden Sportiva?
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Spor salonu yönetiminde karşılaşılan zorlukları anlıyor ve bu sorunlara
              teknolojik çözümler üretiyoruz. İşte bizi farklı kılan değerler:
            </p>
          </div>

          {/* Values Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <div
                  key={index}
                  className="bg-background rounded-xl p-8 border border-border hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-start gap-6">
                    <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-primary mb-3">
                        {value.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Story Section */}
          <div className="mt-20 bg-background rounded-xl p-8 md:p-12 border border-border">
            <div className="max-w-4xl mx-auto text-center">
              <h3 className="text-2xl md:text-3xl font-bold text-primary mb-6">
                Hikayemiz
              </h3>
              <div className="space-y-6 text-muted-foreground leading-relaxed">
                <p className="text-lg">
                  Sportiva, spor salonu işletmecilerinin günlük operasyonlarında yaşadığı zorlukları
                  çözmek amacıyla 2024 yılında kuruldu. Kurucu ekibimiz, hem teknoloji hem de spor
                  endüstrisi deneyimine sahip profesyonellerden oluşuyor.
                </p>
                <p className="text-lg">
                  Başlangıçta küçük bir ekiple yola çıktık, ancak vizyonumuz büyüktü: Türkiye&apos;deki
                  tüm spor salonlarının dijital dönüşümüne öncülük etmek. Bugün, yüzlerce salon
                  tarafından güvenilen bir platform haline geldik.
                </p>
                <p className="text-lg">
                  Gelecekte, sadece Türkiye&apos;de değil, bölgesel bir lider olarak spor teknolojileri
                  alanında yenilikçi çözümler sunmaya devam edeceğiz.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
