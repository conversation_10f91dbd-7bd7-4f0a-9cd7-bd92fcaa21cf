'use server';

import { ApiResponse } from '@/types';
import { UserSettings } from '@/types/database/tables';
import {
  ProfileVisibility,
  NotificationCategories,
} from '@/types/database/enums';

import { z } from 'zod';
import { createAction } from '../core/core';

// Validation schemas
const upSertUserSettingsSchema = z.object({
  email_notifications: z.boolean().optional(),
  push_notifications: z.boolean().optional(),
  profile_visibility: z.enum(['public', 'private', 'friends']).optional(),
  notification_categories: z
    .object({
      appointments: z.boolean(),
      payments: z.boolean(),
      announcements: z.boolean(),
      reminders: z.boolean(),
    })
    .optional(),
});

export type upSertUserSettingsData = z.infer<typeof upSertUserSettingsSchema>;

/**
 * Kullanıcının ayarlarını getirir, yo<PERSON><PERSON> varsay<PERSON>lan ayarları oluşturur
 */
export async function getUserSettings(): Promise<ApiResponse<UserSettings>> {
  return createAction<UserSettings>(async (_, supabase, userId) => {
    // Önce mevcut ayarları kontrol et
    const { data: existingSettings, error: fetchError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('profile_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw new Error(
        'Ayarlar getirilirken hata oluştu: ' + fetchError.message
      );
    }

    // Eğer ayarlar varsa döndür
    if (existingSettings) {
      return existingSettings;
    }

    // Yoksa varsayılan ayarları oluştur
    const defaultSettings = {
      profile_id: userId,
      email_notifications: true,
      push_notifications: true,
      profile_visibility: 'public' as ProfileVisibility,
      notification_categories: {
        appointments: true,
        payments: true,
        announcements: true,
        reminders: true,
      } as NotificationCategories,
    };

    const { data: newSettings, error: insertError } = await supabase
      .from('user_settings')
      .insert(defaultSettings)
      .select()
      .single();

    if (insertError) {
      throw new Error(
        'Varsayılan ayarlar oluşturulurken hata oluştu: ' + insertError.message
      );
    }

    return newSettings;
  });
}

/**
 * Kullanıcının ayarlarını günceller
 */
export async function upSertUserSettings(
  settingsData: upSertUserSettingsData
): Promise<ApiResponse<UserSettings>> {
  return createAction<UserSettings>(
    async (_, supabase, userId) => {
      const validatedData = upSertUserSettingsSchema.parse(settingsData);

      const updateData = {
        ...validatedData,
        updated_at: new Date().toISOString(),
      };

      const { data: updatedSettings, error: updateError } = await supabase
        .from('user_settings')
        .upsert(updateData)
        .eq('profile_id', userId)
        .select()
        .single();

      if (updateError) {
        throw new Error(
          'Ayarlar güncellenirken hata oluştu: ' + updateError.message
        );
      }

      return updatedSettings;
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

/**
 * Bildirim kategorilerini günceller
 */
export async function updateNotificationCategories(
  categories: NotificationCategories
): Promise<ApiResponse<UserSettings>> {
  return upSertUserSettings({ notification_categories: categories });
}
