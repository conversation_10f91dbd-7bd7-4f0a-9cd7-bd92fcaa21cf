'use server';

import { createAction, createAdminAction } from '../core/core';
import sharp from 'sharp';
import {
  Companies,
  PlatformPackages,
  Profiles,
} from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import {
  validatePhoneNumber,
  formatPhoneNumber,
  validatePhoneUniqueness,
} from '@/lib/utils/phone-utils';
import { createClient } from '@/lib/supabase/server';
import { getAuthenticatedUser } from '@/lib/auth/server-auth';

export interface ManagerSubWithPackageDetails {
  company: Companies;
  package: PlatformPackages;
}
export async function getProfile(): Promise<ApiResponse<Profiles>> {
  const supabase = await createClient();
  const user = await getAuthenticatedUser();

  // If no authenticated user, return unsuccessful response early
  if (!user) {
    return {
      success: false,
      error: 'Unauthenticated',
    };
  }

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  if (!profile) {
    return {
      success: false,
      error: 'Profile not found',
    };
  }

  return {
    success: true,
    data: profile,
  };
}

export async function updateFullname(formData: FormData) {
  const full_name = formData.get('full_name') as string;
  return createAction<{ success: boolean }>(
    async (_, supabase, userId) => {
      const { error } = await supabase
        .from('profiles')
        .update({ full_name })
        .eq('id', userId);

      if (error) {
        throw new Error('Profil güncellenirken hata oluştu: ' + error.message);
      }

      return { success: true };
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

export async function updateEmail(formData: FormData) {
  const email = formData.get('email') as string;

  return createAdminAction<{
    success: boolean;
    message: string;
    requiresVerification?: boolean;
  }>(
    async (_, supabase, userId, adminClient) => {
      // Kullanıcının giriş sağlayıcısını kontrol et
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const isGoogleUser = user.app_metadata?.provider === 'google' || 
                            user.identities?.some(identity => identity.provider === 'google');
        
        if (isGoogleUser) {
          throw new Error('Google hesabınızla giriş yaptığınız için e-posta adresiniz değiştirilemez. E-posta değişikliği için Google hesap ayarlarınızı kullanın.');
        }
      }

      // E-posta validasyonu
      if (!email) {
        throw new Error('E-posta adresi gereklidir');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Geçerli bir e-posta adresi giriniz');
      }

      // E-postanın zaten kayıtlı olup olmadığını kontrol et
      const { data: existingProfile } = await adminClient
        .from('profiles')
        .select('id')
        .eq('email', email)
        .neq('id', userId)
        .single();

      if (existingProfile) {
        throw new Error('Bu e-posta adresi zaten kayıtlı');
      }

      // Supabase auth ile e-posta değişikliği için OTP gönderme
      const { error: authError } = await supabase.auth.updateUser({
        email: email,
      });

      if (authError) {
        throw new Error(
          'E-posta güncellenirken hata oluştu: ' + authError.message
        );
      }

      return {
        success: true,
        message:
          'E-posta adresinize doğrulama kodu gönderildi. Lütfen e-postanızı kontrol edin.',
        requiresVerification: true,
      };
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

export async function verifyEmailAndUpdate(formData: FormData) {
  const email = formData.get('email') as string;
  const token = formData.get('token') as string;

  return createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      if (!email || !token) {
        throw new Error('E-posta ve doğrulama kodu gereklidir');
      }

      // OTP doğrula
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email_change',
      });

      if (error) {
        throw new Error('Doğrulama kodu geçersiz: ' + error.message);
      }

      if (!data.user) {
        throw new Error('E-posta doğrulanamadı');
      }

      // Profiles tablosunu güncelle
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          email,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (updateError) {
        throw new Error(
          'Profil güncellenirken hata oluştu: ' + updateError.message
        );
      }

      return {
        success: true,
        message: 'E-posta başarıyla güncellendi ve doğrulandı',
      };
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

export async function updatePhone(formData: FormData) {
  const phone = formData.get('phone') as string;

  return createAdminAction<{
    success: boolean;
    message: string;
    requiresVerification?: boolean;
  }>(
    async (_, supabase, userId, adminClient) => {
      // Telefon validasyonu
      const phoneValidation = validatePhoneNumber(phone);
      if (!phoneValidation.isValid) {
        throw new Error(phoneValidation.error!);
      }

      // Telefon numarasını formatla
      const { clean: cleanPhone, formatted: formattedPhone } =
        formatPhoneNumber(phone);

      // Telefon numarasının zaten kayıtlı olup olmadığını kontrol et
      const { data: existingProfile } = await adminClient
        .from('profiles')
        .select('id')
        .eq('phone_number', cleanPhone)
        .neq('id', userId)
        .single();

      const uniquenessValidation = validatePhoneUniqueness(
        existingProfile,
        userId
      );
      if (!uniquenessValidation.isValid) {
        throw new Error(uniquenessValidation.error!);
      }

      // Supabase auth ile telefon değişikliği için OTP gönderme
      const { error: authError } = await supabase.auth.updateUser({
        phone: formattedPhone,
      });

      if (authError) {
        throw new Error(
          'Telefon güncellenirken hata oluştu: ' + authError.message
        );
      }

      return {
        success: true,
        message:
          "Telefon numaranıza doğrulama kodu gönderildi. Lütfen SMS'inizi kontrol edin.",
        requiresVerification: true,
      };
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

export async function verifyPhoneAndUpdate(formData: FormData) {
  const phone = formData.get('phone') as string;
  const token = formData.get('token') as string;

  return createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      if (!phone || !token) {
        throw new Error('Telefon numarası ve doğrulama kodu gereklidir');
      }

      // Telefon numarasını formatla
      const { clean: cleanPhone, formatted: formattedPhone } =
        formatPhoneNumber(phone);

      // OTP doğrula
      const { data, error } = await supabase.auth.verifyOtp({
        phone: formattedPhone,
        token,
        type: 'phone_change',
      });

      if (error) {
        throw new Error('Doğrulama kodu geçersiz: ' + error.message);
      }

      if (!data.user) {
        throw new Error('Telefon numarası doğrulanamadı');
      }

      // Profiles tablosunu güncelle
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          phone_number: cleanPhone,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (updateError) {
        throw new Error(
          'Profil güncellenirken hata oluştu: ' + updateError.message
        );
      }

      return {
        success: true,
        message: 'Telefon numarası başarıyla güncellendi ve doğrulandı',
      };
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}

export async function updateProfilePhoto(formData: FormData) {
  return createAdminAction<{ success: boolean; url: string; message?: string }>(
    async (_, supabase, userId, adminClient) => {
      // Fotoğraf dosyasını kontrol et
      const file = formData.get('file') as File | null;

      if (!file) {
        throw new Error('Fotoğraf dosyası gereklidir');
      }

      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error("Dosya boyutu 5MB'dan küçük olmalıdır");
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/')) {
        throw new Error('Sadece resim dosyaları yüklenebilir');
      }

      try {
        // File'ı buffer'a dönüştür
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Sharp ile WebP formatına dönüştür ve boyutlandır
        const webpBuffer = await sharp(buffer)
          .resize(400, 400, {
            fit: 'cover',
            position: 'center',
          })
          .webp({ quality: 92 })
          .toBuffer();

        // Dosya adını oluştur (timestamp ekleyerek cache sorununu önle)
        const timestamp = Date.now();
        const fileName = `${userId}_${timestamp}.webp`;

        // Eski dosyaları temizle
        const { data: existingFiles } = await adminClient.storage
          .from('profile-pictures')
          .list('', {
            search: userId,
          });

        if (existingFiles && existingFiles.length > 0) {
          const filesToDelete = existingFiles.map(f => f.name);
          await adminClient.storage
            .from('profile-pictures')
            .remove(filesToDelete);
        }

        // Yeni dosyayı yükle
        const { error: uploadError } = await adminClient.storage
          .from('profile-pictures')
          .upload(fileName, webpBuffer, {
            contentType: 'image/webp',
            upsert: true,
          });

        if (uploadError) {
          console.error('Storage upload error:', uploadError);
          throw new Error(
            'Dosya yüklenirken hata oluştu: ' + uploadError.message
          );
        }

        // Public URL'i al
        const { data: urlData } = adminClient.storage
          .from('profile-pictures')
          .getPublicUrl(fileName);

        // Cache busting için timestamp ekle
        const avatarUrl = `${urlData.publicUrl}?t=${timestamp}`;

        // Profiles tablosunu güncelle
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            avatar_url: avatarUrl,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          throw new Error(
            'Profil güncellenirken hata oluştu: ' + updateError.message
          );
        }

        return {
          success: true,
          url: avatarUrl,
          message: 'Profil fotoğrafı başarıyla güncellendi',
        };
      } catch (err) {
        console.error('Photo processing error:', err);
        throw new Error('Fotoğraf işlenirken hata oluştu');
      }
    },
    {
      revalidatePaths: ['/profile/settings'],
    }
  );
}
