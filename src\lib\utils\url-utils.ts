/**
 * URL utility functions for consistent URL generation across the application
 */

import { getAppUrl } from '@/lib/env';

/**
 * Get the base site URL from environment variable with fallback
 */
export function getSiteUrl(): string {
  return getAppUrl();
}

/**
 * Generate a full URL by combining site URL with a path
 */
export function getFullUrl(path: string): string {
  const siteUrl = getSiteUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${siteUrl}${cleanPath}`;
}

/**
 * Generate gym page URL
 */
export function getGymUrl(slug: string): string {
  return getFullUrl(`/gym/${slug}`);
}

/**
 * Generate findGym URL with optional city and district
 */
export function getFindGymUrl(
  citySlug?: string,
  districtSlug?: string
): string {
  let path = '/findGym';

  if (citySlug) {
    path += `/${citySlug}`;
    if (districtSlug) {
      path += `/${districtSlug}`;
    }
  }

  return getFullUrl(path);
}

/**
 * Generate search URL with query parameters
 */
export function getSearchUrl(params: Record<string, string>): string {
  const searchParams = new URLSearchParams(params);
  return getFullUrl(`/findGym?${searchParams.toString()}`);
}
