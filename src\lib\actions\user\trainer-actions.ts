'use server';

import { createAction } from '../core/core';
import { validateFormData } from '../core';
import { updateTrainerDetailsSchema } from '../core';
import { ApiResponse } from '@/types/global/api';
import { TrainerDetails } from '@/types/database/tables';

// getTrainerGyms kaldırıldı: user_roles görünümü üzerinden gym erişimleri belirleniyor.
/**
 * Kullanıcının trainer details bilgilerini getirir
 */
export async function getTrainerDetail(
  userId?: string | null
): Promise<ApiResponse<TrainerDetails>> {
  return createAction<TrainerDetails>(async (_, supabase, authUserId) => {
    // userId verilmemişse authenticated user'ı kullan
    const targetUserId = userId || authUserId;
    // Trainer bilgilerini al
    const { data: trainer, error: trainerError } = await supabase
      .from('trainer_details')
      .select('*')
      .eq('profile_id', targetUserId)
      .single();

    if (trainerError) {
      // PGRST116 = no rows returned (normal durum - henüz trainer bilgisi yok)
      if (trainerError.code === 'PGRST116') {
        return trainer;
      }
      throw new Error('Antrenör bilgileri alınamadı: ' + trainerError.message);
    }

    return trainer;
  });
}

/**
 * Kullanıcının trainer details bilgilerini günceller
 */
export async function updateTrainerDetails(
  formData: FormData
): Promise<ApiResponse<TrainerDetails>> {
  return createAction<TrainerDetails>(async (_, supabase, userId) => {
    // Form verilerini doğrula
    const validation = await validateFormData(
      formData,
      updateTrainerDetailsSchema
    );
    if (validation.error) {
      throw new Error(validation.error);
    }

    const { specialization, certification_level, experience_years, bio } =
      validation.data!;

    // Trainer details bilgilerini güncelle
    if (
      specialization !== undefined ||
      certification_level !== undefined ||
      experience_years !== undefined ||
      bio !== undefined
    ) {
      const detailsData: any = {};
      if (specialization !== undefined)
        detailsData.specialization = specialization || null;
      if (certification_level !== undefined)
        detailsData.certification_level = certification_level || null;
      if (experience_years !== undefined)
        detailsData.experience_years = experience_years || null;
      if (bio !== undefined) detailsData.bio = bio || null;

      // Sadece update işlemi - mevcut kayıt yoksa hata fırlatır
      const { data, error: detailsError } = await supabase
        .from('trainer_details')
        .update(detailsData)
        .eq('profile_id', userId)
        .select();

      if (detailsError) {
        throw new Error(
          'Antrenör bilgileri güncellenirken hata oluştu: ' +
            detailsError.message
        );
      }

      // Eğer hiçbir satır güncellenmemişse, kayıt bulunamadı demektir
      if (!data || data.length === 0) {
        throw new Error(
          'Güncellenecek antrenör kaydı bulunamadı. Önce antrenör profili oluşturulmalı.'
        );
      }
    }

    // Güncellenmiş trainer bilgilerini getir
    const trainerResult = await getTrainerDetail();
    if (!trainerResult.success || !trainerResult.data) {
      throw new Error('Güncellenmiş antrenör bilgileri alınamadı');
    }

    return trainerResult.data;
  });
}
