import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';
import {
  signUpWithEmail,
  registerWithPhone,
  verifyPhoneOtp,
} from '@/lib/actions/auth/auth-actions';
import { signUpWithGoogle } from '@/lib/actions/auth/google-auth-actions';
import { RegisterForm } from './register-form';
import { GoogleOAuthButton } from '@/components/auth/google-oauth-button';

export const metadata: Metadata = {
  title: 'Kayıt Ol | Sportiva',
  description: 'Sportiva spor salonu yönetim sistemi kayıt sayfası.',
};

interface RegisterPageProps {
  searchParams: {
    error?: string;
    message?: string;
    identifier_error?: string;
    password_error?: string;
    verification_required?: string;
    phone?: string;
    context?: string; // Error context bilgisi
  };
}

export default async function RegisterPage({
  searchParams,
}: RegisterPageProps) {
  const params = await searchParams;
  const { error, message, identifier_error, password_error, context } = params;

  async function handleEmailRegistration(formData: FormData) {
    'use server';

    const result = await signUpWithEmail(formData);

    return {
      ...result,
      email: (formData.get('email') || formData.get('identifier')) as string,
    };
  }

  async function handlePhoneRegistration(formData: FormData) {
    'use server';
    return await registerWithPhone(formData);
  }

  async function handleVerifyPhoneOtp(formData: FormData) {
    'use server';
    return await verifyPhoneOtp(formData);
  }

  async function handleGoogleRegister() {
    'use server';
    return await signUpWithGoogle();
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-foreground mb-2 text-2xl font-bold lg:text-3xl">
          {AUTH_UI_TEXT.REGISTER_TITLE}
        </h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          {AUTH_UI_TEXT.REGISTER_SUBTITLE}
        </p>
      </div>

      {/* Success Message */}
      {message && (
        <div
          className="flex w-full items-center space-x-3 rounded-lg border border-green-200 bg-green-50 p-4 text-green-700"
          role="alert"
          aria-live="polite"
        >
          <span className="text-sm font-medium">{message}</span>
        </div>
      )}

      {/* Google OAuth Button */}
      <GoogleOAuthButton action={handleGoogleRegister}>
        Google ile Kayıt Ol
      </GoogleOAuthButton>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background text-muted-foreground px-2">Veya</span>
        </div>
      </div>

      {/* Register Form */}
      <RegisterForm
        error={error}
        identifierError={identifier_error}
        passwordError={password_error}
        context={context}
        onEmailRegistration={handleEmailRegistration}
        onPhoneRegistration={handlePhoneRegistration}
        onVerifyPhoneOtp={handleVerifyPhoneOtp}
      />

      {/* Footer */}
      <div className="text-center">
        <span className="text-sm">{AUTH_UI_TEXT.HAVE_ACCOUNT}</span>
        <Link
          href="/auth/login"
          className="text-primary hover:text-primary/80 ml-1 text-sm font-medium hover:underline"
        >
          {AUTH_UI_TEXT.LOGIN_LINK}
        </Link>
      </div>
    </div>
  );
}
