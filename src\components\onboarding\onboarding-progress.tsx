'use client';

import { Check, Crown, CreditCard, Building, Building2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  ManagerOnboardingStep,
  getStepName,
} from '@/lib/utils/onboarding-client-utils';

interface OnboardingProgressProps {
  currentStep: ManagerOnboardingStep;
  completedSteps: ManagerOnboardingStep[];
  className?: string;
}

interface StepConfig {
  step: ManagerOnboardingStep;
  icon: React.ReactNode;
  title: string;
  description: string;
}

const stepConfigs: StepConfig[] = [
  {
    step: ManagerOnboardingStep.ROLE_SELECTION,
    icon: <Crown className="h-5 w-5" />,
    title: 'Rol Seçimi',
    description: 'Şirket oluşturma seçeneğini seçin',
  },
  {
    step: ManagerOnboardingStep.COMPANY_SETUP,
    icon: <Building className="h-5 w-5" />,
    title: '<PERSON>irket Kurulumu',
    description: 'Şirket bilgilerinizi girin',
  },
  {
    step: ManagerOnboardingStep.PAYMENT,
    icon: <CreditCard className="h-5 w-5" />,
    title: 'Ödeme',
    description: 'Abonelik planınızı seçin',
  },
  {
    step: ManagerOnboardingStep.GYM_SETUP,
    icon: <Building2 className="h-5 w-5" />,
    title: 'İlk Şube Kurulumu',
    description: 'İlk şubenizi oluşturun',
  },
];

export function OnboardingProgress({
  currentStep,
  completedSteps,
  className,
}: OnboardingProgressProps) {
  const getCurrentStepIndex = () => {
    return stepConfigs.findIndex(config => config.step === currentStep);
  };

  const isStepCompleted = (step: ManagerOnboardingStep) => {
    return completedSteps.includes(step);
  };

  const isStepCurrent = (step: ManagerOnboardingStep) => {
    return step === currentStep;
  };

  const isStepAccessible = (stepIndex: number) => {
    const currentIndex = getCurrentStepIndex();
    return stepIndex <= currentIndex;
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="mb-2 flex items-center justify-between">
          <span className="text-muted-foreground text-sm font-medium">
            İlerleme
          </span>
          <span className="text-muted-foreground text-sm font-medium">
            {completedSteps.length} / {stepConfigs.length} tamamlandı
          </span>
        </div>
        <div className="bg-muted h-2 w-full rounded-full">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${(completedSteps.length / stepConfigs.length) * 100}%`,
            }}
          />
        </div>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {stepConfigs.map((config, index) => {
          const isCompleted = isStepCompleted(config.step);
          const isCurrent = isStepCurrent(config.step);
          const isAccessible = isStepAccessible(index);

          return (
            <div
              key={config.step}
              className={cn(
                'flex items-start space-x-4 rounded-lg border p-4 transition-all duration-200',
                {
                  'bg-primary/5 border-primary/20': isCurrent,
                  'bg-muted/30 border-muted': !isCurrent && !isCompleted,
                  'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20':
                    isCompleted,
                  'opacity-50': !isAccessible,
                }
              )}
            >
              {/* Step Icon */}
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-full border-2 transition-all duration-200',
                  {
                    'bg-primary border-primary text-primary-foreground':
                      isCurrent,
                    'border-green-500 bg-green-500 text-white': isCompleted,
                    'bg-background border-muted-foreground/30 text-muted-foreground':
                      !isCurrent && !isCompleted,
                    'opacity-50': !isAccessible,
                  }
                )}
              >
                {isCompleted ? <Check className="h-5 w-5" /> : config.icon}
              </div>

              {/* Step Content */}
              <div className="min-w-0 flex-1">
                <div className="flex items-center justify-between">
                  <h3
                    className={cn(
                      'text-sm font-semibold transition-colors duration-200',
                      {
                        'text-primary': isCurrent,
                        'text-green-700 dark:text-green-400': isCompleted,
                        'text-foreground': !isCurrent && !isCompleted,
                        'opacity-50': !isAccessible,
                      }
                    )}
                  >
                    {config.title}
                  </h3>
                  {isCompleted && (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <Check className="mr-1 h-4 w-4" />
                      <span className="text-xs font-medium">Tamamlandı</span>
                    </div>
                  )}
                  {isCurrent && (
                    <div className="text-primary flex items-center">
                      <div className="bg-primary mr-2 h-2 w-2 animate-pulse rounded-full" />
                      <span className="text-xs font-medium">Mevcut Adım</span>
                    </div>
                  )}
                </div>
                <p
                  className={cn(
                    'text-muted-foreground mt-1 text-sm transition-colors duration-200',
                    {
                      'opacity-50': !isAccessible,
                    }
                  )}
                >
                  {config.description}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Compact version for smaller spaces
export function OnboardingProgressCompact({
  currentStep,
  completedSteps,
  className,
}: OnboardingProgressProps) {
  const progress = (completedSteps.length / stepConfigs.length) * 100;

  return (
    <div className={cn('w-full', className)}>
      <div className="mb-2 flex items-center justify-between">
        <span className="text-sm font-medium">{getStepName(currentStep)}</span>
        <span className="text-muted-foreground text-sm">
          {completedSteps.length}/{stepConfigs.length}
        </span>
      </div>
      <div className="bg-muted h-2 w-full rounded-full">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
}
