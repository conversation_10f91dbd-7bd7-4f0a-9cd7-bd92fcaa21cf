'use client';

import { useEffect, useState } from 'react';
import { FontSize } from '@/lib/supabase/types';

export interface LocalSettings {
  font_size: FontSize;
  animations_enabled: boolean;
}

const DEFAULT_SETTINGS: LocalSettings = {
  font_size: 'normal',
  animations_enabled: true,
};

/**
 * Local storage anahtarları
 */
const STORAGE_KEYS = {
  FONT_SIZE: 'user-font-size',
  ANIMATIONS: 'user-animations',
} as const;

/**
 * Local ayarları yöneten hook
 * Font boyutu ve animasyon tercihlerini sadece localStorage ile yönetir
 */
export function useLocalSettings() {
  const [settings, setSettings] = useState<LocalSettings>(DEFAULT_SETTINGS);
  const [isLoaded, setIsLoaded] = useState(false);

  // İlk yüklemede localStorage'dan ayarları oku
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const loadedSettings: LocalSettings = {
        font_size:
          (localStorage.getItem(STORAGE_KEYS.FONT_SIZE) as FontSize) ||
          DEFAULT_SETTINGS.font_size,
        animations_enabled:
          localStorage.getItem(STORAGE_KEYS.ANIMATIONS) === 'true' ||
          (localStorage.getItem(STORAGE_KEYS.ANIMATIONS) === null &&
            DEFAULT_SETTINGS.animations_enabled),
      };

      setSettings(loadedSettings);
      applySettingsToDOM(loadedSettings);
    } catch (error) {
      console.error('Error loading local settings:', error);
      // Hata durumunda default ayarları kullan
      applySettingsToDOM(DEFAULT_SETTINGS);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  /**
   * Ayarları DOM'a uygular
   */
  const applySettingsToDOM = (settingsToApply: LocalSettings) => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    root.setAttribute('data-font-size', settingsToApply.font_size);
    root.setAttribute(
      'data-animations',
      settingsToApply.animations_enabled.toString()
    );
  };

  /**
   * Font boyutunu günceller
   */
  const updateFontSize = (fontSize: FontSize) => {
    const newSettings = { ...settings, font_size: fontSize };
    setSettings(newSettings);

    // localStorage'ı güncelle (sadece client-side'da)
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEYS.FONT_SIZE, fontSize);
      } catch (error) {
        console.error('Error saving font size to localStorage:', error);
      }
    }

    // DOM'a uygula
    applySettingsToDOM(newSettings);
  };

  /**
   * Animasyon tercihini günceller
   */
  const updateAnimations = (enabled: boolean) => {
    const newSettings = { ...settings, animations_enabled: enabled };
    setSettings(newSettings);

    // localStorage'ı güncelle (sadece client-side'da)
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEYS.ANIMATIONS, enabled.toString());
      } catch (error) {
        console.error(
          'Error saving animations setting to localStorage:',
          error
        );
      }
    }

    // DOM'a uygula
    applySettingsToDOM(newSettings);
  };

  return {
    settings,
    isLoaded,
    updateFontSize,
    updateAnimations,
    applySettingsToDOM,
  };
}

/**
 * Uygulama başlangıcında ayarları hızlıca uygular
 * Layout veya app bileşeninde çağrılmalıdır
 */
export function initializeLocalSettings() {
  if (typeof window === 'undefined') return;

  try {
    // Flash'ı önlemek için hızlıca ayarları uygula
    const fontSize =
      localStorage.getItem(STORAGE_KEYS.FONT_SIZE) ||
      DEFAULT_SETTINGS.font_size;
    const animations = localStorage.getItem(STORAGE_KEYS.ANIMATIONS);
    const animationsEnabled =
      animations === 'true' ||
      (animations === null && DEFAULT_SETTINGS.animations_enabled);

    const root = document.documentElement;
    root.setAttribute('data-font-size', fontSize);
    root.setAttribute('data-animations', animationsEnabled.toString());
  } catch (error) {
    console.error('Error initializing local settings:', error);
    // Hata durumunda default ayarları uygula
    const root = document.documentElement;
    root.setAttribute('data-font-size', DEFAULT_SETTINGS.font_size);
    root.setAttribute(
      'data-animations',
      DEFAULT_SETTINGS.animations_enabled.toString()
    );
  }
}
