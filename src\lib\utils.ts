import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Tailwind + clsx birleştirici
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Genel amaçlı cache/memoize yardımcıları
 */
const memoize = <V>(factory: () => V) => {
  let cached: V | undefined;
  return () => {
    if (cached === undefined) cached = factory();
    return cached;
  };
};

const getTryNumberFormat = memoize(() => {
  try {
    return new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' });
  } catch {
    return undefined;
  }
});

const getDateTimeFormatter = memoize(() => {
  try {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch {
    return undefined;
  }
});

const getDateTimeWithTimeFormatter = memoize(() => {
  try {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  } catch {
    return undefined;
  }
});

const getDateShortFormatter = memoize(() => {
  try {
    return new Intl.DateTimeFormat('tr-TR', {
      day: 'numeric',
      month: 'short',
    });
  } catch {
    return undefined;
  }
});

const getTimeFormatter = memoize(() => {
  try {
    return new Intl.DateTimeFormat('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  } catch {
    return undefined;
  }
});

const getRelativeTimeFormatter = memoize(() => {
  try {
    return new Intl.RelativeTimeFormat('tr-TR', { numeric: 'auto' });
  } catch {
    return undefined;
  }
});

/**
 * Tek noktadan güvenli tarih ayrıştırma
 */
export type ParsableDate = string | number | Date | null | undefined;

export function parseDate(input: ParsableDate): Date | null {
  if (input == null) return null;
  if (input instanceof Date) return isNaN(input.getTime()) ? null : input;
  // number (timestamp) veya string
  const d = new Date(input);
  return isNaN(d.getTime()) ? null : d;
}

/**
 * TRY para formatı
 */
export function formatCurrency(amount: number): string {
  const nf = getTryNumberFormat();
  if (!isFinite(amount) || nf === undefined) {
    // son çare basit fallback
    return `${amount.toFixed ? amount.toFixed(2) : amount} TL`;
  }
  return nf.format(amount);
}

/**
 * Tarih - "15 Ocak 2024"
 */
export function formatDate(
  value: ParsableDate,
  fallback: string = 'Belirtilmemiş'
): string {
  const d = parseDate(value);
  if (!d) return fallback;

  const fmt = getDateTimeFormatter();
  return fmt ? fmt.format(d) : d.toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });
}

/**
 * Tarih + Saat - "15 Ocak 2024 14:30"
 */
export function formatDateTime(
  value: ParsableDate,
  fallback: string = 'Belirtilmemiş'
): string {
  const d = parseDate(value);
  if (!d) return fallback;

  const fmt = getDateTimeWithTimeFormatter();
  if (fmt) return fmt.format(d);

  // Fallback
  return d.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }) + ' ' + d.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit', hour12: false });
}

/**
 * Kısa tarih - "15 Oca"
 */
export function formatDateShort(
  value: ParsableDate,
  fallback: string = '-'
): string {
  const d = parseDate(value);
  if (!d) return fallback;

  const fmt = getDateShortFormatter();
  return fmt ? fmt.format(d) : d.toLocaleDateString('tr-TR', { day: 'numeric', month: 'short' });
}

/**
 * Sadece saat (HH:mm)
 */
export function formatTime(time: string | ParsableDate): string {
  // "HH:mm" gibi düz saat stringi gelirse dummy tarih ekle
  const value =
    typeof time === 'string' && !time.includes('T') && !time.includes(' ')
      ? `2000-01-01T${time}`
      : time;

  const d = parseDate(value);
  if (!d) return typeof time === 'string' ? time : '-';

  const fmt = getTimeFormatter();
  return fmt ? fmt.format(d) : d.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit', hour12: false });
}

/**
 * Göreli zaman - Intl.RelativeTimeFormat ile
 * Ör: "Az önce", "5 dk önce", "3 sa önce", "2 gün önce", "4 ay önce", "1 yıl önce"
 * 24 saatten eski tarihleri daha geniş bir ölçekle ifade eder.
 */
export function formatRelativeTime(value: ParsableDate): string {
  const d = parseDate(value);
  if (!d) return 'Bilinmiyor';

  const now = new Date();
  const diffMs = d.getTime() - now.getTime(); // geçmiş için negatif
  const diffSec = Math.round(diffMs / 1000);
  const rtf = getRelativeTimeFormatter();

  // İnsan dostu eşikler
  const absSec = Math.abs(diffSec);
  if (absSec < 60) return 'Az önce';

  const diffMin = Math.round(diffSec / 60);
  if (Math.abs(diffMin) < 60) {
    return rtf ? rtf.format(diffMin, 'minute') : `${Math.abs(diffMin)} dk ${diffMin < 0 ? 'önce' : 'sonra'}`;
  }

  const diffHour = Math.round(diffMin / 60);
  if (Math.abs(diffHour) < 24) {
    return rtf ? rtf.format(diffHour, 'hour') : `${Math.abs(diffHour)} sa ${diffHour < 0 ? 'önce' : 'sonra'}`;
  }

  const diffDay = Math.round(diffHour / 24);
  if (Math.abs(diffDay) < 30) {
    return rtf ? rtf.format(diffDay, 'day') : `${Math.abs(diffDay)} gün ${diffDay < 0 ? 'önce' : 'sonra'}`;
  }

  const diffMonth = Math.round(diffDay / 30);
  if (Math.abs(diffMonth) < 12) {
    return rtf ? rtf.format(diffMonth, 'month') : `${Math.abs(diffMonth)} ay ${diffMonth < 0 ? 'önce' : 'sonra'}`;
  }

  const diffYear = Math.round(diffMonth / 12);
  return rtf ? rtf.format(diffYear, 'year') : `${Math.abs(diffYear)} yıl ${diffYear < 0 ? 'önce' : 'sonra'}`;
}
