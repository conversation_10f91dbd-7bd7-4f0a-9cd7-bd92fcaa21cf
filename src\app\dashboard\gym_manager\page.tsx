import { getGymManagerGyms, getGymManagerStats } from '@/lib/actions/dashboard/gym-manager/gym-manager-actions';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Settings,
  Crown,
  MapPin,
  Phone,
  Users,
  DollarSign,
  Calendar,
} from 'lucide-react';
import Link from 'next/link';

export default async function GymManagerPage() {
  const [gymsResult, statsResult] = await Promise.all([
    getGymManagerGyms(),
    getGymManagerStats(),
  ]);

  const managedGyms = gymsResult.success ? gymsResult.data || [] : [];
  const stats = statsResult.success && statsResult.data ? statsResult.data : null;

    if (managedGyms.length === 0) {
      return (
        <div className="py-12 text-center">
          <Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h2 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
            Henüz Yönettiğiniz Salon Yok
          </h2>
          <p className="mb-6 text-gray-600 dark:text-gray-400">
            Bir şirket size salon yöneticisi daveti gönderdiğinde burada
            görünecektir.
          </p>
          <Button variant="outline" asChild>
            <Link href="/dashboard/member">Üye Paneline Dön</Link>
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-2 text-2xl font-bold text-gray-900 dark:text-white">
              <Crown className="text-primary h-6 w-6" />
              Salon Yöneticisi Paneli
            </h1>
            <p className="mt-1 text-gray-600 dark:text-gray-400">
              Yönettiğiniz salonları görüntüleyin ve yönetin
            </p>
          </div>
        </div>

        {/* Stats Section */}
        {stats && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Salon
                </CardTitle>
                <Building2 className="text-muted-foreground h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalGyms}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Üye
                </CardTitle>
                <Users className="text-muted-foreground h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalMembers}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Gelir
                </CardTitle>
                <DollarSign className="text-muted-foreground h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ₺{stats.totalRevenue.toLocaleString()}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Bugünkü Randevular
                </CardTitle>
                <Calendar className="text-muted-foreground h-4 w-4" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.todayAppointments}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Managed Gyms */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {managedGyms.map(gym => (
            <Card key={gym.id} className="transition-shadow hover:shadow-lg">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Building2 className="text-primary h-5 w-5" />
                      {gym.name}
                    </CardTitle>
                  </div>
                  <Badge
                    variant={gym.status === 'active' ? 'default' : 'secondary'}
                  >
                    {gym.status === 'active' ? 'Aktif' : 'Pasif'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2 text-sm">
                  {gym.address && (
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <MapPin className="h-4 w-4" />
                      <span>
                        {gym.address}
                        {gym.district &&
                          gym.city &&
                          `, ${gym.district}/${gym.city}`}
                      </span>
                    </div>
                  )}
                  {gym.gym_phone && (
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <Phone className="h-4 w-4" />
                      <span>{gym.gym_phone}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button asChild size="sm" className="flex-1">
                    <Link href={`/dashboard/gym/${gym.id}`}>Yönet</Link>
                  </Button>
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/dashboard/gym/${gym.id}/settings`}>
                      <Settings className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
}
