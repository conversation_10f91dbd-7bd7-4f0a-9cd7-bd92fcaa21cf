"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useRouter, useSearchParams, usePathname } from "next/navigation";

export function AppointmentsViewSwitcher() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const current = (searchParams.get("view") === "calendar") ? "calendar" : "list";

  const setView = (view: "list" | "calendar") => {
    const sp = new URLSearchParams(searchParams.toString());
    if (view === "list") sp.delete("view"); else sp.set("view", "calendar");
    router.push(`${pathname}?${sp.toString()}`);
  };

  return (
    
             <Tabs defaultValue={current} className="mx-auto">
      <TabsList className="mx-auto w-full max-w-[420px] h-11 p-1 rounded-2xl shadow-sm text-base">
        <TabsTrigger value="list" onClick={() => setView("list")}>Liste</TabsTrigger>
        <TabsTrigger value="calendar" onClick={() => setView("calendar")}>Takvim</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}

     
