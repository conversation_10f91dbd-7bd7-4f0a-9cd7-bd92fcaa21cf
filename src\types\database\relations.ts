/**
 * Database relation types
 *
 * Types for joined queries and relationships between tables
 * Extracted from existing component usage patterns
 */

import type { GymMemberships, Profiles, Gyms, GymPackages } from './tables';

/**
 * Common relationship types for joined queries
 */

/**
 * Membership with member profile (database relation)
 */
export type DatabaseMembershipWithMember = GymMemberships & {
  member: Profiles | null;
};

/**
 * Membership with gym details
 */
export type MembershipWithGym = GymMemberships & {
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
};

/**
 * Gym with membership statistics
 */
export type GymWithStats = Gyms & {
  memberCount: number;
  activeMembers: number;
  trainerCount: number;
};

/**
 * Package with gym information
 */
export type PackageWithGym = GymPackages & {
  gym: {
    id: string;
    name: string;
  };
};

/**
 * Gym with member details for stats
 */
export interface GymMemberStats {
  total: number;
  active: number;
  passive: number;
  pending: number;
}

/**
 * Activity item for recent activity displays
 */
export interface ActivityItem {
  id: string;
  type: 'member_joined' | 'package_purchased' | 'revenue';
  title: string;
  description: string;
  timestamp: string;
  amount?: number;
  status?: string;
  icon: React.ComponentType<{ className?: string }>;
}
