/**
 * Profile feature types
 *
 * Extracted from profile components and settings
 */

import type {
  ProfileVisibility,
  FontSize,
  NotificationCategories,
} from '../../database/enums';

/**
 * Profile update data
 */
export interface ProfileUpdateData {
  fullName?: string;
  avatar?: File;
  phone?: string;
  dateOfBirth?: string;
  bio?: string;
  location?: string;
  website?: string;
}

/**
 * Profile settings data
 */
export interface ProfileSettings {
  visibility: ProfileVisibility;
  fontSize: FontSize;
  notifications: NotificationCategories;
  language: string;
  timezone: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
}

/**
 * Profile privacy settings
 */
export interface PrivacySettings {
  showEmail: boolean;
  showPhone: boolean;
  showLocation: boolean;
  showBirthDate: boolean;
  allowMessages: boolean;
  allowInvitations: boolean;
  showOnlineStatus: boolean;
}

/**
 * Profile security settings
 */
export interface SecuritySettings {
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  sessionTimeout: number;
  allowedDevices: string[];
  lastPasswordChange: string;
  securityQuestions: {
    question: string;
    answer: string;
  }[];
}

/**
 * Profile activity data
 */
export interface ProfileActivity {
  lastLogin: string;
  lastProfileUpdate: string;
  loginCount: number;
  profileViews: number;
  recentActivities: {
    type: string;
    description: string;
    timestamp: string;
    metadata?: Record<string, any>;
  }[];
}

/**
 * Profile completion status
 */
export interface ProfileCompletion {
  percentage: number;
  missingFields: string[];
  suggestions: {
    field: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}
