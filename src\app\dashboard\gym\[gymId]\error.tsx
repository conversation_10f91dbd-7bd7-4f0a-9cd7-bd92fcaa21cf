"use client";

import { RouteError } from "@/components/errors/route-error";

export default function GymDetailError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Salon Detayı Hatası"
      description="Salon detayları yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: "/dashboard/gym/[gymId]" }}
    />
  );
}

