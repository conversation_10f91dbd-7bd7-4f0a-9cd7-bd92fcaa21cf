import { GymDashboardStats } from './components/gym-dashboard-stats';
import { GymPageHeader } from './components/gym-page-header';
import { GymQuickActions } from './components/gym-quick-actions';
import { GymRecentActivity } from './components/gym-recent-activity';
import { GymNotifications } from './components/gym-notifications';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';

export default async function GymPage({
  params,
}: {
  params: { gymId: string };
}) {
  const { gymId } = await params;
  const { data, error } = await getGymById(gymId);

  if (error || !data) {
    console.error('Gym fetch error:', error);
    throw new Error('Salon bulunamadı');
  }

  return (
      <section className="grid lg:grid-cols-12 gap-4">
        {/* Sol Taraf - Salon Bilgileri */}
        <div className="lg:col-span-7 gap-4 grid">
          <GymPageHeader gym={data} />
          <GymDashboardStats gym={data} />
          <GymQuickActions gym={data} />
        </div> 
        {/* Sağ Taraf - Son Aktiviteler */}
        <div className="lg:col-span-5 gap-4 grid">
          <GymRecentActivity gym={data} />
          <GymNotifications gymId={gymId} />
        </div>
      </section>
  );
}
