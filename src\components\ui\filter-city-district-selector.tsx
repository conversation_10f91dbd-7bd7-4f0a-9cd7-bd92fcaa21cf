'use client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CITIES } from '@/lib/constants/cityConstants';

interface FilterCityDistrictSelectorProps {
  selectedCity: string;
  selectedDistrict: string;
  onCityChange: (city: string) => void;
  onDistrictChange: (district: string) => void;
  cityLabel?: string;
  districtLabel?: string;
  cityPlaceholder?: string;
  districtPlaceholder?: string;
  disabled?: boolean;
  className?: string;
  triggerClassName?: string;
}

export function FilterCityDistrictSelector({
  selectedCity,
  selectedDistrict,
  onCityChange,
  onDistrictChange,
  cityLabel = 'Şehir',
  districtLabel = 'İlçe',
  cityPlaceholder = 'Şehir seçin',
  districtPlaceholder = 'İlçe seçin',
  disabled = false,
  className = '',
  triggerClassName = '',
}: FilterCityDistrictSelectorProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      {/* City Filter */}
      <div className="space-y-1">
        <label className="text-muted-foreground text-xs font-medium">
          {cityLabel}
        </label>
        <Select
          value={selectedCity}
          onValueChange={onCityChange}
          disabled={disabled}
        >
          <SelectTrigger className={triggerClassName}>
            <SelectValue placeholder={cityPlaceholder} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm Şehirler</SelectItem>
            {CITIES.map(city => (
              <SelectItem key={city.id} value={city.id.toString()}>
                {city.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* District Filter */}
      <div className="space-y-1">
        <label className="text-muted-foreground text-xs font-medium">
          {districtLabel}
        </label>
        <Select
          value={selectedDistrict}
          onValueChange={onDistrictChange}
          disabled={selectedCity === 'all' || disabled}
        >
          <SelectTrigger className={`${triggerClassName} disabled:opacity-50`}>
            <SelectValue placeholder={districtPlaceholder} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm İlçeler</SelectItem>
            {CITIES.find(
              city => city.id.toString() === selectedCity
            )?.districts.map(district => (
              <SelectItem key={district} value={district}>
                {district}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
