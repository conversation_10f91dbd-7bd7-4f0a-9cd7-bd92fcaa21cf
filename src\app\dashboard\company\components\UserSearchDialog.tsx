'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Search, Loader2, UserPlus, Check, Users } from 'lucide-react';
import { toast } from 'sonner';
import { useDebounce } from '@/hooks/use-debounce';

import Image from 'next/image';
import { sendGymMemberInvite } from '@/lib/actions/gym_invitations/invitation-actions';
import { UserSearchResult } from '@/lib/actions/gym_invitations/invitation-types';
import { searchUsers } from '@/lib/actions/gym_invitations/user-search-service';
// UI Component Props
export interface UserSearchDialogProps {
  gymId: string;
  isOpen: boolean;
  onClose: () => void;
  onInviteSent: () => void;
}
export function UserSearchDialog({
  gymId,
  isOpen,
  onClose,
  onInviteSent,
}: UserSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [invitingUsers, setInvitingUsers] = useState<Set<string>>(new Set());
  const [invitedUsers, setInvitedUsers] = useState<Set<string>>(new Set());

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Search users function
  const performSearch = useCallback(
    async (term: string) => {
      if (!term.trim()) {
        setSearchResults([]);
        setHasSearched(false);
        return;
      }

      setIsSearching(true);
      try {
        const result = await searchUsers(term, gymId);

        if (result.success) {
          setSearchResults(result.data || []);
          setHasSearched(true);
        } else {
          toast.error(result.error || 'Arama sırasında bir hata oluştu');
          setSearchResults([]);
        }
      } catch (error) {
        console.error('Search error:', error);
        toast.error('Arama sırasında bir hata oluştu');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [gymId]
  );

  // Effect for debounced search
  useEffect(() => {
    performSearch(debouncedSearchTerm);
  }, [debouncedSearchTerm, performSearch]);

  // Invite user function
  const handleInviteUser = async (userId: string) => {
    setInvitingUsers(prev => new Set([...prev, userId]));

    try {
        /**
         * Response from sending a member invite to the given user.
         * @type {Promise<ApiResponse<any>>}
         */
      const result = await sendGymMemberInvite(gymId, userId);

      if (result.success) {
        toast.success('Kullanıcı başarıyla davet edildi');
        setInvitedUsers(prev => new Set([...prev, userId]));
        onInviteSent();
      } else {
        toast.error(result.error || 'Davet gönderilirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Invite error:', error);
      toast.error('Davet gönderilirken bir hata oluştu');
    } finally {
      setInvitingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  // Reset state when dialog closes
  const handleClose = () => {
    setSearchTerm('');
    setSearchResults([]);
    setHasSearched(false);
    setInvitingUsers(new Set());
    setInvitedUsers(new Set());
    onClose();
  };
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Mevcut Kullanıcı Davet Et
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            <Input
              placeholder="Kullanıcı adı veya e-posta ile ara..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10"
            />
            {isSearching && (
              <Loader2 className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 transform animate-spin text-gray-400" />
            )}
          </div>

          {/* Search Results */}
          <div className="max-h-[400px] min-h-[300px] overflow-y-auto">
            {isSearching && !hasSearched ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="text-primary h-8 w-8 animate-spin" />
              </div>
            ) : hasSearched && searchResults.length === 0 ? (
              <div className="py-12 text-center">
                <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Kullanıcı bulunamadı
                </h3>
                <p className="text-gray-600">
                  Arama kriterlerinize uygun kullanıcı bulunamadı.
                </p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-2">
                {searchResults.map(user => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        {user.avatar_url ? (
                          <Image
                            src={user.avatar_url}
                            alt={user.full_name}
                            className="h-full w-full object-cover"
                            width={70}
                            height={70}
                          />
                        ) : (
                          <AvatarFallback>
                            {user.full_name
                              .split(' ')
                              .map(n => n[0])
                              .join('')
                              .toUpperCase()}
                          </AvatarFallback>
                        )}
                      </Avatar>

                      <div>
                        <div className="font-medium">{user.full_name}</div>
                      </div>
                    </div>

                    <Button
                      onClick={() => handleInviteUser(user.id)}
                      disabled={
                        invitingUsers.has(user.id) || invitedUsers.has(user.id)
                      }
                      variant={
                        invitedUsers.has(user.id) ? 'outline' : 'default'
                      }
                      size="sm"
                    >
                      {invitingUsers.has(user.id) ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : invitedUsers.has(user.id) ? (
                        <Check className="mr-2 h-4 w-4" />
                      ) : (
                        <UserPlus className="mr-2 h-4 w-4" />
                      )}
                      {invitedUsers.has(user.id) ? 'Davet Edildi' : 'Davet Et'}
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-12 text-center">
                <Search className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="text-primary mb-2 text-lg font-medium">
                  Kullanıcı Ara
                </h3>
                <p className="text-muted-foreground">
                  Davet etmek istediğiniz kullanıcının adını veya e-posta
                  adresini yazın.
                </p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
