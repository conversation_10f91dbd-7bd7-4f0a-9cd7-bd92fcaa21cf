import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { AnimatedSection } from '@/components/ui/animated-section'
import { Shield, TrendingUp, Building2, <PERSON>rk<PERSON>, ArrowRight, Play } from 'lucide-react'

export function PremiumHero() {
  return (
    <section className="relative isolate overflow-hidden bg-gradient-to-br from-background via-primary/5 to-background">
      {/* Enhanced background layers */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/10" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,hsl(var(--primary)/0.15),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,hsl(var(--secondary)/0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-grid-pattern opacity-30" />
      </div>

      <div className="container mx-auto px-4 pb-20 pt-24 md:pt-32">
        <div className="mx-auto max-w-7xl">
          <div className="grid items-center gap-16 lg:grid-cols-2">
            {/* Left */}
            <div className="text-center lg:text-left">
              <AnimatedSection animation="fade-up" delay={250}>
                {/* Premium badge */}
                <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-4 py-2 text-sm font-medium text-primary backdrop-blur-sm">
                  <Sparkles className="h-4 w-4" />
                  <span>Kurumsal Çözüm</span>
                </div>

                <h1 className="text-balance text-5xl font-extrabold leading-tight tracking-tight md:text-6xl lg:text-7xl">
                  <span className="bg-gradient-to-r from-foreground via-foreground to-foreground/80 bg-clip-text text-transparent">
                    Kurumsal Düzeyde
                  </span>
                  <span className="block bg-gradient-to-r from-primary via-primary to-primary/60 bg-clip-text text-transparent">
                    Spor Salonu Yönetimi
                  </span>
                </h1>
                <p className="mx-auto mt-6 max-w-[65ch] text-pretty text-xl text-muted-foreground md:text-2xl lg:mx-0 lg:leading-relaxed">
                  Çoklu şube yönetimi, gelişmiş raporlama ve güvenli altyapı ile operasyonel
                  verimliliğinizi katlayın.
                </p>
              </AnimatedSection>

              <AnimatedSection animation="fade-up" delay={400}>
                <div className="mt-10 flex flex-col items-center gap-4 sm:flex-row sm:justify-center lg:justify-start">
                  <Button asChild size="lg" className="group h-12 px-8 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                    <Link href="/onboarding" aria-label="Planları gör">
                      Planları Gör
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="group h-12 px-8 text-base font-semibold border-2 hover:bg-primary/5">
                    <Link href="/features" aria-label="Demo izle">
                      <Play className="mr-2 h-4 w-4" />
                      Demo İzle
                    </Link>
                  </Button>
                </div>

                {/* Enhanced Features */}
                <div className="mt-10 grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-gradient-to-r from-card/80 to-card/60 p-5 backdrop-blur transition-all duration-300 hover:border-primary/40 hover:shadow-lg hover:-translate-y-1">
                    <div className="rounded-lg bg-primary/10 p-2 group-hover:bg-primary/20 transition-colors">
                      <Shield className="size-5 text-primary" />
                    </div>
                    <span className="text-sm font-semibold">KVKK uyumlu güvenlik</span>
                  </div>
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-gradient-to-r from-card/80 to-card/60 p-5 backdrop-blur transition-all duration-300 hover:border-primary/40 hover:shadow-lg hover:-translate-y-1">
                    <div className="rounded-lg bg-primary/10 p-2 group-hover:bg-primary/20 transition-colors">
                      <TrendingUp className="size-5 text-primary" />
                    </div>
                    <span className="text-sm font-semibold">İleri seviye analitik</span>
                  </div>
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-gradient-to-r from-card/80 to-card/60 p-5 backdrop-blur transition-all duration-300 hover:border-primary/40 hover:shadow-lg hover:-translate-y-1">
                    <div className="rounded-lg bg-primary/10 p-2 group-hover:bg-primary/20 transition-colors">
                      <Building2 className="size-5 text-primary" />
                    </div>
                    <span className="text-sm font-semibold">Çoklu şube yönetimi</span>
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Enhanced Right mockup */}
            <AnimatedSection className="relative" animation="slide-left" delay={250}>
              <div className="relative mx-auto w-full max-w-2xl">
                {/* Floating elements for visual appeal */}
                <div className="absolute -top-4 -right-4 h-20 w-20 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 blur-xl" />
                <div className="absolute -bottom-4 -left-4 h-16 w-16 rounded-full bg-gradient-to-br from-secondary/20 to-primary/20 blur-xl" />

                <div className="relative rounded-3xl border border-primary/20 bg-gradient-to-br from-card/90 to-card/70 p-6 shadow-2xl backdrop-blur-sm">
                  <div className="rounded-2xl border border-border/50 bg-background/95 p-6 shadow-inner">
                    {/* Header */}
                    <div className="mb-6 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-3 w-3 rounded-full bg-red-400" />
                        <div className="h-3 w-3 rounded-full bg-yellow-400" />
                        <div className="h-3 w-3 rounded-full bg-green-400" />
                      </div>
                      <div className="text-xs text-muted-foreground">Sportiva Dashboard</div>
                    </div>

                    {/* Dashboard mockup */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-3">
                        <div className="h-16 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20" />
                        <div className="h-16 rounded-lg bg-gradient-to-br from-secondary/20 to-secondary/10 border border-secondary/20" />
                        <div className="h-16 rounded-lg bg-gradient-to-br from-accent/20 to-accent/10 border border-accent/20" />
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="h-20 rounded-lg bg-gradient-to-br from-muted/80 to-muted/60 border border-border/50" />
                        <div className="h-20 rounded-lg bg-gradient-to-br from-muted/80 to-muted/60 border border-border/50" />
                      </div>
                      <div className="h-12 rounded-lg bg-gradient-to-r from-primary/30 via-primary/20 to-primary/10 border border-primary/30" />
                    </div>

                    {/* Progress indicator */}
                    <div className="mt-6 flex items-center gap-3">
                      <div className="h-2 flex-1 rounded-full bg-muted">
                        <div className="h-2 w-3/4 rounded-full bg-gradient-to-r from-primary to-primary/80" />
                      </div>
                      <span className="text-xs text-muted-foreground">75% Complete</span>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  )
}
