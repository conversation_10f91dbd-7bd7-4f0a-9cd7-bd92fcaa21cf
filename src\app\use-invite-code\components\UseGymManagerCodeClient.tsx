'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { Label } from '@/components/ui/label';
import { ArrowRight, ArrowLeft, UserPlus, Info, Ticket, Building2, CheckCircle, AlertTriangle } from 'lucide-react';
import { redeemInvitationCode } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';

export function UseGymManagerCodeClient() {
  const router = useRouter();
  const [inviteCode, setInviteCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleComplete = async () => {
    if (!inviteCode.trim()) return;

    setIsProcessing(true);
    setError(''); // Önceki hataları temizle

    try {
      // Server action ile davet kodunu kullan ve kullanıcıyı salon yöneticisi olarak kaydet
            const result = await redeemInvitationCode({
        inviteCode: inviteCode.trim(),
      });

      if (result.success) {
        setSuccess(true);
        // Başarılı olursa dashboard&apos;a yönlendir
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setError('Geçersiz davet kodu: ' + (result.error || 'Bilinmeyen hata'));
      }
    } catch (error) {
      console.error('Error:', error);
      setError('İşlem sırasında hata oluştu');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <div className="mx-auto max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <Ticket className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Salon Yöneticisi Davet Kodu
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-xl mx-auto">
            Şirket sahibinden aldığınız davet kodunu girin ve salon yöneticisi olarak sisteme katılın.
          </p>
        </div>

        {/* Davet Kodu Formu */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-primary" />
              Davet Kodu Girişi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Bilgi Uyarısı */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Şirket sahibinden aldığınız 8 karakterlik davet kodunu girin. Bu kod ile belirli bir salona yönetici olarak atanacaksınız.
              </AlertDescription>
            </Alert>

            {/* Davet Kodu Input */}
            <div className="space-y-2">
              <Label htmlFor="inviteCode">Davet Kodu *</Label>
              <input
                id="inviteCode"
                type="text"
                placeholder="Örn: 26711155"
                value={inviteCode}
                onChange={(e) => setInviteCode(e.target.value)}
                maxLength={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            {/* Hata Mesajı */}
            {error && (
              <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
                <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
                <AlertDescription>
                  <p className="font-medium text-red-800 dark:text-red-200">
                    {error}
                  </p>
                </AlertDescription>
              </Alert>
            )}

            {/* Başarı Mesajı */}
            {success && (
              <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/10">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertDescription>
                  <p className="font-medium text-green-800 dark:text-green-200">
                    Başarılı! Salon yöneticisi olarak kaydedildiniz.
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Dashboard&apos;a yönlendiriliyorsunuz...
                  </p>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Yardım Bilgisi */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Davet Kodu Nasıl Alınır?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                Şirket sahibi size bir davet kodu gönderir
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                Bu kod 8 karakterlik benzersiz bir koddur
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                Kod belirli bir salon için geçerlidir
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                Kod tek kullanımlıktır ve sınırlı sürelidir
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Geri
          </Button>

          <Button
            onClick={handleComplete}
            disabled={!inviteCode.trim() || isProcessing}
            size="lg"
            className="px-8 py-3 text-lg font-semibold"
          >
            {isProcessing ? 'İşleniyor...' : success ? 'Tamamlandı!' : 'Onayla ve Tamamla'}
            {!isProcessing && !success && <ArrowRight className="ml-2 h-5 w-5" />}
            {success && <CheckCircle className="ml-2 h-5 w-5" />}
          </Button>
        </div>
      </div>
    </div>
  );
}
