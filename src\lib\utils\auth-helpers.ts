import { redirect } from 'next/navigation';
import { LOGIN_ERRORS, REGISTER_ERRORS } from '@/lib/constants/auth';

/**
 * Get target redirect URL after successful login
 */
export function getLoginRedirectUrl(redirectTo?: string): string {
  return redirectTo && redirectTo.startsWith('/dashboard')
    ? redirectTo
    : '/dashboard';
}

/**
 * <PERSON>le login redirect with error
 */
export function redirectWithError(error: string): never {
  redirect(`/auth/login?error=${encodeURIComponent(error)}`);
}

/**
 * Check if error is a Next.js redirect error
 */
export function isRedirectError(error: unknown): boolean {
  return error instanceof Error && error.message === 'NEXT_REDIRECT';
}

/**
 * Generic login handler for both email and phone
 */
export async function handleLoginResult(
  result: { success: boolean; error?: string | null },
  redirectTo: string | undefined,
  fallbackError: string
): Promise<never> {
  if (!result.success) {
    redirectWithError(result.error || fallbackError);
  }

  // Successful login - redirect to intended page or dashboard
  const targetUrl = getLoginRedirectUrl(redirectTo);
  redirect(targetUrl);
}

/**
 * Handle login errors with proper error checking
 */
export function handleLoginError(error: unknown, context: string): never {
  // Check if this is a redirect error (which is expected)
  if (isRedirectError(error)) {
    throw error; // Re-throw redirect errors
  }

  console.error(`${context} server action error:`, error);
  redirectWithError(LOGIN_ERRORS.UNEXPECTED_ERROR);
}

// Register-specific helpers
/**
 * Handle register redirect with error
 */
export function redirectWithRegisterError(error: string): never {
  redirect(`/auth/register?error=${encodeURIComponent(error)}`);
}

/**
 * Handle register result with verification support
 */
export async function handleRegisterResult(
  result: {
    success: boolean;
    error?: string | null;
    requiresVerification?: boolean;
  },
  formData: FormData,
  registerType: 'email' | 'phone',
  fallbackError: string
): Promise<never> {
  if (!result.success) {
    redirectWithRegisterError(result.error || fallbackError);
  }

  if (result.requiresVerification) {
    const fieldName = registerType === 'email' ? 'email' : 'phone';
    const value = formData.get(fieldName) as string;
    const verifyPath =
      registerType === 'email' ? '/auth/verify-email' : '/auth/verify-phone';
    redirect(`${verifyPath}?${fieldName}=${encodeURIComponent(value)}`);
  }

  // Successful registration - redirect to onboarding
  redirect('/onboarding');
}

/**
 * Handle register errors with proper error checking
 */
export function handleRegisterError(error: unknown, context: string): never {
  // Check if this is a redirect error (which is expected)
  if (isRedirectError(error)) {
    throw error; // Re-throw redirect errors
  }

  console.error(`${context} server action error:`, error);
  redirectWithRegisterError(REGISTER_ERRORS.UNEXPECTED_ERROR);
}

// Unified auth handlers
/**
 * Unified login handler that determines email vs phone automatically
 */
export async function handleUnifiedLogin(
  formData: FormData,
  inputType: 'email' | 'phone',
  emailLoginAction: (formData: FormData) => Promise<any>,
  phoneLoginAction: (formData: FormData) => Promise<any>,
  redirectTo?: string
): Promise<never> {
  try {
    const result =
      inputType === 'email'
        ? await emailLoginAction(formData)
        : await phoneLoginAction(formData);

    const fallbackError =
      inputType === 'email'
        ? LOGIN_ERRORS.EMAIL_LOGIN_FAILED
        : LOGIN_ERRORS.PHONE_LOGIN_FAILED;

    return await handleLoginResult(result, redirectTo, fallbackError);
  } catch (error) {
    return handleLoginError(
      error,
      inputType === 'email' ? 'Email login' : 'Phone login'
    );
  }
}

/**
 * Unified register handler that determines email vs phone automatically
 */
export async function handleUnifiedRegister(
  formData: FormData,
  inputType: 'email' | 'phone',
  emailRegisterAction: (formData: FormData) => Promise<any>,
  phoneRegisterAction: (formData: FormData) => Promise<any>
): Promise<never> {
  try {
    const result =
      inputType === 'email'
        ? await emailRegisterAction(formData)
        : await phoneRegisterAction(formData);

    const fallbackError =
      inputType === 'email'
        ? REGISTER_ERRORS.EMAIL_REGISTER_FAILED
        : REGISTER_ERRORS.PHONE_REGISTER_FAILED;

    return await handleRegisterResult(
      result.data || result,
      formData,
      inputType,
      fallbackError
    );
  } catch (error) {
    return handleRegisterError(
      error,
      inputType === 'email' ? 'Email registration' : 'Phone registration'
    );
  }
}
