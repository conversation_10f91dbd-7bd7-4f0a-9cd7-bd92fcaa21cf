import { Building2, Users, TrendingUp, Clock, Award, Globe } from "lucide-react";

export function AboutStats() {
  const stats = [
    {
      icon: Building2,
      value: "500+",
      label: "Aktif Salon",
      description: "Türkiye genelinde"
    },
    {
      icon: Users,
      value: "50K+",
      label: "<PERSON><PERSON><PERSON>",
      description: "Platformumuzu kullanan"
    },
    {
      icon: TrendingUp,
      value: "%40",
      label: "Verimlilik Artışı",
      description: "Ortalama salon performansı"
    },
    {
      icon: Clock,
      value: "99.9%",
      label: "Uptime",
      description: "Kesintisiz hizmet"
    },
    {
      icon: Award,
      value: "4.9/5",
      label: "Müşteri Memnuniyeti",
      description: "Ortalama değerlendirme"
    },
    {
      icon: Globe,
      value: "24/7",
      label: "Destek",
      description: "Kesintisiz müşteri desteği"
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Rakamlarla Sportiva
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Başarımızı gösteren rakamlar ve müşterilerimizin güvenini kazanmamızın nedenleri
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={index}
                  className="text-center group hover:scale-105 transition-all duration-300"
                >
                  <div className="bg-muted/30 rounded-xl p-8 border border-border hover:border-primary/30 hover:shadow-lg transition-all duration-300">
                    <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>

                    <div className="text-4xl font-bold text-primary mb-2">
                      {stat.value}
                    </div>

                    <div className="text-lg font-semibold text-foreground mb-2">
                      {stat.label}
                    </div>

                    <div className="text-sm text-muted-foreground">
                      {stat.description}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Achievement Section */}
          <div className="mt-20 bg-muted/30 rounded-xl p-8 md:p-12 border border-border">
            <div className="text-center">
              <h3 className="text-2xl md:text-3xl font-bold text-primary mb-6">
                Başarılarımız
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="space-y-3">
                  <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Award className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-foreground">En İyi Startup 2024</h4>
                  <p className="text-sm text-muted-foreground">TechCrunch Türkiye</p>
                </div>
                <div className="space-y-3">
                  <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-foreground">%300 Büyüme</h4>
                  <p className="text-sm text-muted-foreground">Son 12 ayda</p>
                </div>
                <div className="space-y-3">
                  <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-semibold text-foreground">Sektör Lideri</h4>
                  <p className="text-sm text-muted-foreground">Spor teknolojilerinde</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
