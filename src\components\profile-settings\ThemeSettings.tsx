'use client';

import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Type } from 'lucide-react';

import { FontSize } from '@/lib/supabase/types';
import { toast } from 'sonner';
import { useLocalSettings } from '@/hooks/use-local-settings';

export function ThemeSettings() {
  const { settings, isLoaded, updateFontSize, updateAnimations } =
    useLocalSettings();

  // Ayarlar yüklenene kadar loading göster
  if (!isLoaded) {
    return <div>Ayarlar yükleniyor...</div>;
  }

  const handleFontSizeChange = (newFontSize: FontSize) => {
    updateFontSize(newFontSize);
    toast.success('Font boyutu güncellendi');
  };

  const handleAnimationsToggle = (enabled: boolean) => {
    updateAnimations(enabled);
    toast.success(enabled ? 'Animasyonlar açıldı' : 'Animasyonlar kapatıldı');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Type className="h-5 w-5" />
          Görünüm Ayarları
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Font Boyutu */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Font Boyutu</Label>
          <Select
            value={settings.font_size}
            onValueChange={(value: FontSize) => handleFontSizeChange(value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="small">Küçük</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="large">Büyük</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Animasyonlar */}
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Animasyonlar</Label>
          <Switch
            checked={settings.animations_enabled}
            onCheckedChange={handleAnimationsToggle}
          />
        </div>
      </CardContent>
    </Card>
  );
}
