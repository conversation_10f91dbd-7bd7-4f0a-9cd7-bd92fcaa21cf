import { getAllPlatformPackagesCached } from "@/lib/actions/business/platform-packages";
import { Check, Zap, Crown, BadgeDollarSign } from "lucide-react";
import { PlatformPackages } from "@/types/database/tables";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// Sadece görsel amaçlı ikon eşlemesi (paket verisinin kendisi DB'den gelir)
const TIER_ICON: Record<string, React.ReactElement> = {
  free: <BadgeDollarSign className="h-5 w-5" />,
  starter: <Zap className="h-5 w-5" />,
  professional: <Crown className="h-5 w-5" />,
};

interface PricingModernProps {
  showHeader?: boolean;
  compact?: boolean;
  packages?: PlatformPackages[];
}

export async function PricingModern({
  showHeader = true,
  compact = false,
  packages: incomingPackages,
}: PricingModernProps) {
  let packages: PlatformPackages[] = Array.isArray(incomingPackages) ? incomingPackages : [];
  let errorMsg: string | null = null;
  if (!incomingPackages) {
    const packagesResult = await getAllPlatformPackagesCached();
    if (packagesResult.success && packagesResult.data) {
      packages = packagesResult.data;
    } else {
      errorMsg = packagesResult.error || "Paketler yüklenirken bir hata oluştu.";
    }
  }

  const groupedPackages = packages.reduce((acc, pkg) => {
    if (!acc[pkg.tier]) acc[pkg.tier] = [];
    acc[pkg.tier].push(pkg);
    return acc;
  }, {} as Record<string, PlatformPackages[]>);

  // Helper functions
  const formatFeatures = (features: unknown): string[] => {
    // Array<string>
    if (Array.isArray(features)) {
      return features.filter((item): item is string => typeof item === "string");
    }
    // JSON string fallback
    if (typeof features === "string") {
      try {
        const parsed = JSON.parse(features);
        return formatFeatures(parsed);
      } catch {
        return [];
      }
    }
    // Object form: { perks: string[]; ... }
    if (typeof features === "object" && features !== null) {
      const obj = features as Record<string, unknown>;
      if (Array.isArray(obj.perks)) {
        return (obj.perks as unknown[]).filter((v): v is string => typeof v === "string");
      }
      // Direct string values in object
      const directStrings = Object.values(obj).filter(
        (v): v is string => typeof v === "string"
      );
      if (directStrings.length) return directStrings;
      // Nested arrays of strings
      const nested = Object.values(obj)
        .flatMap(v => (Array.isArray(v) ? v : []))
        .filter((v): v is string => typeof v === "string");
      return nested;
    }
    return [];
  };

  // Feature text prettifier to improve readability
  const prettifyFeature = (text: string): string => {
    try {
      let t = (text || "").toString().trim();
      if (!t) return t;
      // Common replacements
      t = t.replace(/^Maks\s/i, "Maksimum ");
      t = t.replace(/\bmax\b/i, "Maksimum");
      t = t.replace(/uye/gi, "üye");
      // Ensure first letter uppercase
      t = t.charAt(0).toUpperCase() + t.slice(1);
      return t;
    } catch {
      return text as string;
    }
  };

  // New helpers for redesigned UI
  const POPULAR_TIER = "starter" as const;
  // Order ascending by typical price so the middle option (starter) sits in the center
  const TIER_ORDER: Record<string, number> = { free: 0, starter: 1, professional: 2 };
  const tiersSorted = Object.keys(groupedPackages).sort(
    (a, b) => (TIER_ORDER[a] ?? 99) - (TIER_ORDER[b] ?? 99)
  );

  const tl = (n: number) => n.toLocaleString("tr-TR");

  // Savings computation removed (no monthly plans)


  if (packages.length === 0) {
    return (
      <section
        id={compact ? "pricing" : "pricing-plans"}
        className={`relative isolate w-full ${compact ? "py-10" : "py-16 md:py-24 lg:py-32"}`}
        aria-label="Fiyatlandırma paketi yükleme hatası"
      >
        <div className="container px-4 md:px-6">
          <div className="mx-auto max-w-3xl">
            <div
              role="alert"
              aria-live="assertive"
              className="rounded-2xl border border-white/15 bg-white/5 p-6 text-center shadow-xl backdrop-blur supports-[backdrop-filter]:bg-white/10"
            >
              <div className="text-destructive text-lg font-semibold">⚠️ {errorMsg || "Paketler yüklenirken bir hata oluştu."}</div>
              <p className="mt-2 text-sm text-muted-foreground">
                Lütfen daha sonra tekrar deneyin veya destek ekibimizle iletişime geçin.
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      id={compact ? "pricing" : "pricing-plans"}
      className={`relative isolate w-full bg-gradient-to-br from-muted/20 via-background to-muted/20 ${compact ? "py-16" : "py-20 md:py-28 lg:py-32"}`}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,hsl(var(--primary)/0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-grid-pattern opacity-20" />
      </div>

      <div className="container px-4 mx-auto md:px-6">
        {showHeader && (
          <div className="mx-auto mb-16 max-w-4xl text-center md:mb-20">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-4 py-2 text-sm font-medium text-primary backdrop-blur-sm">
              <Crown className="h-4 w-4" />
              <span>Fiyatlandırma</span>
            </div>
            <h2 className="text-balance text-4xl font-bold md:text-5xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-6">
              İşletmenize Uygun Esnek Planlar
            </h2>
            <p className="mx-auto mt-6 max-w-3xl text-xl text-muted-foreground leading-relaxed">
              Büyüme hedeflerinize göre ölçeklenen şeffaf fiyatlandırma. Beta döneminde denemesi ücretsiz.
            </p>
          </div>
        )}

        <div id="pricing-list" className="grid gap-8 sm:grid-cols-1 md:grid-cols-3 max-w-6xl mx-auto">
          {tiersSorted.map((tier) => {
            const tierPackages = groupedPackages[tier] ?? [];
            const icon = TIER_ICON[tier];
            // Support lifetime packages (duration is null)
            const isLifetime = (d: string | null | undefined) =>
              d == null || d === "lifetime" || d === "free" || d === "";
            const yearlyPkg = tierPackages.find((p) => p.duration === "yearly");
            const lifetimePkg = tierPackages.find((p) => isLifetime(p.duration as any));
            if (!yearlyPkg && !lifetimePkg && tierPackages.length === 0) return null;

            const currentPkg = lifetimePkg ?? yearlyPkg ?? tierPackages[0];
            const features = formatFeatures(currentPkg.features);
            const iconEl = icon ?? <BadgeDollarSign className="h-5 w-5" />;
            const displayName = currentPkg.name;
            const isPopular = tier === POPULAR_TIER;

            return (
              <article
                key={tier}
                className={`group relative overflow-hidden rounded-3xl border bg-gradient-to-br from-background/90 to-background/70 p-10 shadow-xl backdrop-blur-sm transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl ${
                  isPopular
                    ? "z-10 border-primary/50 ring-2 ring-primary/30 md:scale-[1.05] md:-translate-y-2"
                    : "border-border/50 hover:border-primary/30"
                }`}
              >
                {/* Enhanced gradient highlight */}
                <div className="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent" />
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_0%,hsl(var(--primary)/0.15),transparent_60%)]" />
                </div>

                {/* Enhanced popular badge */}
                {isPopular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <div className="rounded-full bg-gradient-to-r from-primary to-primary/80 px-6 py-2 text-sm font-bold text-primary-foreground shadow-lg">
                      ⭐ En Popüler
                    </div>
                  </div>
                )}

                <header className="mb-8 text-center relative">
                  <div className="mx-auto mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 text-primary border border-primary/20 group-hover:scale-110 transition-transform duration-300">
                    {iconEl}
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-4">{displayName}</h3>

                  <div className="text-5xl font-extrabold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                    ₺{tl(currentPkg.price)}
                  </div>
                  <div className="mt-2 text-lg text-muted-foreground font-medium">
                    {isLifetime(currentPkg.duration as any) ? "Ömür Boyu" : "/yıl"}
                  </div>

                </header>

                <ul className="mb-10 space-y-4 relative">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-4">
                      <div className="mt-0.5 rounded-full bg-emerald-100 p-1">
                        <Check className="h-4 w-4 text-emerald-600" />
                      </div>
                      <span className="text-base text-foreground leading-relaxed">{prettifyFeature(feature)}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  asChild
                  size="lg"
                  className={
                    isPopular
                      ? `w-full h-14 text-lg font-semibold bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1`
                      : `w-full h-14 text-lg font-semibold bg-gradient-to-r from-muted to-muted/80 text-foreground border-2 border-border hover:border-primary/30 transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:shadow-lg`
                  }
                >
                  <Link href={`/onboarding`} aria-label={`${displayName} paketi ile başla`}>
                    {isPopular ? "🚀 Hemen Başla" : "Başla"}
                  </Link>
                </Button>
              </article>
            );
          })}
        </div>
      </div>
    </section>
  );
}
