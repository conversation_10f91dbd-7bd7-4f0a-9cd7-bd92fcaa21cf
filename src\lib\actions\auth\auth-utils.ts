/**
 * Utility functions for authentication operations
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import { getSupabaseAdmin } from '@/lib/supabase/admin';
import {
  validatePhoneNumber,
  formatPhoneNumber,
} from '@/lib/utils/phone-utils';
import { COMMON_PATTERNS } from '@/lib/actions/shared/constants';
import {
  AUTH_ERROR_MESSAGES,
  PASSWORD_VALIDATION,
  AUTH_DATABASE_DEFAULTS,
} from './auth-constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface EmailSignUpData {
  email: string;
  password: string;
}

export interface PhoneSignUpData {
  phone: string;
  password: string;
}

export interface AuthFormData {
  email?: string;
  phone?: string;
  password: string;
}

export interface OtpVerificationData {
  email?: string;
  phone?: string;
  token: string;
}

export interface PasswordResetData {
  password: string;
  confirmPassword: string;
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate email format
 */
export function validateEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email) {
    return { isValid: false, error: AUTH_ERROR_MESSAGES.EMAIL_REQUIRED };
  }

  if (!COMMON_PATTERNS.EMAIL_REGEX.test(email)) {
    return { isValid: false, error: AUTH_ERROR_MESSAGES.INVALID_EMAIL_FORMAT };
  }

  return { isValid: true };
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): {
  isValid: boolean;
  error?: string;
} {
  if (!password) {
    return {
      isValid: false,
      error: AUTH_ERROR_MESSAGES.PASSWORD_AND_CONFIRM_REQUIRED,
    };
  }

  if (password.length < PASSWORD_VALIDATION.MIN_LENGTH) {
    return { isValid: false, error: AUTH_ERROR_MESSAGES.PASSWORD_TOO_SHORT };
  }

  return { isValid: true };
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(
  password: string,
  confirmPassword: string
): { isValid: boolean; error?: string } {
  if (!password || !confirmPassword) {
    return {
      isValid: false,
      error: AUTH_ERROR_MESSAGES.PASSWORD_AND_CONFIRM_REQUIRED,
    };
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    return passwordValidation;
  }

  if (password !== confirmPassword) {
    return {
      isValid: false,
      error: AUTH_ERROR_MESSAGES.PASSWORDS_DO_NOT_MATCH,
    };
  }

  return { isValid: true };
}

/**
 * Validate email signup data
 */
export function validateEmailSignUpData(formData: FormData): EmailSignUpData {
  const identifier = formData.get('identifier') as string;
  const email = (formData.get('email') as string) || identifier;
  const password = formData.get('password') as string;

  if (!email || !password) {
    throw new Error(AUTH_ERROR_MESSAGES.EMAIL_AND_PASSWORD_REQUIRED);
  }

  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    throw new Error(emailValidation.error!);
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    throw new Error(passwordValidation.error!);
  }

  return { email, password };
}

/**
 * Validate phone signup data
 */
export function validatePhoneSignUpData(formData: FormData): {
  phone: string;
  password: string;
  cleanPhone: string;
  formattedPhone: string;
} {
  const identifier = formData.get('identifier') as string;
  const phone = (formData.get('phone') as string) || identifier;
  const password = formData.get('password') as string;

  if (!phone || !password) {
    throw new Error(AUTH_ERROR_MESSAGES.PHONE_AND_PASSWORD_REQUIRED);
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    throw new Error(passwordValidation.error!);
  }

  const phoneValidation = validatePhoneNumber(phone);
  if (!phoneValidation.isValid) {
    throw new Error(phoneValidation.error!);
  }

  const { clean, formatted } = formatPhoneNumber(phone);

  return {
    phone,
    password,
    cleanPhone: clean,
    formattedPhone: formatted,
  };
}

// ============================================================================
// PROFILE MANAGEMENT
// ============================================================================

/**
 * Create user profile in database
 */
export async function createUserProfile(
  userId: string,
  email: string | null,
  phoneNumber: string | null,
  fullName?: string | null,
  avatarUrl?: string | null
): Promise<void> {
  const admin = getSupabaseAdmin();

  const { error: profileError } = await admin.from('profiles').insert({
    id: userId,
    email: email || AUTH_DATABASE_DEFAULTS.NULL_VALUE,
    full_name: fullName || AUTH_DATABASE_DEFAULTS.EMPTY_FULL_NAME,
    phone_number: phoneNumber || AUTH_DATABASE_DEFAULTS.NULL_VALUE,
    avatar_url: avatarUrl || AUTH_DATABASE_DEFAULTS.NULL_VALUE,
    is_guest_account: AUTH_DATABASE_DEFAULTS.IS_GUEST_ACCOUNT,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });

  if (profileError) {
    console.error('Profile creation error:', profileError);
    throw new Error(AUTH_ERROR_MESSAGES.PROFILE_CREATION_FAILED);
  }
}

// ============================================================================
// PHONE UTILITIES
// ============================================================================

/**
 * Check if phone number is already registered
 */
export async function isPhoneRegistered(cleanPhone: string): Promise<boolean> {
  const admin = getSupabaseAdmin();

  const { data: existingProfile } = await admin
    .from('profiles')
    .select('id')
    .eq('phone_number', cleanPhone)
    .single();

  return !!existingProfile;
}

/**
 * Find user by phone number with alternative formats
 */
export async function findUserByPhone(
  cleanPhone: string
): Promise<{ id: string; phone_number: string } | null> {
  const admin = getSupabaseAdmin();

  // Try exact match first
  const { data: profile, error: profileError } = await admin
    .from('profiles')
    .select('id, phone_number')
    .eq('phone_number', cleanPhone)
    .single();

  if (profileError || !profile) {
    // Try alternative formats
    const { data: profileAlt } = await admin
      .from('profiles')
      .select('id, phone_number')
      .or(
        `phone_number.eq.${cleanPhone},phone_number.eq.+${cleanPhone},phone_number.eq.0${cleanPhone.slice(2)}`
      )
      .single();

    return profileAlt || null;
  }

  return profile;
}
