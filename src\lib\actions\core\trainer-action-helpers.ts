import { TrainerPermissions } from '@/lib/auth/server-auth';

// Permission requirement tipi
export interface PermissionRequirement {
  category: keyof TrainerPermissions;
  action: string;
}

/**
 * Trainer action için yardımcı fonksiyonlar
 * Bu dosya 'use server' direktifi olmadığı için object export edebilir
 */
export const TrainerActionHelpers = {
  /**
   * Permission kontrolü yapar
   */
  hasPermission(
    permissions: TrainerPermissions,
    category: keyof TrainerPermissions,
    action: string
  ): boolean {
    const categoryPermissions = permissions[category] as any;
    return categoryPermissions && categoryPermissions[action] === true;
  },

  /**
   * Birden fazla permission kontrolü yapar
   */
  hasAnyPermission(
    permissions: TrainerPermissions,
    requirements: PermissionRequirement[]
  ): boolean {
    return requirements.some(req =>
      this.hasPermission(permissions, req.category, req.action)
    );
  },

  /**
   * Tüm permission'ları kontrol eder
   */
  hasAllPermissions(
    permissions: TrainerPermissions,
    requirements: PermissionRequirement[]
  ): boolean {
    return requirements.every(req =>
      this.hasPermission(permissions, req.category, req.action)
    );
  },
};
