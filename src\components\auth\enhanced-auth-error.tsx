'use client';

import { AlertCircle, RefreshCw, Mail, Phone, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  translateAuthError,
  DetailedAuthError,
  AuthErrorContext,
} from '@/lib/utils/auth-errors';

interface EnhancedAuthErrorProps {
  error: string;
  context?: AuthErrorContext;
  onRetry?: () => void;
  className?: string;
}

export function EnhancedAuthError({
  error,
  context,
  onRetry,
  className = '',
}: EnhancedAuthErrorProps) {
  const router = useRouter();
  const errorInfo: DetailedAuthError = translateAuthError(error, context);

  const getContextIcon = () => {
    if (context?.method === 'email') {
      return <Mail className="h-4 w-4" />;
    }
    if (context?.method === 'phone') {
      return <Phone className="h-4 w-4" />;
    }
    return <AlertCircle className="h-4 w-4" />;
  };

  const getContextTitle = () => {
    if (context?.action === 'login') {
      return 'Giriş Hatası';
    }
    if (context?.action === 'register') {
      return 'Kayıt Hatası';
    }
    if (context?.action === 'verify_email') {
      return 'E-posta Doğrulama Hatası';
    }
    if (context?.action === 'verify_phone') {
      return 'Telefon Doğrulama Hatası';
    }
    return 'Hata';
  };

  return (
    <div
      className={`border-destructive/30 bg-destructive/5 animate-in slide-in-from-top-2 rounded-xl border p-4 duration-500 ${className}`}
      role="alert"
      aria-live="polite"
    >
      {/* Header */}
      <div className="flex items-start gap-3">
        <div className="mt-0.5 flex-shrink-0">
          <div className="bg-destructive/10 flex h-8 w-8 items-center justify-center rounded-full">
            {getContextIcon()}
          </div>
        </div>

        <div className="min-w-0 flex-1">
          <h3 className="text-destructive mb-1 text-sm font-semibold">
            {getContextTitle()}
          </h3>

          <p className="text-destructive/90 mb-2 text-sm">
            {errorInfo.message}
          </p>

          {errorInfo.suggestion && (
            <p className="text-muted-foreground mb-3 text-xs">
              {errorInfo.suggestion}
            </p>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            {/* Retry Button */}
            {errorInfo.canRetry && onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="border-destructive/30 text-destructive hover:bg-destructive/10 h-8 text-xs"
              >
                <RefreshCw className="mr-1 h-3 w-3" />
                Tekrar Dene
              </Button>
            )}

            {/* Retry Button without onRetry prop (page refresh) */}
            {errorInfo.canRetry && !onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.refresh()}
                className="border-destructive/30 text-destructive hover:bg-destructive/10 h-8 text-xs"
              >
                <RefreshCw className="mr-1 h-3 w-3" />
                Tekrar Dene
              </Button>
            )}

            {/* Action Button */}
            {errorInfo.actionText && errorInfo.actionUrl && (
              <Button
                variant="outline"
                size="sm"
                asChild
                className="border-primary/30 text-primary hover:bg-primary/10 h-8 text-xs"
              >
                <Link href={errorInfo.actionUrl}>
                  {errorInfo.actionText}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </Button>
            )}

            {/* Custom Action Button (for resend operations) */}
            {errorInfo.actionText && !errorInfo.actionUrl && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Bu durumda parent component'ten action gelecek
                  if (errorInfo.actionText?.includes('Gönder')) {
                    if (onRetry) {
                      onRetry();
                    } else {
                      // Telefon doğrulama sayfasında resend butonunu tetikle
                      const resendButton = document.querySelector(
                        'form[action*="handleResendOTP"] button[type="submit"]'
                      ) as HTMLButtonElement;
                      if (resendButton) {
                        resendButton.click();
                      }
                    }
                  }
                }}
                className="border-primary/30 text-primary hover:bg-primary/10 h-8 text-xs"
              >
                {errorInfo.actionText}
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Compact version for inline usage
 */
export function EnhancedAuthErrorInline({
  error,
  context,
  className = '',
}: {
  error: string;
  context?: AuthErrorContext;
  className?: string;
}) {
  const errorInfo: DetailedAuthError = translateAuthError(error, context);

  return (
    <div
      className={`border-destructive/30 bg-destructive/5 text-destructive flex items-center gap-2 rounded-lg border p-3 text-sm ${className}`}
      role="alert"
      aria-live="polite"
    >
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <div className="min-w-0 flex-1">
        <span className="font-medium">{errorInfo.message}</span>
        {errorInfo.suggestion && (
          <span className="text-muted-foreground mt-1 block text-xs">
            {errorInfo.suggestion}
          </span>
        )}
      </div>
    </div>
  );
}
