import { redirect } from 'next/navigation';
import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { CheckCircle, Mail } from 'lucide-react';
import { EmailOTPForm } from '@/components/auth/email-otp-form';
import {
  verifyEmailOTP,
  resendEmailOTP,
} from '@/lib/actions/auth/auth-actions';

export const metadata: Metadata = {
  title: 'E-posta Doğrulama | Sportiva',
  description: 'E-posta adresinizi doğrulayın.',
};

interface VerifyEmailPageProps {
  searchParams: {
    email?: string;
    error?: string;
    message?: string;
    resent?: string;
  };
}

export default async function VerifyEmailPage({
  searchParams,
}: VerifyEmailPageProps) {
  const { email, error, message, resent } = searchParams;

  // E-posta adresi yoksa register sayfasına yönlendir
  if (!email) {
    redirect('/auth/register?error=E-posta adresi bulunamadı');
  }

  async function handleVerifyOTP(formData: FormData) {
    'use server';

    try {
      // E-posta adresini form data'ya ekle
      formData.append('email', email!);

      const result = await verifyEmailOTP(formData);

      if (!result.success) {
        redirect(
          `/auth/verify-email?email=${encodeURIComponent(email!)}&error=${encodeURIComponent(result.error || 'Doğrulama kodu geçersiz')}`
        );
      }

      // Successful verification - redirect to onboarding
      redirect('/onboarding');
    } catch (error) {
      // Check if this is a redirect error (which is expected)
      if (error instanceof Error && error.message === 'NEXT_REDIRECT') {
        throw error; // Re-throw redirect errors
      }

      console.error('Email OTP verification server action error:', error);
      redirect(
        `/auth/verify-email?email=${encodeURIComponent(email!)}&error=${encodeURIComponent('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.')}`
      );
    }
  }

  async function handleResendOTP(formData: FormData) {
    'use server';

    try {
      // E-posta adresini form data'ya ekle
      formData.append('email', email!);

      const result = await resendEmailOTP(formData);

      if (!result.success) {
        redirect(
          `/auth/verify-email?email=${encodeURIComponent(email!)}&error=${encodeURIComponent(result.error || 'E-posta yeniden gönderilemedi')}`
        );
      }

      // Email resent successfully
      redirect(
        `/auth/verify-email?email=${encodeURIComponent(email!)}&resent=true&message=${encodeURIComponent('Doğrulama kodu yeniden gönderildi')}`
      );
    } catch (error) {
      // Check if this is a redirect error (which is expected)
      if (error instanceof Error && error.message === 'NEXT_REDIRECT') {
        throw error; // Re-throw redirect errors
      }

      console.error('Email OTP resend server action error:', error);
      redirect(
        `/auth/verify-email?email=${encodeURIComponent(email!)}&error=${encodeURIComponent('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.')}`
      );
    }
  }

  return (
    <div className="animate-in slide-in-from-bottom-4 w-full duration-700">
      {/* Header */}
      <div className="mb-8 text-center">
        <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
          <Mail className="text-primary h-8 w-8" />
        </div>
        <h1 className="text-foreground mb-2 text-2xl font-bold lg:text-3xl">
          E-posta Doğrulama
        </h1>
        <p className="text-muted-foreground text-sm lg:text-base">
          <span className="font-medium">{email}</span> adresine gönderilen 6
          haneli kodu giriniz.
        </p>
      </div>

      {/* Success Message */}
      {(message || resent) && (
        <div
          className="animate-in slide-in-from-top-2 mb-6 flex w-full items-center space-x-3 rounded-xl border border-green-500/30 bg-green-50/50 p-4 text-green-700 duration-500 dark:bg-green-950/20 dark:text-green-400"
          role="alert"
          aria-live="polite"
        >
          <CheckCircle className="h-5 w-5 flex-shrink-0" aria-hidden="true" />
          <span className="text-sm font-medium">
            {message || 'Doğrulama kodu yeniden gönderildi'}
          </span>
        </div>
      )}

      {/* Form Card */}
      <div className="bg-card/80 border-border/50 shadow-primary/5 hover:shadow-primary/10 rounded-2xl border p-6 shadow-2xl backdrop-blur-sm transition-all duration-500 hover:scale-[1.01] lg:p-8">
        <EmailOTPForm
          onVerifyOTP={handleVerifyOTP}
          onResendOTP={handleResendOTP}
          error={error}
          resent={resent}
        />
      </div>

      {/* Footer */}
      <div className="mt-6 text-center">
        <p className="text-muted-foreground text-sm">
          Yanlış e-posta adresi mi?{' '}
          <Link
            href="/auth/register"
            className="text-primary hover:text-primary/80 font-medium transition-colors"
          >
            Kayıt sayfasına dön
          </Link>
        </p>
      </div>
    </div>
  );
}
