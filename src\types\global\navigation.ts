/**
 * Navigation and routing types
 *
 * Types for Next.js routing, search params, and navigation
 */

/**
 * Next.js SearchParams type (reusable across all pages)
 */
export type NextSearchParams = Promise<{
  [key: string]: string | string[] | undefined;
}>;

/**
 * Common page props interface
 */
export interface BasePageProps {
  searchParams: NextSearchParams;
}

/**
 * Dynamic route params generic type
 */
export interface DynamicPageProps<T extends Record<string, string>>
  extends BasePageProps {
  params: Promise<T>;
}

/**
 * City page specific props
 */
export interface CityPageProps extends DynamicPageProps<{ city: string }> {}

/**
 * District page specific props
 */
export interface DistrictPageProps
  extends DynamicPageProps<{ city: string; district: string }> {}

/**
 * Gym page specific props
 */
export interface GymPageProps extends DynamicPageProps<{ gymId: string }> {}

/**
 * User profile page props
 */
export interface UserPageProps extends DynamicPageProps<{ userId: string }> {}

/**
 * Breadcrumb item type
 */
export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

/**
 * Route configuration
 */
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  requiresAuth?: boolean;
  roles?: string[];
}
