'use client';

import * as React from 'react';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { cn } from '@/lib/utils';

interface EmailOTPInputProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
  onComplete?: (value: string) => void;
}

export function EmailOTPInput({
  value,
  onChange,
  disabled = false,
  className,
  onComplete,
}: EmailOTPInputProps) {
  const handleChange = (newValue: string) => {
    // Sadece sayıları kabul et
    const numericValue = newValue.replace(/\D/g, '');

    // 6 haneli sınırla
    const limitedValue = numericValue.slice(0, 6);

    onChange(limitedValue);

    // 6 hane tamamlandığında onComplete'i çağır
    if (limitedValue.length === 6 && onComplete) {
      onComplete(limitedValue);
    }
  };

  return (
    <div className={cn('flex justify-center', className)}>
      <InputOTP
        maxLength={6}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        pattern="[0-9]*"
        inputMode="numeric"
        autoComplete="one-time-code"
        containerClassName="gap-2"
      >
        <InputOTPGroup className="gap-2">
          <InputOTPSlot
            index={0}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
          <InputOTPSlot
            index={1}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
          <InputOTPSlot
            index={2}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
        </InputOTPGroup>

        <div className="flex w-4 items-center justify-center">
          <div className="bg-muted-foreground/30 h-0.5 w-2 rounded-full" />
        </div>

        <InputOTPGroup className="gap-2">
          <InputOTPSlot
            index={3}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
          <InputOTPSlot
            index={4}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
          <InputOTPSlot
            index={5}
            className="focus:border-primary focus:ring-primary/20 h-12 w-12 rounded-lg border-2 font-mono text-lg transition-all duration-200 focus:ring-2"
          />
        </InputOTPGroup>
      </InputOTP>
    </div>
  );
}

// Countdown Timer Component
interface CountdownTimerProps {
  initialSeconds: number;
  onComplete?: () => void;
  className?: string;
}

export function CountdownTimer({
  initialSeconds,
  onComplete,
  className,
}: CountdownTimerProps) {
  const [seconds, setSeconds] = React.useState(initialSeconds);
  const [isActive, setIsActive] = React.useState(true);

  React.useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds(prevSeconds => {
          if (prevSeconds <= 1) {
            setIsActive(false);
            if (onComplete) {
              onComplete();
            }
            return 0;
          }
          return prevSeconds - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isActive, seconds, onComplete]);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const remainingSeconds = time % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn('text-center', className)}>
      {seconds > 0 ? (
        <p className="text-muted-foreground text-sm">
          Yeni kod gönderebilmek için{' '}
          <span className="text-primary font-mono font-medium">
            {formatTime(seconds)}
          </span>{' '}
          bekleyin
        </p>
      ) : (
        <p className="text-muted-foreground text-sm">
          Artık yeni kod gönderebilirsiniz
        </p>
      )}
    </div>
  );
}

// OTP Validation Utilities
export const validateOTPCode = (code: string): boolean => {
  return /^[0-9]{6}$/.test(code);
};

export const formatOTPCode = (code: string): string => {
  // Sadece sayıları al ve 6 haneli sınırla
  return code.replace(/\D/g, '').slice(0, 6);
};
