import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getUserRole } from '@/lib/actions/auth/auth-actions';
import { getMemberAppointments, getMemberAppointmentStats } from '@/lib/actions/dashboard/member/appointment-actions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { MemberAppointmentsList } from './components/member-appointments-list';
import { MemberAppointmentsStats } from './components/member-appointments-stats';
import { MemberAppointmentsFilters } from './components/member-appointments-filters';

interface PageProps {
  searchParams: {
    startDate?: string;
    endDate?: string;
    status?: string;
    gymId?: string;
  };
}

export default async function MemberAppointmentsPage({ searchParams }: PageProps) {
  // Auth and role check
  const userRole = await getUserRole();
  if (!userRole.success || userRole.data?.role !== 'member') {
    redirect('/dashboard');
  }

  const memberId = userRole.data.profile_id;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Randevularım</h1>
        <p className="text-muted-foreground">
          Yaklaşan ve geçmiş randevularınızı görüntüleyin
        </p>
      </div>

      {/* Stats */}
      <Suspense fallback={<StatsSkeletons />}>
        <AppointmentStats memberId={memberId} />
      </Suspense>

      <div className="grid gap-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filtreler</CardTitle>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<FiltersSkeleton />}>
              <MemberAppointmentsFilters searchParams={searchParams} />
            </Suspense>
          </CardContent>
        </Card>
            <Suspense fallback={<AppointmentsListSkeleton />}>
              <AppointmentsContent memberId={memberId} searchParams={searchParams} />
            </Suspense>
      </div>
    </div>
  );
}

async function AppointmentStats({ memberId }: { memberId: string }) {
  const statsResult = await getMemberAppointmentStats(memberId, 'month');
  
  if (statsResult.success) {
    return <MemberAppointmentsStats stats={statsResult.data!} />;
  }

  return null;
}

async function AppointmentsContent({ 
  memberId, 
  searchParams 
}: { 
  memberId: string; 
  searchParams: PageProps['searchParams'] 
}) {
  const filters = {
    startDate: searchParams.startDate,
    endDate: searchParams.endDate,
    status: searchParams.status as any,
    gymId: searchParams.gymId,
  };

  const appointmentsResult = await getMemberAppointments(memberId, filters);

  if (!appointmentsResult.success) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Randevular Yüklenemedi
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Randevularınız yüklenirken bir sorun oluştu. Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin.
            </p>
            {process.env.NODE_ENV === 'development' && (
              <p className="mt-2 text-xs text-red-600 font-mono">
                Hata: {appointmentsResult.error}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return <MemberAppointmentsList appointments={appointmentsResult.data as any || []} />;
}

function StatsSkeletons() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-8 w-12" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function FiltersSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  );
}

function AppointmentsListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-3 w-32" />
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-20" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}