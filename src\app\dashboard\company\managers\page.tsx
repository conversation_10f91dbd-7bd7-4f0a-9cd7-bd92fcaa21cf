import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { ManagersClient } from './components/ManagersClient';
import { getCompanyInvitations } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';
export default async function ManagersPage() {
  // Paralel olarak gym'leri ve davet kodlarını getir
  const [gymsResult, invitationsResult] = await Promise.all([
    getManagerGyms(),
    getCompanyInvitations(),
  ]);

  if (!gymsResult.success) {
    throw new Error(gymsResult.error || 'Salonlar yüklenemedi');
  }

  if (!invitationsResult.success) {
    throw new Error(invitationsResult.error || 'Davet kodları yüklenemedi');
  }

  return (
    <ManagersClient
      gyms={gymsResult.data || []}
      invitations={invitationsResult.data || []}
    />
  );
}
