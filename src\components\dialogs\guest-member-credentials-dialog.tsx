'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Copy,
  Eye,
  EyeOff,
  AlertTriangle,
  UserCheck,
  Mail,
  Lock,
} from 'lucide-react';
import { toast } from 'sonner';

interface GuestMemberCredentialsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  memberName: string;
  email: string;
  password: string;
}

export function GuestMemberCredentialsDialog({
  isOpen,
  onClose,
  memberName,
  email,
  password,
}: GuestMemberCredentialsDialogProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showWarning, setShowWarning] = useState(false);

  const copyToClipboard = async (text: string, type: 'email' | 'password') => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(
        type === 'email' ? 'E-posta adresi kopyalandı' : 'Şifre kopyalandı'
      );
    } catch (error) {
      toast.error('Kopyalama başarısız oldu');
    }
  };

  const handleClose = () => {
    if (!showWarning) {
      setShowWarning(true);
      return;
    }
    onClose();
  };

  const confirmClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md" showCloseButton={false}>
        {!showWarning ? (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5 text-green-600" />
                Misafir Üye Başarıyla Oluşturuldu
              </DialogTitle>
              <DialogDescription>
                <strong>{memberName}</strong> için giriş bilgileri aşağıdadır.
                Bu bilgileri müşteriye iletin.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  E-posta Adresi
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="email"
                    value={email}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(email, 'email')}
                    className="shrink-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Şifre
                </Label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      readOnly
                      className="pr-10 font-mono text-sm"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute top-0 right-0 h-full px-3 hover:bg-transparent"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(password, 'password')}
                    className="shrink-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Info Box */}
              <div className="rounded-md border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>Not:</strong> Bu bilgileri müşteriye ilettikten sonra
                  güvenlik için bir daha görüntüleyemezsiniz.
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button onClick={handleClose} className="w-full">
                Üyeler Sayfasına Dön
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                Dikkat!
              </DialogTitle>
              <DialogDescription>
                Bu pencereyi kapattıktan sonra giriş bilgilerini bir daha
                görüntüleyemezsiniz. Bilgileri müşteriye ilettiğinizden emin
                misiniz?
              </DialogDescription>
            </DialogHeader>

            <DialogFooter className="gap-2 sm:gap-2">
              <Button
                variant="outline"
                onClick={() => setShowWarning(false)}
                className="flex-1"
              >
                Bilgileri Tekrar Gör
              </Button>
              <Button onClick={confirmClose} className="flex-1">
                Üyeler Sayfasına Dön
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
