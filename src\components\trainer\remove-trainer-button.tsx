'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { removeTrainerFromGym } from '@/lib/actions/dashboard/company/gym-trainers';

interface RemoveTrainerButtonProps {
  trainerId: string;
  gymId: string;
  trainerName: string;
  onSuccess?: () => void;
  variant?: 'default' | 'dropdown';
}

export function RemoveTrainerButton({
  trainerId,
  gymId,
  trainerName,
  onSuccess,
  variant = 'default',
}: RemoveTrainerButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handleRemoveTrainer = async () => {
    try {
      setIsLoading(true);

      const result = await removeTrainerFromGym(trainerId, gymId);

      if (!result.success) {
        console.error('❌ Frontend: İşlem başarısız:', result.error);
        toast.error(result.error || 'Antrenör çıkarılırken bir hata oluştu');
        return;
      }

      // Callback çağır (sayfa yenilenmesi için)
      if (onSuccess) {
        onSuccess();
      } else {
        console.warn('⚠️ Frontend: onSuccess callback tanımlı değil');
      }
    } catch (error) {
      console.error('💥 Frontend: Beklenmeyen hata:', error);
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  if (variant === 'dropdown') {
    return (
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogTrigger asChild>
          <DropdownMenuItem className="cursor-pointer text-red-600 focus:bg-red-50 focus:text-red-700">
            <Trash2 className="mr-2 h-4 w-4" />
            Salondan Çıkar
          </DropdownMenuItem>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Antrenörü Salondan Çıkar</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div>
                <p>
                  <strong>{trainerName}</strong> adlı antrenörü salondan
                  çıkarmak istediğinizden emin misiniz?
                </p>
                <p className="mt-4">Bu işlem:</p>
                <ul className="mt-2 list-inside list-disc space-y-1">
                  <li>Antrenörün salon ile ilişkisini sonlandıracak</li>
                  <li>Antrenöre bildirim gönderilecek</li>
                  <li>
                    Antrenör bilgileri korunacak (başka salonlarda çalışabilir)
                  </li>
                </ul>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveTrainer}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Çıkarılıyor...' : 'Evet, Çıkar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:text-foreground hover:bg-muted/50 h-8 w-8 p-0 transition-colors"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Antrenör işlemleri</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <AlertDialogTrigger asChild>
            <DropdownMenuItem className="cursor-pointer text-red-600 focus:bg-red-50 focus:text-red-700">
              <Trash2 className="mr-2 h-4 w-4" />
              Salondan Çıkar
            </DropdownMenuItem>
          </AlertDialogTrigger>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Antrenörü Salondan Çıkar</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div>
              <p>
                <strong>{trainerName}</strong> adlı antrenörü salondan çıkarmak
                istediğinizden emin misiniz?
              </p>
              <p className="mt-4">Bu işlem:</p>
              <ul className="mt-2 list-inside list-disc space-y-1">
                <li>Antrenörün salon ile ilişkisini sonlandıracak</li>
                <li>Antrenöre bildirim gönderilecek</li>
                <li>
                  Antrenör bilgileri korunacak (başka salonlarda çalışabilir)
                </li>
              </ul>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>İptal</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleRemoveTrainer}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? 'Çıkarılıyor...' : 'Evet, Çıkar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
