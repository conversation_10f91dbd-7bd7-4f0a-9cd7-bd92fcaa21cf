"use client";

import { RouteError } from "@/components/errors/route-error";

export default function GymRootError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Salon Paneli Hatası"
      description="Salonlar bölümü yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: "/dashboard/gym" }}
    />
  );
}

