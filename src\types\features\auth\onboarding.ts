/**
 * Onboarding feature types
 *
 * Extracted from onboarding components and flows
 */

import type { PlatformRoles } from '../../database/enums';

/**
 * Onboarding step data
 */
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
  component?: React.ComponentType;
}

/**
 * Role selection data
 */
export interface RoleOption {
  id: PlatformRoles;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
  isAvailable: boolean;
  requiresVerification?: boolean;
}

/**
 * Onboarding form data
 */
export interface OnboardingFormData {
  selectedRole: PlatformRoles;
  personalInfo: {
    fullName: string;
    phone?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other';
  };
  preferences: {
    notifications: boolean;
    marketing: boolean;
    language: string;
  };
  roleSpecific?: {
    // For trainers
    specializations?: string[];
    certifications?: string[];
    experienceYears?: number;
    bio?: string;

    // For members
    fitnessGoals?: string[];
    experienceLevel?: 'beginner' | 'intermediate' | 'advanced';
    medicalConditions?: string[];

    // For managers
    gymName?: string;
    gymAddress?: string;
    businessType?: string;
  };
}

/**
 * Onboarding progress data
 */
export interface OnboardingProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: string[];
  isCompleted: boolean;
  startedAt: string;
  completedAt?: string;
}

/**
 * Verification status
 */
export interface VerificationStatus {
  isRequired: boolean;
  isCompleted: boolean;
  type: 'email' | 'phone' | 'document' | 'manual';
  status: 'pending' | 'verified' | 'rejected';
  submittedAt?: string;
  verifiedAt?: string;
  rejectionReason?: string;
}
