'use client';

import { useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';

export function ToastHandler() {
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const toastType = searchParams.get('toast');
    const message = searchParams.get('message');

    if (toastType && message) {
      // Toast mesajını göster
      if (toastType === 'error') {
        toast.error(message);
      } else if (toastType === 'success') {
        toast.success(message);
      } else if (toastType === 'info') {
        toast.info(message);
      }

      // URL'den parametreleri temizle
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('toast');
      newUrl.searchParams.delete('message');
      
      // URL'yi güncelle (say<PERSON> ye<PERSON>len<PERSON>)
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [searchParams, router]);

  return null; // Bu component hiçbir şey render etmez
}
