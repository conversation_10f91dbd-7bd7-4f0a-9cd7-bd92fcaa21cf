import { z } from 'zod';
import { COMMON_PATTERNS } from '@/lib/actions/shared/constants';

// Türkçe karakter desteği için regex
const TURKISH_NAME_REGEX = /^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/;

// Türk telefon numarası regex (daha spesifik)
const TURKISH_PHONE_REGEX = /^(\+90|0)?[5][0-9]{9}$/;

export const memberFormSchema = z
  .object({
    // Temel bilgiler
    full_name: z
      .string()
      .min(2, 'Ad soyad en az 2 karakter olmalıdır')
      .max(100, 'Ad soyad en fazla 100 karakter olabilir')
      .regex(TURKISH_NAME_REGEX, 'Sadece harf ve boşluk kullanabilirsiniz')
      .refine(val => val.trim().split(' ').length >= 2, 'Ad ve soyad giriniz'),

    age: z
      .number({
        message: '<PERSON><PERSON><PERSON><PERSON><PERSON> bir yaş giriniz',
      })
      .min(16, 'Ya<PERSON> en az 16 olmalıdır')
      .max(100, 'Yaş en fazla 100 olabilir')
      .int('Yaş tam sayı olmalıdır'),

    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say'], {
      message: 'Cinsiyet seçimi gereklidir',
    }),

    // İletişim bilgileri (opsiyonel ama en az biri gerekli)
    email: z
      .string()
      .optional()
      .refine(
        val => !val || val === '' || COMMON_PATTERNS.EMAIL_REGEX.test(val),
        'Geçerli bir e-posta adresi giriniz'
      ),

    phone_number: z
      .string()
      .optional()
      .refine(
        val =>
          !val ||
          val === '' ||
          TURKISH_PHONE_REGEX.test(val.replace(/\s/g, '')),
        'Geçerli bir telefon numarası giriniz (örn: 0532 123 45 67)'
      ),

    // Fiziksel bilgiler (opsiyonel)
    height_cm: z
      .number({
        message: 'Geçerli bir boy değeri giriniz',
      })
      .min(100, 'Boy en az 100 cm olmalıdır')
      .max(250, 'Boy en fazla 250 cm olabilir')
      .int('Boy tam sayı olmalıdır')
      .optional(),

    weight_kg: z
      .number({
        message: 'Geçerli bir kilo değeri giriniz',
      })
      .min(30, 'Kilo en az 30 kg olmalıdır')
      .max(300, 'Kilo en fazla 300 kg olabilir')
      .int('Kilo tam sayı olmalıdır')
      .optional(),

    fitness_goal: z.string().optional(),

    // Misafir mode kontrolü
    is_guest_mode: z.boolean(),

    // Hidden fields
    gymId: z.string().min(1, 'Gym ID gereklidir'),
  })
  .refine(
    data => {
      // Misafir mode değilse email veya telefon gerekli
      if (!data.is_guest_mode) {
        const hasEmail = data.email && data.email.trim() !== '';
        const hasPhone = data.phone_number && data.phone_number.trim() !== '';
        return hasEmail || hasPhone;
      }
      return true;
    },
    {
      message: 'E-posta veya telefon numarasından en az biri gereklidir',
      path: ['email'], // Hata mesajını email alanında göster
    }
  );

// Trainer variant için özel schema (telefon alanı olmadan)
export const memberFormSchemaTrainer = z
  .object({
    // Temel bilgiler
    full_name: z
      .string()
      .min(2, 'Ad soyad en az 2 karakter olmalıdır')
      .max(100, 'Ad soyad en fazla 100 karakter olabilir')
      .regex(TURKISH_NAME_REGEX, 'Sadece harf ve boşluk kullanabilirsiniz')
      .refine(val => val.trim().split(' ').length >= 2, 'Ad ve soyad giriniz'),

    age: z
      .number({
        message: 'Geçerli bir yaş giriniz',
      })
      .min(16, 'Yaş en az 16 olmalıdır')
      .max(100, 'Yaş en fazla 100 olabilir')
      .int('Yaş tam sayı olmalıdır'),

    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say'], {
      message: 'Cinsiyet seçimi gereklidir',
    }),

    // İletişim bilgileri (sadece email, misafir modda opsiyonel)
    email: z
      .string()
      .optional()
      .refine(
        val => !val || val === '' || COMMON_PATTERNS.EMAIL_REGEX.test(val),
        'Geçerli bir e-posta adresi giriniz'
      ),

    // Fiziksel bilgiler (opsiyonel)
    height_cm: z
      .number({
        message: 'Geçerli bir boy değeri giriniz',
      })
      .min(100, 'Boy en az 100 cm olmalıdır')
      .max(250, 'Boy en fazla 250 cm olabilir')
      .int('Boy tam sayı olmalıdır')
      .optional(),

    weight_kg: z
      .number({
        message: 'Geçerli bir kilo değeri giriniz',
      })
      .min(30, 'Kilo en az 30 kg olmalıdır')
      .max(300, 'Kilo en fazla 300 kg olabilir')
      .int('Kilo tam sayı olmalıdır')
      .optional(),

    fitness_goal: z.string().optional(),

    // Misafir mode kontrolü
    is_guest_mode: z.boolean(),

    // Hidden fields
    gymId: z.string().min(1, 'Gym ID gereklidir'),
  })
  .refine(
    data => {
      // Misafir mode değilse email gerekli (telefon alanı yok)
      if (!data.is_guest_mode) {
        const hasEmail = data.email && data.email.trim() !== '';
        return hasEmail;
      }
      return true;
    },
    {
      message: 'E-posta adresi gereklidir',
      path: ['email'],
    }
  );

export type MemberFormData = z.infer<typeof memberFormSchema>;

// Form default değerleri
export const memberFormDefaults: Partial<MemberFormData> = {
  full_name: '',
  email: '',
  phone_number: '',
  fitness_goal: '',
  is_guest_mode: false,
};

// Hata mesajları için yardımcı fonksiyon
export const getFieldError = (
  errors: any,
  fieldName: string
): string | undefined => {
  const error = errors[fieldName];
  return error?.message;
};

// Form verilerini FormData'ya dönüştürme yardımcısı
export const convertToFormData = (data: MemberFormData): FormData => {
  const formData = new FormData();

  // String alanlar
  formData.append('gymId', data.gymId);
  formData.append('full_name', data.full_name);
  formData.append('gender', data.gender);

  // Number alanlar
  formData.append('age', data.age.toString());

  // Opsiyonel string alanlar
  if (data.email) formData.append('email', data.email);
  if (data.phone_number) formData.append('phone_number', data.phone_number);
  if (data.fitness_goal) formData.append('fitness_goal', data.fitness_goal);

  // Opsiyonel number alanlar
  if (data.height_cm) formData.append('height_cm', data.height_cm.toString());
  if (data.weight_kg) formData.append('weight_kg', data.weight_kg.toString());

  // Boolean alan
  if (data.is_guest_mode) formData.append('is_guest_mode', 'on');

  return formData;
};
