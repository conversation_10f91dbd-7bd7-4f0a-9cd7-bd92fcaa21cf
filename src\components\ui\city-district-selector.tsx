'use client';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CITIES } from '@/lib/constants';

interface CityDistrictSelectorProps {
  cityValue: string;
  districtValue: string;
  onCityChange: (city: string) => void;
  onDistrictChange: (district: string) => void;
  cityLabel?: string;
  districtLabel?: string;
  cityPlaceholder?: string;
  districtPlaceholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  triggerClassName?: string;
}

export function CityDistrictSelector({
  cityValue,
  districtValue,
  onCityChange,
  onDistrictChange,
  cityLabel = 'Şehir',
  districtLabel = 'İlçe',
  cityPlaceholder = 'Şehir seçin',
  districtPlaceholder = 'İlçe seçin',
  required = false,
  disabled = false,
  className = '',
  triggerClassName = '',
}: CityDistrictSelectorProps) {
  const selectedCity = CITIES.find(city => city.id.toString() === cityValue);
  const districts = selectedCity?.districts || [];

  const handleCityChange = (city: string) => {
    onCityChange(city);
    // Reset district when city changes
    onDistrictChange('');
  };

  return (
    <div className={`grid grid-cols-1 gap-4 md:grid-cols-2 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="city">{cityLabel}</Label>
        <Select
          value={cityValue}
          onValueChange={handleCityChange}
          disabled={disabled}
          required={required}
        >
          <SelectTrigger className={triggerClassName}>
            <SelectValue placeholder={cityPlaceholder} />
          </SelectTrigger>
          <SelectContent>
            {CITIES.map(city => (
              <SelectItem key={city.id} value={city.id.toString()}>
                {city.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="district">{districtLabel}</Label>
        <Select
          value={districtValue}
          onValueChange={onDistrictChange}
          disabled={!cityValue || disabled}
          required={required}
        >
          <SelectTrigger className={triggerClassName}>
            <SelectValue placeholder={districtPlaceholder} />
          </SelectTrigger>
          <SelectContent>
            {districts.map(district => (
              <SelectItem key={district} value={district}>
                {district}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
