'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { addDays, addWeeks, format, startOfWeek } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { useTransition } from 'react';
import {
  rescheduleAppointment,
  checkTrainerAvailability,
  findExistingGroupAppointment,
  quickCreateAppointment,
} from '@/lib/actions/dashboard/company/appointment-actions';
import {
  moveParticipantToAppointment,
  moveParticipantToDateTime,
} from '@/lib/actions/dashboard/company/participant-actions';
import {
  useParams,
  useRouter,
  useSearchParams,
  usePathname,
} from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

import {
  ChevronLeft,
  ChevronRight,
  Users,
  User2,
  Loader2,
  Plus,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getGymMembers, getMemberPackages } from '@/lib/actions/all-actions';
import { getGymTrainersWithPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';
import type { TrainerPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';
import { AppointmentDetailDialog } from './appointment-detail-dialog';

// Minimal type for what we render
interface AppointmentItem {
  id: string;
  appointment_date: string;
  status?: string | null;
  appointment_type?: string | null;
  max_participants?: number;
  trainer_profile_id?: string | null;
  gym_id?: string | null;
  gym_package_id?: string | null;
  trainer_profile?: { full_name?: string | null };
  participants?: Array<{
    id: string; // appointment_participant id
    status?: string | null;
    membership_package?: {
      id?: string | null; // gym_membership_packages id
      gym_package_id?: string | null;
      gym_package?: {
        name?: string | null;
        session_duration_minutes?: number | null;
      } | null;
      membership?: { profile?: { full_name?: string | null } | null } | null;
    } | null;
  }>;
  notes?: string | null;
}

function buildSlotRows(timeSlots?: string[]): string[] {
  if (!timeSlots || timeSlots.length === 0) {
    return Array.from(
      { length: 13 },
      (_, i) => `${String(8 + i).padStart(2, '0')}:00`
    );
  }
  const uniq = Array.from(new Set(timeSlots));
  return uniq;
}

export function AppointmentsCalendar({
  appointments,
  timeSlots,
  fixedTrainerId,
  permissions,
  mode = 'company_manager',
}: {
  appointments: AppointmentItem[];
  timeSlots?: string[];
  showTrainerFilter?: boolean;
  fixedTrainerId?: string;
  permissions?: TrainerPermissions;
  mode?: 'company_manager' | 'trainer';
}) {
  const [weekStart, setWeekStart] = useState<Date>(
    startOfWeek(new Date(), { weekStartsOn: 1 })
  );
  const [, startTransition] = useTransition();
  const params = useParams();
  const gymId = params?.gymId as string;

  // URL üzerinden eğitmen filtresi okuma
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // Drag state & UX
  const [draggingId, setDraggingId] = useState<string | null>(null);
  const [dragOverKey, setDragOverKey] = useState<string | null>(null);
  const [isDropping, setIsDropping] = useState(false);
  const [moveModeId, setMoveModeId] = useState<string | null>(null); // keyboard/touch pick up
  const [confirmMove, setConfirmMove] = useState<{
    id: string;
    from: Date;
    to: Date;
  } | null>(null);

  const getSessionDuration = (apt?: AppointmentItem): number => {
    const dur =
      apt?.participants?.[0]?.membership_package?.gym_package
        ?.session_duration_minutes;
    if (typeof dur === 'number' && dur > 0) return dur;
    if (
      apt?.appointment_type === 'appointment_vip' ||
      apt?.appointment_type === 'vip' ||
      apt?.appointment_type === 'personal'
    )
      return 60;
    return 60; // default
  };

  const computeCellKey = (day: Date, hour: number) =>
    `${format(day, 'yyyy-MM-dd')}-${hour}`;
  const findAppointment = (id?: string | null) =>
    appointments.find(a => a.id === id);

  const isCellValidTarget = (id: string | null, day: Date, hour: number) => {
    if (!id) return false;
    const appt = findAppointment(id);
    if (!appt) return false;
    const srcDate = new Date(appt.appointment_date);
    if (
      srcDate.getFullYear() === day.getFullYear() &&
      srcDate.getMonth() === day.getMonth() &&
      srcDate.getDate() === day.getDate() &&
      srcDate.getHours() === hour
    ) {
      return false; // same slot
    }
    // Prevent immediate trainer double-booking in same hour (client-side hint)
    const targetKey = computeCellKey(day, hour);
    const existing = byDayHour.get(targetKey) || [];
    if (
      existing.some(
        x =>
          x.trainer_profile_id &&
          x.trainer_profile_id === appt.trainer_profile_id
      )
    )
      return false;
    return true;
  };

  const doReschedule = async (id: string, day: Date, hour: number) => {
    const date = new Date(day);
    date.setHours(hour, 0, 0, 0);
    const iso = date.toISOString();

    const appt = appointments.find(x => x.id === id);

    // Optional confirmation when moving to a different day
    if (appt) {
      const src = new Date(appt.appointment_date);
      const sameDay =
        src.getFullYear() === date.getFullYear() &&
        src.getMonth() === date.getMonth() &&
        src.getDate() === date.getDate();
      if (!sameDay) {
        setConfirmMove({ id, from: src, to: date });
        return;
      }
    }

    await performReschedule(id, iso);
  };

  const performReschedule = async (id: string, iso: string) => {
    const appt = appointments.find(x => x.id === id);
    const trainerId = appt?.trainer_profile_id as string;
    const duration = getSessionDuration(appt);

    setIsDropping(true);
    const availability = await checkTrainerAvailability(
      trainerId,
      iso,
      duration,
      gymId
    );
    if (!availability.success) {
      setIsDropping(false);
      toast.error(availability.error || 'Müsaitlik kontrolü başarısız');
      return;
    }
    const { available, existingGroupAppointment } = availability.data!;
    if (!available || existingGroupAppointment) {
      setIsDropping(false);
      toast.error('Bu saat uygun değil. Lütfen başka bir saat seçin.');
      return;
    }

    startTransition(async () => {
      const res = await rescheduleAppointment(id, iso, gymId);
      setIsDropping(false);
      setDraggingId(null);
      setMoveModeId(null);
      setDragOverKey(null);
      setConfirmMove(null);
      if (res.success) toast.success('Randevu yeniden planlandı');
      else toast.error(res.error || 'Randevu yeniden planlanamadı');
    });
  };

  const handleDrop = async (
    e: React.DragEvent<HTMLDivElement>,
    day: Date,
    hour: number
  ) => {
    e.preventDefault();

    // 1) Katılımcı sürüklendi mi?
    const participantId = e.dataTransfer.getData('text/participant-id');
    if (participantId) {
      const key = `${format(day, 'yyyy-MM-dd')}-${hour}`;
      const listAtTarget = byDayHour.get(key) || [];
      // Yalnızca boş saate bırakmaya izin ver
      if (listAtTarget.length > 0) {
        return;
      }
      // Etkin eğitmen (takvim zaten eğitmene filtreli)
      const activeTrainerId =
        fixedTrainerId || (searchParams.get('trainerId') as string) || '';
      if (!activeTrainerId) {
        toast.error('Önce bir antrenör seçin');
        return;
      }
      const date = new Date(day);
      date.setHours(hour, 0, 0, 0);
      const iso = date.toISOString();
      try {
        setIsDropping(true);
        const res = await moveParticipantToDateTime(
          participantId,
          activeTrainerId,
          iso,
          gymId
        );
        setIsDropping(false);
        if (res.success) {
          toast.success('Üye yeni saate taşındı');
          router.refresh();
        } else {
          toast.error(res.error || 'Taşıma başarısız');
        }
      } catch (err: any) {
        setIsDropping(false);
        toast.error(err?.message || 'Taşıma başarısız');
      }
      return;
    }

    // 2) Randevu kartı sürüklendi (mevcut davranış)
    const id = e.dataTransfer.getData('text/appointment-id');
    if (!id) return;
    if (!isCellValidTarget(id, day, hour)) {
      toast.error('Geçersiz hedef. Bu hücreye bırakılamaz.');
      return;
    }
    await doReschedule(id, day, hour);
  };

  const allowDrop = (e: React.DragEvent, valid: boolean) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = valid ? 'move' : 'none';
  };

  const handleDragStart = (
    e: React.DragEvent<HTMLElement>,
    apt: AppointmentItem
  ) => {
    e.dataTransfer.setData('text/appointment-id', apt.id);
    e.dataTransfer.effectAllowed = 'move';
    // Use the element as drag image for better UX
    const target = e.currentTarget as HTMLElement;
    try {
      e.dataTransfer.setDragImage(target, 10, 10);
    } catch {}
    setDraggingId(apt.id);
  };

  const handleDragEnd = () => {
    setDraggingId(null);
    setDragOverKey(null);
  };

  const [dialogOpen, setDialogOpen] = useState(false);
  const [selected, setSelected] = useState<AppointmentItem | null>(null);

  // Quick add state
  const router = useRouter();
  const [quickAddOpen, setQuickAddOpen] = useState(false);
  const [quickAddCell, setQuickAddCell] = useState<{
    date: Date;
    hour: number;
  } | null>(null);
  const [members, setMembers] = useState<any[]>([]);
  const [trainers, setTrainers] = useState<any[]>([]);
  const [packages, setPackages] = useState<any[]>([]);
  const [selectedMemberId, setSelectedMemberId] = useState<string>('');
  const [selectedTrainerId, setSelectedTrainerId] = useState<string>('');
  const [selectedPackageId, setSelectedPackageId] = useState<string>('');
  const [quickLoading, setQuickLoading] = useState(false);

  const days = useMemo(
    () => Array.from({ length: 7 }, (_, i) => addDays(weekStart, i)),
    [weekStart]
  );

  // URL'den gelen trainerId'ye göre görünür randevular (anında filtre etkisi)
  const visibleAppointments = useMemo(() => {
    // fixedTrainerId varsa onu zorunlu uygula (trainer sayfası)
    if (fixedTrainerId) {
      return appointments.filter(
        a => (a.trainer_profile_id || '') === fixedTrainerId
      );
    }
    const trainerId = (searchParams.get('trainerId') as string) || '';
    // Antrenör seçilmemişse boş array döndür
    if (!trainerId) return [];
    return appointments.filter(a => (a.trainer_profile_id || '') === trainerId);
  }, [appointments, searchParams, fixedTrainerId]);

  const byDayHour = useMemo(() => {
    const map = new Map<string, AppointmentItem[]>();
    for (const a of visibleAppointments) {
      const d = new Date(a.appointment_date);
      const key = `${format(d, 'yyyy-MM-dd')}-${d.getHours()}`;
      const list = map.get(key) || [];
      list.push(a);
      map.set(key, list);
    }
    return map;
  }, [visibleAppointments]);

  const openDetail = (apt: AppointmentItem) => {
    setSelected(apt);
    setDialogOpen(true);
  };

  const openQuickAdd = (day: Date, hour: number) => {
    const date = new Date(day);
    date.setHours(hour, 0, 0, 0);
    setQuickAddCell({ date, hour });
    setSelectedMemberId('');
    // Mevcut antrenör filtresi seçiliyse onu varsayılan yap
    const prefillTrainer = (searchParams.get('trainerId') as string) || '';
    setSelectedTrainerId(prefillTrainer);
    setSelectedPackageId('');
    setPackages([]);
    setQuickAddOpen(true);
  };

  useEffect(() => {
    const loadBaseData = async () => {
      if (!gymId) return;
      try {
        const [mRes, tRes] = await Promise.all([
          getGymMembers(gymId),
          getGymTrainersWithPermissions(gymId),
        ]);
        if (mRes.success && mRes.data)
          setMembers((mRes.data?.members || []).filter((m: any) => m.member));
        if (tRes.success) setTrainers(tRes.data || []);
      } catch (e) {
        // ignore
      }
    };
    // Takvim açıldığında da yükle (sadece quickAdd değil)
    loadBaseData();
  }, [gymId]);

  const handleMemberChange = async (memberId: string) => {
    setSelectedMemberId(memberId);
    setSelectedPackageId('');
    setPackages([]);
    if (!gymId) return;
    try {
      const res = await getMemberPackages(gymId, memberId);
      if (res.success) setPackages(res.data || []);
      else toast.error(res.error || 'Paketler yüklenemedi');
    } catch (e: any) {
      toast.error(e?.message || 'Paketler yüklenemedi');
    }
  };

  const confirmQuickAdd = async () => {
    if (!quickAddCell) return;
    if (!selectedMemberId || !selectedPackageId || !selectedTrainerId) {
      toast.error('Lütfen üye, paket ve antrenör seçin');
      return;
    }
    const pkg = packages.find((p: any) => p.id === selectedPackageId);
    const pkgType = pkg?.gym_package?.package_type || 'appointment_standard';
    const duration = pkg?.gym_package?.session_duration_minutes || 60;
    const iso = quickAddCell.date.toISOString();

    setQuickLoading(true);
    try {
      // Önce var olan grup randevusuna katılma şansı
      const groupRes = await findExistingGroupAppointment(
        selectedTrainerId,
        iso,
        pkgType,
        gymId
      );
      if (groupRes.success && groupRes.data) {
        const joinRes = await quickCreateAppointment(
          'join-group',
          selectedPackageId,
          groupRes.data.id
        );
        if (joinRes.success) {
          toast.success('Mevcut gruba katıldı');
          setQuickAddOpen(false);
          router.refresh();
          return;
        } else {
          toast.error(joinRes.error || 'Katılım başarısız');
          return;
        }
      }

      // Aksi halde, müsaitlik kontrolü ve yeni oluşturma
      const avail = await checkTrainerAvailability(
        selectedTrainerId,
        iso,
        duration,
        gymId
      );
      if (!avail.success) {
        toast.error(avail.error || 'Müsaitlik kontrolü başarısız');
        return;
      }
      if (!avail.data?.available || avail.data?.existingGroupAppointment) {
        toast.error('Bu saat uygun değil');
        return;
      }
      const createRes = await quickCreateAppointment(
        'create-new',
        selectedPackageId,
        undefined,
        selectedTrainerId,
        iso,
        gymId
      );
      if (createRes.success) {
        toast.success('Randevu oluşturuldu');
        setQuickAddOpen(false);
        router.refresh();
      } else {
        toast.error(createRes.error || 'Randevu oluşturulamadı');
      }
    } catch (e: any) {
      toast.error(e?.message || 'İşlem başarısız');
    } finally {
      setQuickLoading(false);
    }
  };

  return (
    <div
      className="space-y-4 px-8"
      tabIndex={-1}
      onKeyDown={e => {
        if (e.key === 'Escape') {
          setMoveModeId(null);
          setDraggingId(null);
          setDragOverKey(null);
        }
      }}
    >
      {/* Eğitmen filtresi (sadece manager için) */}
      {!fixedTrainerId && (
        <div className="mb-4 flex items-center gap-2">
          <div className="text-muted-foreground text-sm">Antrenör:</div>
          <Select
            value={(searchParams.get('trainerId') as string) || ''}
            onValueChange={val => {
              const sp = new URLSearchParams(searchParams.toString());
              if (!val) sp.delete('trainerId');
              else sp.set('trainerId', val);
              router.push(`${pathname}?${sp.toString()}`);
            }}
          >
            <SelectTrigger className="min-w-[220px]">
              <SelectValue placeholder="Antrenör seçin" />
            </SelectTrigger>
            <SelectContent>
              {trainers.map((t: any) => (
                <SelectItem
                  key={t.trainer_profile_id}
                  value={t.trainer_profile_id}
                >
                  {t.trainer?.full_name || t.trainer_profile_id}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {isDropping && (
        <div className="bg-background/50 absolute inset-0 z-40 grid place-items-center backdrop-blur-[1px]">
          <div className="text-muted-foreground flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="text-sm">Taşınıyor...</span>
          </div>
        </div>
      )}

      {/* Haftalık tablo */}
      {!fixedTrainerId && !(searchParams.get('trainerId') as string) ? (
        <div className="flex flex-col items-center justify-center px-4 py-16 text-center">
          <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900/20">
            <Users className="h-8 w-8 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
            Antrenör Seçin
          </h3>
          <p className="max-w-md text-gray-600 dark:text-gray-400">
            Randevu takvimini görüntülemek için yukarıdan bir antrenör seçin.
          </p>
        </div>
      ) : (
        <div className="relative">
          {/* Sol navigasyon butonu */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setWeekStart(d => addWeeks(d, -1))}
            className="absolute top-1/2 -left-10 z-10 -translate-y-1/2 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl dark:bg-gray-950/90"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Sağ navigasyon butonu */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setWeekStart(d => addWeeks(d, 1))}
            className="absolute top-1/2 -right-10 z-10 -translate-y-1/2 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl dark:bg-gray-950/90"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          <div className="relative overflow-x-auto rounded-lg border bg-white shadow-sm dark:bg-gray-950">
            <div className="grid min-w-[1000px] grid-cols-[80px_1fr_1fr_1fr_1fr_1fr_1fr_1fr] gap-0">
              {/* Header row */}
              <div className="text-muted-foreground border-r border-b bg-gradient-to-br from-gray-50 to-gray-100/50 p-3 font-semibold dark:from-gray-900/30 dark:to-gray-800/20">
                Saat
              </div>
              {days.map(d => {
                const dayName = format(d, 'EEEE', { locale: tr });
                const monthName = format(d, 'MMMM', { locale: tr });
                const cap = (s: string) =>
                  s.charAt(0).toLocaleUpperCase('tr-TR') + s.slice(1);
                const isToday =
                  format(d, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
                return (
                  <div
                    key={d.toISOString()}
                    className={`border-b p-3 text-center text-sm font-semibold transition-colors ${
                      isToday
                        ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50 text-blue-700 dark:border-blue-700 dark:from-blue-900/30 dark:to-blue-800/20 dark:text-blue-300'
                        : 'bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-900/30 dark:to-gray-800/20'
                    }`}
                  >
                    <div
                      className={
                        isToday ? 'text-blue-800 dark:text-blue-200' : ''
                      }
                    >
                      {cap(dayName)}
                    </div>
                    <div
                      className={`text-xs sm:text-sm ${isToday ? 'text-blue-600 dark:text-blue-400' : 'text-muted-foreground'}`}
                    >
                      {format(d, 'd')} {cap(monthName)}
                    </div>
                  </div>
                );
              })}

              {/* Time rows by gym time_slots */}
              {buildSlotRows(timeSlots).map(slot => {
                const [hStr] = slot.split(':');
                const hour = parseInt(hStr, 10);
                return (
                  <React.Fragment key={slot}>
                    {/* time label */}
                    <div className="text-muted-foreground flex min-h-16 items-center justify-center border-r bg-gradient-to-r from-gray-50/50 to-gray-100/30 p-3 text-xs font-semibold dark:from-gray-900/20 dark:to-gray-800/10">
                      <span className="text-center">{slot}</span>
                    </div>
                    {/* day cells */}
                    {days.map(d => {
                      const key = `${format(d, 'yyyy-MM-dd')}-${hour}`;
                      const list = byDayHour.get(key) || [];
                      const activeId = draggingId || moveModeId;
                      const valid = activeId
                        ? isCellValidTarget(activeId, d, hour)
                        : false;
                      const isOver = dragOverKey === key;
                      return (
                        <div
                          key={key}
                          tabIndex={0}
                          role="button"
                          aria-label={`${format(d, 'EEEE', { locale: tr })} ${slot}`}
                          data-dropeffect={
                            activeId ? (valid ? 'move' : 'none') : undefined
                          }
                          className={
                            `min-h-16 border-r border-b p-2 transition-all duration-200 ` +
                            `${activeId ? (valid ? 'cursor-move hover:bg-emerald-50/90 hover:shadow-sm dark:hover:bg-emerald-900/30' : 'cursor-not-allowed hover:bg-red-50/80 dark:hover:bg-red-900/20') : 'hover:bg-blue-50/40 hover:shadow-sm dark:hover:bg-blue-900/10'} ` +
                            `${isOver && activeId ? (valid ? 'bg-emerald-50/80 shadow-md ring-2 ring-emerald-400/70' : 'bg-red-50/80 ring-2 ring-red-400/70') : ''}`
                          }
                          onClick={() => {
                            if (
                              moveModeId &&
                              valid &&
                              permissions?.appointments.update !== false
                            )
                              doReschedule(moveModeId, d, hour);
                          }}
                          onKeyDown={e => {
                            if (
                              (e.key === 'Enter' || e.key === ' ') &&
                              moveModeId &&
                              valid &&
                              permissions?.appointments.update !== false
                            ) {
                              e.preventDefault();
                              doReschedule(moveModeId, d, hour);
                            }
                          }}
                          onDragEnter={() => {
                            if (activeId) setDragOverKey(key);
                          }}
                          onDragLeave={() => {
                            setDragOverKey(prev =>
                              prev === key ? null : prev
                            );
                          }}
                          onDragOver={e => allowDrop(e, true)}
                          onDrop={e => handleDrop(e, d, hour)}
                        >
                          <div className="flex flex-col gap-1">
                            {list.length === 0 &&
                              permissions?.appointments.create !== false && (
                                <div className="flex justify-end">
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    className="text-muted-foreground hover:text-foreground h-6 w-6"
                                    onClick={e => {
                                      e.stopPropagation();
                                      openQuickAdd(d, hour);
                                    }}
                                    aria-label="Hızlı randevu ekle"
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            {list.map(a => (
                              <div
                                key={a.id}
                                className={`relative rounded-lg px-3 py-2 transition-all duration-200 ${draggingId === a.id ? 'scale-[0.98] opacity-80 shadow-lg' : 'hover:scale-[1.02] hover:shadow-lg'} ${typeClasses(a.appointment_type)} ${statusTone(a.status || '')}`}
                                draggable={
                                  permissions?.appointments.update !== false
                                }
                                data-grabbed={draggingId === a.id || undefined}
                                onClick={() => openDetail(a)}
                                onDragStart={e => handleDragStart(e as any, a)}
                                onDragEnd={handleDragEnd}
                                onDragOver={e => allowDrop(e, true)}
                                onDrop={async e => {
                                  // Katılımcıyı bu randevuya bırakma
                                  e.preventDefault();
                                  const partId = e.dataTransfer.getData(
                                    'text/participant-id'
                                  );
                                  if (partId) {
                                    try {
                                      const res =
                                        await moveParticipantToAppointment(
                                          partId,
                                          a.id,
                                          gymId
                                        );
                                      if (res.success) {
                                        toast.success('Üye randevuya taşındı');
                                        router.refresh();
                                      } else {
                                        toast.error(
                                          res.error || 'Taşıma başarısız'
                                        );
                                      }
                                    } catch (err: any) {
                                      toast.error(
                                        err?.message || 'Taşıma başarısız'
                                      );
                                    }
                                    return;
                                  }
                                }}
                              >
                                {/* Üst: Saat + Durum ve Paket adı */}
                                <div className="mb-2 flex items-center justify-between gap-2">
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm font-semibold">
                                      {format(
                                        new Date(a.appointment_date),
                                        'HH:mm'
                                      )}
                                    </span>
                                  </div>
                                  {(() => {
                                    const name =
                                      a.participants?.[0]?.membership_package
                                        ?.gym_package?.name;
                                    return name ? (
                                      <div className="text-xs font-medium">
                                        {name}
                                      </div>
                                    ) : null;
                                  })()}
                                </div>

                                {/* Katılımcı listesi */}
                                <div className="space-y-1">
                                  <div className="flex flex-col gap-1">
                                    {(a.participants || []).map(p => (
                                      <div
                                        key={p.id}
                                        className="flex items-center justify-between rounded border bg-white/60 px-2 py-1 text-xs dark:bg-white/5"
                                        draggable={
                                          permissions?.appointments.update !==
                                          false
                                        }
                                        onDragStart={e => {
                                          e.stopPropagation();
                                          e.dataTransfer.setData(
                                            'text/participant-id',
                                            p.id
                                          );
                                          e.dataTransfer.setData(
                                            'text/source-appointment-id',
                                            a.id
                                          );
                                        }}
                                      >
                                        <div className="truncate">
                                          {p.membership_package?.membership
                                            ?.profile?.full_name || 'Üye'}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>

                                {/* Alt: n/maks bilgisi */}
                                <div className="text-muted-foreground mt-2 flex items-center justify-between text-[10px]">
                                  <div className="flex items-center gap-1">
                                    {a.appointment_type === 'personal' ? (
                                      <User2 className="h-3 w-3" />
                                    ) : (
                                      <Users className="h-3 w-3" />
                                    )}
                                  </div>
                                  <span className="font-medium">
                                    {a.participants?.length ?? 0}/
                                    {a.max_participants ?? 1}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Quick Add Dialog */}
      <Dialog open={quickAddOpen} onOpenChange={setQuickAddOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Hızlı Randevu Ekle</DialogTitle>
            <DialogDescription>
              {quickAddCell
                ? `${format(quickAddCell.date, 'EEEE', { locale: tr })} ${format(quickAddCell.date, 'd MMMM yyyy HH:mm', { locale: tr })}`
                : ''}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Member select */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Üye</label>
              <Select
                value={selectedMemberId}
                onValueChange={handleMemberChange}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Üye seçin" />
                </SelectTrigger>
                <SelectContent>
                  {members.map((m: any) => (
                    <SelectItem
                      key={m.member.id}
                      value={m.member.id}
                      disabled={!m.active_package_count || m.active_package_count === 0}
                    >
                      {m.member.full_name}
                      {(!m.active_package_count || m.active_package_count === 0) && (
                        <span className="text-muted-foreground ml-2 text-xs">
                          (Aktif paketi yok)
                        </span>
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Package select */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Paket</label>
              <Select
                value={selectedPackageId}
                onValueChange={setSelectedPackageId}
                disabled={!selectedMemberId}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      selectedMemberId ? 'Paket seçin' : 'Önce üye seçin'
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {packages.map((p: any) => (
                    <SelectItem key={p.id} value={p.id}>
                      {p.gym_package?.name} (
                      {p.computed_remaining_sessions ??
                        p.remaining_sessions ??
                        0}
                      )
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Trainer select */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Antrenör</label>
              <Select
                value={selectedTrainerId}
                onValueChange={setSelectedTrainerId}
                disabled={!!fixedTrainerId}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Antrenör seçin" />
                </SelectTrigger>
                <SelectContent>
                  {(fixedTrainerId
                    ? trainers.filter(
                        (t: any) => t.trainer_profile_id === fixedTrainerId
                      )
                    : trainers
                  ).map((t: any) => (
                    <SelectItem
                      key={t.trainer_profile_id}
                      value={t.trainer_profile_id}
                    >
                      {t.trainer?.full_name || t.trainer_profile_id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end gap-2 pt-2">
              <Button variant="outline" onClick={() => setQuickAddOpen(false)}>
                İptal
              </Button>
              <Button
                onClick={confirmQuickAdd}
                disabled={
                  quickLoading ||
                  !selectedMemberId ||
                  !selectedPackageId ||
                  !selectedTrainerId
                }
              >
                {quickLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Kaydet
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Detail Dialog */}
      <AppointmentDetailDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        appointment={selected as any}
        mode={mode}
        permissions={permissions as any}
      />

      {/* Confirmation Dialog */}
      <Dialog open={!!confirmMove} onOpenChange={() => setConfirmMove(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Randevu Taşıma Onayı</DialogTitle>
            <DialogDescription>
              Randevuyu farklı bir güne taşımak istediğinize emin misiniz?
            </DialogDescription>
          </DialogHeader>
          {confirmMove && (
            <div className="space-y-4">
              <div className="text-sm">
                <div className="font-medium">Mevcut:</div>
                <div className="text-muted-foreground">
                  {format(confirmMove.from, 'dd MMMM yyyy, EEEE HH:mm', {
                    locale: tr,
                  })}
                </div>
              </div>
              <div className="text-sm">
                <div className="font-medium">Yeni:</div>
                <div className="text-muted-foreground">
                  {format(confirmMove.to, 'dd MMMM yyyy, EEEE HH:mm', {
                    locale: tr,
                  })}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setConfirmMove(null)}>
                  İptal
                </Button>
                <Button
                  onClick={() => {
                    const iso = confirmMove.to.toISOString();
                    performReschedule(confirmMove.id, iso);
                  }}
                >
                  Taşı
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

function typeClasses(type?: string | null) {
  if (type === 'personal' || type === 'vip') {
    return 'bg-gradient-to-br from-purple-50/90 to-purple-100/60 dark:from-purple-950/40 dark:to-purple-900/30 border border-purple-200/80 dark:border-purple-800/50 hover:from-purple-100/90 hover:to-purple-150/70 dark:hover:from-purple-900/50 dark:hover:to-purple-800/40';
  }
  return 'bg-gradient-to-br from-teal-50/90 to-teal-100/60 dark:from-teal-950/40 dark:to-teal-900/30 border border-teal-200/80 dark:border-teal-800/50 hover:from-teal-100/90 hover:to-teal-150/70 dark:hover:from-teal-900/50 dark:hover:to-teal-800/40'; // group
}

function statusTone(status: string) {
  switch (status) {
    case 'scheduled':
      return 'ring-1 ring-blue-200 dark:ring-blue-800 bg-blue-50/80 dark:bg-blue-950/60 border-blue-300 dark:border-blue-700 shadow-sm hover:shadow-md';
    case 'completed':
      return 'ring-1 ring-green-200 dark:ring-green-800 bg-green-50/80 dark:bg-green-950/60 border-green-300 dark:border-green-700 opacity-90 shadow-inner';
    case 'cancelled':
      return 'ring-1 ring-red-200 dark:ring-red-800 bg-red-50/80 dark:bg-red-950/60 border-red-300 dark:border-red-700 opacity-75 grayscale-[0.3]';
    default:
      return 'ring-1 ring-gray-200 dark:ring-gray-700 bg-gray-50/80 dark:bg-gray-900/60 border-gray-300 dark:border-gray-600 shadow-sm';
  }
}
