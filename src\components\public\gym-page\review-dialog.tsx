import { memo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { StarRating } from "@/components/ui/star-rating";
import { Textarea } from "@/components/ui/textarea";

interface ReviewDialogProps {
  gymName: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  rating: number;
  comment: string;
  onRatingChange: (rating: number) => void;
  onCommentChange: (comment: string) => void;
  onSubmit: () => Promise<void>;
  hasExistingReview: boolean;
}

export const ReviewDialog = memo(function ReviewDialog({
  gymName,
  isOpen,
  onOpenChange,
  rating,
  comment,
  onRatingChange,
  onCommentChange,
  onSubmit,
  hasExistingReview
}: ReviewDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button size="lg" className="duration-300">
          {hasExistingReview ? "Değerlendirmeyi Düzenle" : "Değerlendirme Yap"}
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-muted/30 rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-primary">
            Salon Değerlendirmesi
          </DialogTitle>
          <DialogDescription>
            {gymName} salonunu değerlendirin. Deneyiminizi diğer üyelerle paylaşın.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-3">
            <Label className="text-base font-medium">Puanınız</Label>
            <StarRating
              rating={rating}
              onRatingChange={onRatingChange}
              size="lg"
            />
          </div>
          <div className="space-y-3">
            <Label htmlFor="comment" className="text-base font-medium">
              Yorumunuz (Opsiyonel)
            </Label>
            <Textarea
              id="comment"
              placeholder="Deneyiminizi paylaşın..."
              value={comment}
              onChange={(e) => onCommentChange(e.target.value)}
              className="h-32 resize-none"
            />
          </div>
        </div>
        <DialogFooter>
          <Button 
            onClick={onSubmit}
            className="duration-300 shadow-lg"
          >
            {hasExistingReview ? "Güncelle" : "Gönder"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});
