'use server';

import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { env } from '../env';



// Route tanımları
export const AUTH_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth',
  '/verify-phone',
  '/auth/verify-email',
  '/auth/forgot-password',
] as const;
export const PROTECTED_ROUTES = [
  '/dashboard',
  '/onboarding',
] as const;

// Rol görsel adları (TR)
const ROLE_LABELS_TR: Record<string, string> = {
  member: '<PERSON><PERSON>',
  trainer: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  company_manager: 'Şirket Yöneticisi',
  gym_manager: 'Salon Yöneticisi',
};

interface AuthMiddlewareResult {
  response: NextResponse;
  user?: any;
  userRoles?: string[];
}

/**
 * Gelişmiş authentication middleware
 * - Session yönetimi
 * - Route protection
 * - Role-based access control
 * - Performance optimizasyonu
 */
export async function authMiddleware(
  request: NextRequest
): Promise<AuthMiddlewareResult> {
  let supabaseResponse = NextResponse.next({ request });

  // Pathname'i header'a ekle (server components için)
  supabaseResponse.headers.set('x-pathname', request.nextUrl.pathname);

  // Supabase client oluştur
  const supabase = createServerClient(
    env.SUPABASE_URL || '',
    env.SUPABASE_ANON_KEY || '',
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({ request });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // User session'ını al
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  const { pathname } = request.nextUrl;

  // Route type'ını belirle
  const isAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));
  const isProtectedRoute = PROTECTED_ROUTES.some(route =>
    pathname.startsWith(route)
  );

  // Auth error durumunda korumalı sayfalardan çıkar
  if (error && isProtectedRoute) {
    const url = request.nextUrl.clone();
    url.pathname = '/error';
    url.searchParams.set('type', 'session_expired');
    url.searchParams.set('message', error.message || 'Oturum hatası');
    return { response: NextResponse.redirect(url) };
  }

  // Korumalı sayfalara erişim kontrolü
  if (isProtectedRoute && !user) {
    const url = request.nextUrl.clone();
    url.pathname = '/auth/login';
    url.searchParams.set('redirect', pathname);
    url.searchParams.set(
      'message',
      'Bu sayfaya erişmek için giriş yapmanız gerekiyor'
    );
    return { response: NextResponse.redirect(url) };
  }

  // Rol kontrolü için kullanıcı rollerini yükle
  const userRoles: string[] = [];
  if (user) {
    const userId = user.id;

    // Önce user_roles VIEW'ından flag'leri çek
    try {
      const { data: flags, error: roleErr } = await (supabase as any)
        .from('user_roles')
        .select('is_member,is_trainer,is_gym_manager,is_company_manager')
        .eq('profile_id', userId)
        .maybeSingle();

      if (!roleErr && flags) {
        if (flags.is_member) userRoles.push('member');
        if (flags.is_trainer) userRoles.push('trainer');
        if (flags.is_gym_manager) userRoles.push('gym_manager');
        if (flags.is_company_manager) userRoles.push('company_manager');
      }
    } catch {}

    // Eğer boş kaldıysa eski yöntem ile fallback
    if (userRoles.length === 0) {
      const [memberCheck, trainerCheck, companyCheck, gymManagerCheck] = await Promise.all([
        supabase
          .from('member_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('trainer_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('companies')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
        supabase
          .from('gyms')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
      ]);

      if (memberCheck.data) userRoles.push('member');
      if (trainerCheck.data) userRoles.push('trainer');
      if (companyCheck.data) userRoles.push('company_manager');
      if (gymManagerCheck.data) userRoles.push('gym_manager');
    }
  }

  // Rol-bazlı dashboard erişim kuralları
  const roleRouteMap: Array<{ prefix: string; role: string }> = [
    { prefix: '/dashboard/member', role: 'member' },
    { prefix: '/dashboard/trainer', role: 'trainer' },
    { prefix: '/dashboard/company', role: 'company_manager' },
    { prefix: '/dashboard/gym_manager', role: 'gym_manager' },
  ];

  const matched = roleRouteMap.find(r => pathname.startsWith(r.prefix));
  if (matched && user) {
    const hasRole = userRoles.includes(matched.role);
    if (!hasRole) {
      const url = request.nextUrl.clone();
      url.pathname = '/onboarding';
      url.searchParams.set('message', `Bu sayfaya erişmek için ${ROLE_LABELS_TR[matched.role] ?? matched.role} rolü gerekiyor`);
      url.searchParams.set('toast', 'error');
      return { response: NextResponse.redirect(url) };
    }
  }

  // Giriş yapmış kullanıcıları auth sayfalarından yönlendir
  if (isAuthRoute && user) {
    const url = request.nextUrl.clone();

    // Login sayfasından panel'e yönlendir
    if (pathname.startsWith('/auth/login')) {
      // Redirect parametresi varsa oraya yönlendir
      const redirectTo = request.nextUrl.searchParams.get('redirect');
      url.pathname =
        redirectTo && redirectTo.startsWith('/dashboard')
          ? redirectTo
          : '/dashboard';
      url.search = ''; // Query parametrelerini temizle
      return { response: NextResponse.redirect(url) };
    }

    // Register sayfasından onboarding'e yönlendir
    if (pathname.startsWith('/auth/register') || pathname.startsWith('/auth')) {
      url.pathname = '/onboarding';
      return { response: NextResponse.redirect(url) };
    }
  }


  return {
    response: supabaseResponse,
    user,
  };
}


