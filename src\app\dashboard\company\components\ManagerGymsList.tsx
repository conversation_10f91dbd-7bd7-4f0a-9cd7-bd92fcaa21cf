'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Gyms } from '@/types/database/tables';
import { resolveCityName } from '@/lib/utils/display-helpers';
import {
  Building2,
  ChevronRight,
  ExternalLink,
  Heart,
  MapPin,
  Plus,
  Search,
  Star,
  X,
  Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';

// Constants
const FAVORITE_GYMS_KEY = 'manager-favorite-gyms';
const SEARCH_THRESHOLD = 5; // 5'ten fazla salon varsa arama göster

// Types
interface GymLocation {
  summary: string;
  fullAddress: string;
}

interface ManagerGymsListProps {
  initialGyms: Gyms[];
}

// Components
function EmptyGymsState() {
  return (
    <div className="flex min-h-[60vh] flex-col">
      {/* Action Button */}
      <div className="mb-8 flex justify-end">
        <Button asChild size="lg" className="shadow-lg">
          <Link href="/dashboard/company/gym-setup">
            <Plus className="mr-2 h-5 w-5" />
            Yeni Salon Ekle
          </Link>
        </Button>
      </div>

      {/* Empty State */}
      <div className="flex flex-1 items-center justify-center">
        <div className="max-w-md text-center">
          <div className="relative mb-8">
            <div className="bg-muted mx-auto flex h-24 w-24 items-center justify-center rounded-full">
              <Building2 className="text-muted-foreground h-12 w-12" />
            </div>
            <div className="bg-primary absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full">
              <Plus className="text-primary-foreground h-4 w-4" />
            </div>
          </div>
          <h2 className="text-primary mb-3 text-2xl font-semibold">
            İlk Salonunuzu Oluşturun
          </h2>
          <p className="text-muted-foreground mb-8 leading-relaxed">
            Spor salonu işletmenizi dijitalleştirin. Üye yönetimi, paket
            satışları, randevu sistemi ve detaylı raporlama ile işinizi büyütün.
            Hemen başlayın!
          </p>

          <div className="bg-muted/30 mb-6 rounded-lg border p-4">
            <h3 className="text-foreground mb-2 text-sm font-medium">
              Salon oluşturduktan sonra yapabilecekleriniz:
            </h3>
            <ul className="text-muted-foreground space-y-1 text-sm">
              <li>• Üye kayıt ve yönetimi</li>
              <li>• Paket ve abonelik satışları</li>
              <li>• Randevu ve sınıf programları</li>
              <li>• Detaylı gelir raporları</li>
            </ul>
          </div>
          <Button asChild size="lg" className="shadow-lg">
            <Link href="/dashboard/company/gym-setup">
              <Zap className="mr-2 h-5 w-5" />
              Salon Kurulumuna Başla
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

function SearchAndFilterSection({
  searchQuery,
  onSearchChange,
  onSearchClear,
  showOnlyFavorites,
  onToggleFavorites,
  favoriteCount,
}: {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onSearchClear: () => void;
  showOnlyFavorites: boolean;
  onToggleFavorites: () => void;
  favoriteCount: number;
}) {
  return (
    <div className="bg-muted/30 border-muted rounded-xl border p-6">
      <div className="flex flex-col gap-4 sm:flex-row">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="text-muted-foreground absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 transform" />
          <Input
            placeholder="Salon ara (ad, konum, slug)..."
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
            className="bg-background border-muted-foreground/20 focus:border-primary h-12 pl-12 pr-12 text-base"
          />
          {searchQuery && (
            <button
              onClick={onSearchClear}
              className="text-muted-foreground hover:text-foreground absolute right-4 top-1/2 h-5 w-5 -translate-y-1/2 transform transition-colors"
              aria-label="Aramayı temizle"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Favorites Filter */}
        <Button
          variant={showOnlyFavorites ? 'default' : 'outline'}
          onClick={onToggleFavorites}
          className="flex h-12 min-w-fit items-center gap-3 px-6"
        >
          <Heart
            className={`h-5 w-5 transition-all duration-300 ${showOnlyFavorites ? 'fill-current' : ''}`}
          />
          <span className="font-medium">Favoriler</span>
          {favoriteCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 px-2 py-1 text-xs font-semibold"
            >
              {favoriteCount}
            </Badge>
          )}
        </Button>
      </div>
    </div>
  );
}

export function ManagerGymsList({ initialGyms }: ManagerGymsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [favoriteGyms, setFavoriteGyms] = useState<string[]>([]);
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Computed values
  const shouldShowSearch = initialGyms.length > SEARCH_THRESHOLD;
  const hasActiveFilters = searchQuery.trim() || showOnlyFavorites;

  // Utility functions
  const loadFavoritesFromStorage = useCallback(() => {
    if (typeof window === 'undefined') return [];

    try {
      const savedFavorites = localStorage.getItem(FAVORITE_GYMS_KEY);
      if (!savedFavorites) return [];

      const parsed = JSON.parse(savedFavorites);
      const validGymIds = initialGyms.map(gym => gym.id);
      return parsed.filter((gymId: string) => validGymIds.includes(gymId));
    } catch (error) {
      console.error('Error loading favorite gyms:', error);
      return [];
    }
  }, [initialGyms]);

  const saveFavoritesToStorage = useCallback((favorites: string[]) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(FAVORITE_GYMS_KEY, JSON.stringify(favorites));
    } catch (error) {
      console.error('Error saving favorite gyms:', error);
    }
  }, []);

  const isFavorite = useCallback(
    (gymId: string) => favoriteGyms.includes(gymId),
    [favoriteGyms]
  );

  const toggleFavorite = useCallback(
    (gymId: string, event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();

      const validGymIds = initialGyms.map(gym => gym.id);
      if (!validGymIds.includes(gymId)) return;

      setFavoriteGyms(prev =>
        prev.includes(gymId)
          ? prev.filter(id => id !== gymId)
          : [...prev, gymId]
      );
    },
    [initialGyms]
  );

  const clearSearch = useCallback(() => setSearchQuery(''), []);
  const toggleFavoritesFilter = useCallback(
    () => setShowOnlyFavorites(prev => !prev),
    []
  );

  // Effects
  useEffect(() => {
    const favorites = loadFavoritesFromStorage();
    setFavoriteGyms(favorites);
    setIsLoaded(true);
  }, [loadFavoritesFromStorage]);

  useEffect(() => {
    if (isLoaded) {
      saveFavoritesToStorage(favoriteGyms);
    }
  }, [favoriteGyms, isLoaded, saveFavoritesToStorage]);

  // Filtering and sorting logic
  const searchInGym = useCallback((gym: Gyms, query: string): boolean => {
    const cityName = resolveCityName(gym.city) || gym.city;
    const searchFields = [
      gym.name,
      cityName,
      gym.district,
      gym.address,
      gym.slug,
    ].filter(Boolean);

    return searchFields.some(field =>
      field?.toLowerCase().includes(query.toLowerCase())
    );
  }, []);

  const sortGyms = useCallback(
    (gyms: Gyms[]): Gyms[] => {
      return gyms.sort((a, b) => {
        const aIsFav = isFavorite(a.id);
        const bIsFav = isFavorite(b.id);

        if (aIsFav !== bIsFav) return aIsFav ? -1 : 1;
        return a.name.localeCompare(b.name);
      });
    },
    [isFavorite]
  );

  const filteredGyms = useMemo(() => {
    let gyms = [...initialGyms];

    // Apply filters only if we have enough gyms to warrant search
    if (shouldShowSearch) {
      if (showOnlyFavorites) {
        gyms = gyms.filter(gym => isFavorite(gym.id));
      }

      if (searchQuery.trim()) {
        gyms = gyms.filter(gym => searchInGym(gym, searchQuery.trim()));
      }
    }

    return sortGyms(gyms);
  }, [
    initialGyms,
    searchQuery,
    showOnlyFavorites,
    shouldShowSearch,
    isFavorite,
    searchInGym,
    sortGyms,
  ]);

  // Helper functions for location formatting
  const formatGymLocation = useCallback((gym: Gyms): GymLocation => {
    const cityName = resolveCityName(gym.city) || gym.city;
    const locationParts = [gym.district, cityName].filter(Boolean);
    return {
      summary: locationParts.join(', ') || 'Konum belirtilmemiş',
      fullAddress: gym.address || 'Adres belirtilmemiş',
    };
  }, []);
  // Render empty state if no gyms
  if (initialGyms.length === 0) {
    return <EmptyGymsState />;
  }

  return (
    <div className="space-y-6">
      {/* Action Button */}
      <div className="flex justify-end">
        <Button asChild size="lg" className="shadow-lg">
          <Link href="/dashboard/company/gym-setup">
            <Plus className="mr-2 h-5 w-5" />
            Yeni Salon Ekle
          </Link>
        </Button>
      </div>

      {/* Search and Filter Section - Sadece 5'ten fazla salon varsa göster */}
      {shouldShowSearch && (
        <SearchAndFilterSection
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onSearchClear={clearSearch}
          showOnlyFavorites={showOnlyFavorites}
          onToggleFavorites={toggleFavoritesFilter}
          favoriteCount={favoriteGyms.length}
        />
      )}

      {/* Search Results Info */}
      {shouldShowSearch && hasActiveFilters && (
        <div className="bg-muted/20 border-muted flex items-center justify-between rounded-lg border px-4 py-3">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-medium">
              {filteredGyms.length} salon
            </Badge>
            {searchQuery && (
              <span className="text-muted-foreground text-sm">
                &apos;{searchQuery}&apos; için
              </span>
            )}
            {showOnlyFavorites && (
              <Badge variant="secondary" className="gap-1">
                <Heart className="h-3 w-3 fill-current" />
                Favoriler
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* No Results Message */}
      {shouldShowSearch && filteredGyms.length === 0 && hasActiveFilters && (
        <div className="bg-muted/10 border-muted-foreground/30 flex flex-col items-center justify-center rounded-xl border border-dashed py-20 text-center">
          {showOnlyFavorites && !searchQuery ? (
            <>
              <div className="relative mb-6">
                <div className="bg-muted flex h-20 w-20 items-center justify-center rounded-full">
                  <Heart className="text-muted-foreground h-10 w-10" />
                </div>
                <div className="bg-accent absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full">
                  <Star className="text-accent-foreground h-3 w-3" />
                </div>
              </div>
              <h3 className="text-primary mb-3 text-2xl font-semibold">
                Henüz favori salon yok
              </h3>
              <p className="text-muted-foreground mb-8 max-w-md leading-relaxed">
                Salon kartlarındaki kalp ikonuna tıklayarak sık kullandığınız
                salonları favorilerinize ekleyebilirsiniz.
              </p>
              <Button
                variant="outline"
                onClick={toggleFavoritesFilter}
                size="lg"
              >
                Tüm Salonları Göster
              </Button>
            </>
          ) : (
            <>
              <div className="bg-muted mb-6 flex h-20 w-20 items-center justify-center rounded-full">
                <Search className="text-muted-foreground h-10 w-10" />
              </div>
              <h3 className="text-primary mb-3 text-2xl font-semibold">
                Salon bulunamadı
              </h3>
              <p className="text-muted-foreground mb-8 max-w-md leading-relaxed">
                {searchQuery &&
                  `"${searchQuery}" araması için salon bulunamadı.`}
                {showOnlyFavorites &&
                  searchQuery &&
                  ' Favori salonlarınızda bu arama sonucu bulunamadı.'}
                {!searchQuery &&
                  showOnlyFavorites &&
                  'Favori salonlarınızda bu kriterlere uygun salon bulunamadı.'}
                {searchQuery &&
                  !showOnlyFavorites &&
                  ' Farklı anahtar kelimeler deneyin.'}
              </p>
              <div className="flex gap-3">
                {searchQuery && (
                  <Button variant="outline" onClick={clearSearch} size="lg">
                    <X className="mr-2 h-4 w-4" />
                    Aramayı Temizle
                  </Button>
                )}
                {showOnlyFavorites && (
                  <Button
                    variant="outline"
                    onClick={toggleFavoritesFilter}
                    size="lg"
                  >
                    Tüm Salonları Göster
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
      )}

      {/* Gym Cards */}
      {filteredGyms.length > 0 && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          {filteredGyms.map(gym => {
            const location = formatGymLocation(gym);

            return (
              <div
                key={gym.id}
                className={`bg-card group relative  overflow-hidden rounded-2xl border transition-all duration-300 hover:-translate-y-1 hover:shadow-lg ${
                  gym.status === 'active'
                    ? 'border-border/50 hover:border-primary/40 hover:shadow-primary/10'
                    : 'border-destructive/30 hover:border-destructive/60'
                } ${
                  isFavorite(gym.id)
                    ? 'ring-primary/30 border-primary/40 bg-primary/[0.03] shadow-sm ring-2'
                    : 'hover:bg-card/80'
                }`}
              >
                {/* Status Badge */}
                {gym.status === 'passive' && (
                  <div className="absolute left-4 top-4 z-20">
                    <Badge
                      variant="destructive"
                      className="text-xs font-medium shadow-sm"
                    >
                      Pasif
                    </Badge>
                  </div>
                )}

                {/* Favorite Button */}
                <button
                  onClick={e => toggleFavorite(gym.id, e)}
                  className={`absolute right-4 top-4 z-20 flex h-9 w-9 items-center justify-center rounded-full transition-all duration-300 ${
                    isFavorite(gym.id)
                      ? 'bg-primary text-primary-foreground scale-110 shadow-md'
                      : 'bg-background/90 text-muted-foreground hover:bg-primary hover:text-primary-foreground backdrop-blur-sm hover:scale-105'
                  } border-border/50 border shadow-sm hover:shadow-md`}
                  title={
                    isFavorite(gym.id)
                      ? 'Favorilerden çıkar'
                      : 'Favorilere ekle'
                  }
                  aria-label={
                    isFavorite(gym.id)
                      ? 'Favorilerden çıkar'
                      : 'Favorilere ekle'
                  }
                >
                  <Heart
                    className={`h-4 w-4 transition-all duration-300 ${
                      isFavorite(gym.id) ? 'fill-current' : ''
                    }`}
                  />
                </button>

                {/* Main Content */}
                <div className=" block p-6 pb-4">
                  <Link
                    className="space-y-5"
                    href={`/dashboard/gym/${gym.id}`}
                  >
                    {/* Gym Name with External Link */}
                    <div className="flex items-center justify-between gap-3">
                      <h3 className="text-foreground group-hover:text-primary line-clamp-1 text-xl font-bold transition-colors">
                        {gym.name}
                      </h3>
                    </div>

                    {/* Location */}
                    <div className="flex items-start gap-3">
                      <div className="bg-primary/10 text-primary mt-0.5 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg">
                        <MapPin className="h-4 w-4" />
                      </div>
                      <div className="min-w-0 flex-1 space-y-1">
                        <p className="text-foreground line-clamp-1 text-sm font-semibold">
                          {location.summary}
                        </p>
                        {gym.address && (
                          <p className="text-muted-foreground line-clamp-2 text-xs leading-relaxed">
                            {location.fullAddress}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Slug */}
                    {gym.slug && (
                      <div className="bg-muted/50 flex items-center gap-2 rounded-lg px-3 py-2">
                        <div className="bg-primary h-1.5 w-1.5 rounded-full"></div>
                        <span className="text-muted-foreground font-mono text-xs font-medium">
                          /{gym.slug}
                        </span>
                      </div>
                    )}
                  </Link>

                  {/* Action Button */}
                  <Link
                    href={gym.slug ? `/gym/${gym.slug}` : `/gym/${gym.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={e => e.stopPropagation()}
                    className="text-muted-foreground hover:text-primary right-18 absolute top-6 flex-shrink-0 transition-colors duration-200"
                    title="Salon detayını görüntüle"
                  >
                    <ExternalLink className="h-5 w-5" />
                  </Link>
                  <div className="mt-6">
                    <div className="from-muted/60 to-muted/40 group-hover:from-primary group-hover:to-primary/90 group-hover:text-primary-foreground flex w-full items-center justify-between rounded-xl bg-gradient-to-r px-4 py-3.5 transition-all duration-300 group-hover:shadow-sm">
                      <span className="font-semibold">Salonu Yönet</span>
                      <ChevronRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
