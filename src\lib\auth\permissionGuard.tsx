import { PermissionGuardProps } from './trainer-permissions';

export function PermissionGuard({
  permissions,
  category,
  action,
  errorMessage = 'Bu işlem için yetkiniz bulunmuyor',
  children,
  fallback,
}: PermissionGuardProps) {
  const categoryPermissions = permissions[category] as any;
  const hasPermission =
    categoryPermissions && categoryPermissions[action] === true;

  if (!hasPermission) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Erişim Engellendi
          </h3>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {errorMessage}
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
