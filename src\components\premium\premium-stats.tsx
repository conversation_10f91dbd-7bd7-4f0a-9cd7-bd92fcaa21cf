import { AnimatedSection, AnimatedCounter } from '@/components/ui/animated-section'
import { TrendingUp, Users, Clock, Star } from 'lucide-react'

export function PremiumStats() {
  const stats = [
    {
      label: 'Aktif Salon',
      value: 1200,
      suffix: '+',
      icon: Users,
      color: 'from-blue-500/20 to-blue-600/10',
      iconColor: 'text-blue-600'
    },
    {
      label: '<PERSON><PERSON><PERSON><PERSON>',
      value: 85000,
      suffix: '+',
      icon: TrendingUp,
      color: 'from-green-500/20 to-green-600/10',
      iconColor: 'text-green-600'
    },
    {
      label: 'Uptime',
      value: 99,
      suffix: '.95%',
      icon: Clock,
      color: 'from-orange-500/20 to-orange-600/10',
      iconColor: 'text-orange-600'
    },
    {
      label: 'Memnuniyet',
      value: 4,
      suffix: '.9/5',
      icon: Star,
      color: 'from-purple-500/20 to-purple-600/10',
      iconColor: 'text-purple-600'
    },
  ]

  return (
    <section className="py-16 md:py-20">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto grid max-w-6xl grid-cols-2 gap-6 md:grid-cols-4">
            {stats.map((s) => {
              const IconComponent = s.icon
              return (
                <div
                  key={s.label}
                  className={`group relative overflow-hidden rounded-2xl border border-border/50 bg-gradient-to-br ${s.color} p-8 text-center shadow-lg backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-xl hover:-translate-y-2`}
                >
                  {/* Background glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                  <div className="relative">
                    <div className="mb-4 flex justify-center">
                      <div className={`rounded-xl bg-background/80 p-3 shadow-sm ${s.iconColor}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                    </div>

                    <div className="text-4xl font-extrabold md:text-5xl">
                      <AnimatedCounter target={s.value} className="animate-counter" />
                      <span className="text-primary">{s.suffix}</span>
                    </div>

                    <div className="mt-3 text-sm font-semibold text-muted-foreground">{s.label}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </AnimatedSection>
      </div>
    </section>
  )
}
