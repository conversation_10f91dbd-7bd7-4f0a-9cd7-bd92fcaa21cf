import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { StructuredData } from '@/components/seo/structured-data';
import { Metadata } from 'next';
import { PricingModern } from '@/components/public/home/<USER>';
import { PremiumHero } from '@/components/premium/premium-hero'
import { PremiumStats } from '@/components/premium/premium-stats'
import { RoleFeatures } from '@/components/public/home/<USER>'
import { AboutFeatures } from '@/components/public/about/about-features'
import { FeaturesGrid } from '@/app/(public)/features/features-grid'

// Aşağıdaki görsel ağırlıklı bölümleri client tarafında gerektiğinde yükle
const PremiumShowcase = dynamic(() => import('@/components/premium/premium-showcase').then(m => m.PremiumShowcase));
const PremiumTestimonials = dynamic(() => import('@/components/premium/premium-testimonials').then(m => m.PremiumTestimonials));
const PremiumCTA = dynamic(() => import('@/components/premium/premium-cta').then(m => m.PremiumCTA));
const PremiumLogosMarquee = dynamic(() => import('@/components/premium/premium-logos-marquee').then(m => m.PremiumLogosMarquee));
const PremiumROICalculator = dynamic(() => import('@/components/premium/premium-roi-calculator').then(m => m.PremiumROICalculator));

// PPR pilotu: statik kabuk + dinamik adaları stream etmek için
export const experimental_ppr = true;
export const metadata: Metadata = {
  title: 'Sportiva | Salon Yönetiminin En İyi Hali',
  description:
    'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
  keywords: [
    'spor salonu yönetimi',
    'fitness yönetim sistemi',
    'salon üye takibi',
    'spor salonu yazılımı',
    'dijital salon yönetimi',
    'fitness teknolojisi',
    'istanbul spor salonu',
    'ankara spor salonu',
    'izmir spor salonu',
    'rize spor salonu',
    'salon bul',
    'fitness merkezi',
  ],
  openGraph: {
    title: 'Sportiva | Salon Yönetiminin En İyi Hali',
    description:
      'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
    images: ['/placeholder.jpg'],
  },
  alternates: {
    canonical: '/',
  },
};

export default function Home() {

  return (
    <>
      <StructuredData
        type="Product"
        data={{
          name: 'Sportiva',
          description:
            'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
          brand: 'Sportiva',
          offers: { priceCurrency: 'TRY', availability: 'InStock' },
        }}
      />
      
      <main className="flex-1">
        <PremiumHero />
        <PremiumStats />
        <Suspense fallback={<LogosFallback/>}>
          <PremiumLogosMarquee />
        </Suspense>
        <AboutFeatures />
        <FeaturesGrid />
        <RoleFeatures />
        <Suspense fallback={<RoiFallback/>}>
          <PremiumROICalculator />
        </Suspense>
        <Suspense fallback={<SectionFallback/>}>
          <PremiumShowcase />
        </Suspense>
        <Suspense fallback={<SectionFallback/>}>
          <PremiumTestimonials />
        </Suspense>
        <Suspense fallback={<PricingFallbackHome />}>
          <section className="py-20">
            <div className="container mx-auto px-4">
              <PricingModern showHeader={true} compact={false} />
            </div>
          </section>
        </Suspense>
        <Suspense fallback={<SectionFallback/>}>
          <PremiumCTA />
        </Suspense>
      </main>
    </>
  );
}

function PricingFallbackHome() {
  return (
    <section className="py-20" aria-label="Fiyatlandırma yükleniyor">
      <div className="container mx-auto px-4">
        <div className="mx-auto w-full max-w-5xl animate-pulse space-y-6" role="status" aria-live="polite">
          <div className="h-8 w-48 rounded bg-muted" />
          <div className="h-32 w-full rounded bg-muted" />
          <div className="h-6 w-72 rounded bg-muted" />
          <div className="h-48 w-full rounded bg-muted" />
        </div>
      </div>
    </section>
  );
}

function SectionFallback(){
  return (
    <section className="py-16" aria-label="Bölüm yükleniyor">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="h-40 rounded bg-muted" />
          <div className="h-40 rounded bg-muted" />
        </div>
      </div>
    </section>
  );
}

function LogosFallback(){
  return (
    <section className="py-16" aria-label="Markalar yükleniyor">
      <div className="container mx-auto px-4">
        <div className="h-14 w-full rounded bg-muted" />
      </div>
    </section>
  )
}

function RoiFallback(){
  return (
    <section className="py-16" aria-label="ROI hesaplayıcı yükleniyor">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="h-72 rounded bg-muted" />
          <div className="h-72 rounded bg-muted" />
        </div>
      </div>
    </section>
  )
}
