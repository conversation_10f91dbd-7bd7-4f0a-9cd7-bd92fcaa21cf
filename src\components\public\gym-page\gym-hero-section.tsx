'use client';

import { Map<PERSON>in, Phone, Star, Shield, Zap, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Clock, Heart } from "lucide-react";
import Image from "next/image";
import { memo, useEffect, useState } from "react";

import { Gyms } from '@/types/database/tables';
import { GymJoinButton } from './gym-join-button';
import { TrainerJoinButton } from './trainer-join-button';
import { GymSocialLinks } from './gym-social-links';
import { User } from "@supabase/supabase-js";
import { ReviewStats } from "@/lib/actions/gymPage/gym-utils";

interface GymHeroSectionProps {
  gymId: string;
  gym: Gyms & { company?: any };
  membershipStatus: 'none' | 'pending' | 'active';
  trainerStatus: 'none' | 'pending' | 'active';
  reviewStats: ReviewStats | null;
  user: User | null;
  isGymOwner: boolean;
  hasTrainerRole?: boolean;
  hasMemberRole?: boolean;
}

export const GymHeroSection = memo(function GymHeroSection({
  gymId,
  gym,
  membershipStatus,
  trainerStatus,
  reviewStats,
  user,
  isGymOwner,
  hasTrainerRole = false,
  hasMemberRole = false
}: GymHeroSectionProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div className="relative overflow-hidden">
      {/* Modern Hero Section */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-background via-muted/20 to-background">
        
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full blur-3xl" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-emerald-500/20 to-cyan-500/10 rounded-full blur-3xl" />
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px] opacity-30" />
        </div>

        {/* Background Image Overlay */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background" />
        </div>

        {/* Main Content */}
        <div className="relative z-10 container mx-auto px-6 py-20">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">

            {/* Left Column - Main Content */}
            <div className={`space-y-12 text-center lg:text-left transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
              
              {/* Premium Badge */}
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium backdrop-blur-sm">
                <Sparkles className="h-4 w-4" aria-hidden="true" />
                <span>Premium Fitness Deneyimi</span>
              </div>

              {/* Main Title */}
              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl font-bold text-foreground leading-tight">
                  <span className="block">{gym.name}</span>
                  <span className="block bg-gradient-to-r from-primary to-emerald-500 bg-clip-text text-transparent">
                    ile Tanışın
                  </span>
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto lg:mx-0">
                  {gym.description || "Modern ekipmanlar, uzman eğitmenler ve motivasyonel bir toplulukla fitness yolculuğunuzu başlatın"}
                </p>
              </div>

              {/* Location & Contact Info */}
              <div className="flex flex-wrap items-center gap-6 text-muted-foreground" aria-label="Salon konum ve telefon bilgileri">
                {gym.address && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4 text-primary" aria-hidden="true" />
                    <span>{gym.address}</span>
                  </div>
                )}
                {gym.gym_phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="w-4 h-4 text-primary" aria-hidden="true" />
                    <a
                      href={`tel:${gym.gym_phone}`}
                      className="hover:text-primary transition-colors"
                    >
                      {gym.gym_phone}
                    </a>
                  </div>
                )}
              </div>

              {/* Social Links */}
              {gym.company?.social_links && (
                <div className="pt-4">
                  <GymSocialLinks
                    socialLinks={gym.company.social_links}
                    companyName={gym.company.name || gym.name}
                  />
                </div>
              )}

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-8" role="group" aria-label="Birincil eylemler">
                {/* Salon sahibi değilse katılım butonlarını göster */}
                {!isGymOwner && (
                  <>
                    <GymJoinButton
                      membershipStatus={membershipStatus}
                      gymId={gymId}
                      user={user}
                      hasMemberRole={hasMemberRole}
                    />
                    {/* Antrenör butonunu sadece antrenör rolüne sahip kullanıcılar için göster */}
                    {user && hasTrainerRole && (
                      <TrainerJoinButton
                        trainerStatus={trainerStatus}
                        gymId={gymId}
                        gymName={gym.name}
                      />
                    )}
                  </>
                )}

                {/* Salon sahibi ise özel mesaj göster */}
                {isGymOwner && (
                  <div className="px-6 py-3 bg-primary/10 backdrop-blur-sm border border-primary/20 rounded-xl text-primary font-medium">
                    Bu salonun sahibisiniz
                  </div>
                )}

                <button
                  type="button"
                  className="px-6 py-3 bg-secondary/10 backdrop-blur-sm border border-secondary/20 rounded-xl text-secondary-foreground font-medium hover:bg-secondary/20 transition-all duration-300 hover:scale-105"
                  onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  Daha Fazla Bilgi
                </button>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-3 gap-4 pt-8" aria-label="Salon istatistikleri">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">
                    {reviewStats?.averageRating?.toFixed(1) || '5.0'}
                  </div>
                  <div className="flex justify-center gap-1 mb-1">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i}
                        className={`w-3 h-3 ${i < Math.floor(reviewStats?.averageRating || 5) ? 'text-yellow-400 fill-current' : 'text-muted'}`}
                        aria-hidden="true"
                      />
                    ))}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {reviewStats?.totalReviews || 0} Değerlendirme
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">
                    50+
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Eğitmen
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">
                    500+
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Aktif Üye
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - 3D Visual Elements */}
            <div className={`relative flex justify-center lg:justify-end transition-all duration-1000 delay-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>

              {/* 3D Card Container */}
              <div className="relative perspective-1000">

                {/* Main 3D Card */}
                <div className="relative w-80 h-96 md:w-96 md:h-[28rem] transform-gpu hover:rotate-y-12 transition-transform duration-700 preserve-3d">

                  {/* Card Front */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-2xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden">

                    {/* Gym Logo Section */}
                    <div className="p-8 text-center">
                      <div className="relative mx-auto w-32 h-32 mb-6">
                        {/* Glow Effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/30 to-cyan-500/30 rounded-full blur-xl scale-150 animate-pulse" />

                        {(gym as any).company?.logo_url ? (
                          <div className="relative w-full h-full rounded-full overflow-hidden border-4 border-emerald-500/40 shadow-2xl">
                            <Image
                              src={(gym as any).company.logo_url}
                              alt={`${gym.name} - Logo`}
                              className="w-full h-full object-cover"
                              width={128}
                              height={128}
                            />
                          </div>
                        ) : (
                          <div className="relative w-full h-full rounded-full bg-gradient-to-br from-emerald-500/30 to-cyan-500/30 border-4 border-emerald-500/40 shadow-2xl flex items-center justify-center">
                            <span className="text-4xl font-black text-white">
                              {gym.name.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Card Content */}
                      <h3 className="text-2xl font-bold text-white mb-4">{gym.name}</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-center gap-2 text-emerald-400">
                          <Award className="h-5 w-5" />
                          <span className="text-sm font-medium">Premium Üyelik</span>
                        </div>
                        <div className="flex items-center justify-center gap-2 text-cyan-400">
                          <Clock className="h-5 w-5" />
                          <span className="text-sm font-medium">24/7 Açık</span>
                        </div>
                        <div className="flex items-center justify-center gap-2 text-pink-400">
                          <Heart className="h-5 w-5" />
                          <span className="text-sm font-medium">500+ Mutlu Üye</span>
                        </div>
                        {gym.max_capacity && (
                          <div className="flex items-center justify-center gap-2 text-blue-400">
                            <Users className="h-5 w-5" />
                            <span className="text-sm font-medium">{gym.max_capacity} Kişi Kapasiteli</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Decorative Elements */}
                    <div className="absolute top-4 right-4 w-3 h-3 bg-emerald-400 rounded-full animate-pulse" />
                    <div className="absolute bottom-4 left-4 w-2 h-2 bg-cyan-400 rounded-full animate-pulse delay-500" />
                  </div>
                </div>

                {/* Floating Action Icons */}
                <div className="absolute -top-6 -right-6 w-16 h-16 bg-gradient-to-r from-emerald-500/20 to-cyan-500/20 rounded-2xl border border-emerald-500/30 flex items-center justify-center backdrop-blur-xl animate-float shadow-lg">
                  <Dumbbell className="h-8 w-8 text-emerald-400" />
                </div>

                <div className="absolute -bottom-6 -left-6 w-14 h-14 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl border border-purple-500/30 flex items-center justify-center backdrop-blur-xl animate-float delay-1000 shadow-lg">
                  <Shield className="h-7 w-7 text-purple-400" />
                </div>

                <div className="absolute top-1/2 -left-8 w-12 h-12 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl border border-yellow-500/30 flex items-center justify-center backdrop-blur-xl animate-float delay-500 shadow-lg">
                  <Zap className="h-6 w-6 text-yellow-400" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Scroll Indicator */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2" aria-hidden="true">
          <div className="flex flex-col items-center gap-2 text-white/60">
            <span className="text-xs font-medium tracking-wider uppercase">Keşfet</span>
            <div className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center relative overflow-hidden">
              <div className="w-1 h-4 bg-gradient-to-b from-emerald-400 to-cyan-400 rounded-full mt-2 animate-bounce" />
            </div>
          </div>
        </div>
      </section>

      {/* Modern Features Section */}
      <section className="py-24 bg-gradient-to-b from-muted/10 to-background">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

            {/* Feature 1 - Location */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-emerald-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-primary/20 transition-all duration-300 hover:transform hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-emerald-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <MapPin className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-bold text-lg text-foreground mb-2">Merkezi Konum</h3>
                <p className="text-sm text-muted-foreground">{gym.address}</p>
              </div>
            </div>

            {/* Feature 2 - Equipment */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-purple-500/30 transition-all duration-300 hover:transform hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-6 w-6 text-purple-500" />
                </div>
                <h3 className="font-bold text-lg text-foreground mb-2">Son Teknoloji</h3>
                <p className="text-sm text-muted-foreground">Premium ekipmanlar ve modern cihazlar</p>
              </div>
            </div>

            {/* Feature 3 - Staff */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-blue-500/30 transition-all duration-300 hover:transform hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
                <h3 className="font-bold text-lg text-foreground mb-2">Uzman Kadro</h3>
                <p className="text-sm text-muted-foreground">Sertifikalı eğitmenler ve destek ekibi</p>
              </div>
            </div>

            {/* Feature 4 - Security */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 hover:border-yellow-500/30 transition-all duration-300 hover:transform hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-6 w-6 text-yellow-500" />
                </div>
                <h3 className="font-bold text-lg text-foreground mb-2">Güvenli Ortam</h3>
                <p className="text-sm text-muted-foreground">7/24 güvenlik ve temiz ortam</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
});
