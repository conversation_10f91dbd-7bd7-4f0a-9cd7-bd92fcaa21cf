/**
 * Constants for core action functionality
 * Following Clean Code principles - centralized constants to avoid magic numbers and strings
 */

// ============================================================================
// ERROR CODES
// ============================================================================

export const DATABASE_ERROR_CODES = {
  NO_ROWS_RETURNED: 'PGRST116',
  UNIQUE_VIOLATION: '23505',
  FOREIGN_KEY_VIOLATION: '23503',
  INSUFFICIENT_PRIVILEGE: '42501',
  RLS_POLICY_VIOLATION: 'PGRST301',
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const CORE_ERROR_MESSAGES = {
  // Auth errors
  LOGIN_REQUIRED: 'Bu işlem için giriş yapmanız gerekiyor.',
  ADMIN_CLIENT_MISSING: 'Admin client bulunamadı.',

  // Access control errors
  GYM_ACCESS_DENIED: 'Bu salona erişim yetkiniz yok veya salon bulunamadı.',
  INSUFFICIENT_PERMISSIONS: 'Bu işlem için yetkiniz yok.',
  RLS_POLICY_DENIED: 'Bu işlem için gerekli yetkiye sahip değilsiniz.',

  // RLS specific errors
  GYM_RLS_ACCESS_DENIED: 'Bu salona erişim yetkiniz yok. Lütfen salon yöneticisi ile iletişime geçin.',
  SUBSCRIPTION_INACTIVE: 'Aktif aboneliğiniz bulunmuyor. İşlem yapabilmek için aboneliğinizi yenileyin.',
  TRAINER_PERMISSION_DENIED: 'Bu işlem için antrenör yetkileriniz yeterli değil.',
  MEMBER_ACCESS_DENIED: 'Bu üyelik bilgilerine erişim yetkiniz yok.',
  PACKAGE_ACCESS_DENIED: 'Bu paket bilgilerine erişim yetkiniz yok.',
  APPOINTMENT_ACCESS_DENIED: 'Bu randevu bilgilerine erişim yetkiniz yok.',
  STAFF_ACCESS_DENIED: 'Bu personel bilgilerine erişim yetkiniz yok.',

  // Database errors
  RECORD_NOT_FOUND: 'Kayıt bulunamadı.',
  RECORD_ALREADY_EXISTS: 'Bu kayıt zaten mevcut.',
  FOREIGN_KEY_CONSTRAINT: 'İlişkili kayıtlar nedeniyle işlem gerçekleştirilemedi.',
  DATABASE_OPERATION_FAILED: 'Veritabanı işlemi sırasında bir hata oluştu.',

  // Validation errors
  INVALID_DATA_FORMAT: 'Geçersiz veri formatı. Lütfen tüm alanları kontrol edin.',
  VALIDATION_FAILED: 'Girilen bilgiler geçersiz.',

  // Generic errors
  UNKNOWN_ERROR: 'Bilinmeyen bir hata oluştu',
  UNEXPECTED_ERROR: 'Beklenmeyen bir hata oluştu.',
  INTERNAL_ERROR: 'Beklenmeyen bir hata oluştu.',
} as const;

// ============================================================================
// ACTION CONFIGURATION
// ============================================================================

export const ACTION_DEFAULTS = {
  REQUIRE_AUTH: true,
  ENABLE_ADMIN_CLIENT: false,
} as const;

// ============================================================================
// REVALIDATION TYPES
// ============================================================================

export const REVALIDATION_TYPES = {
  PAGE: 'page' as const,
  LAYOUT: 'layout' as const,
} as const;
