import { AnimatedSection } from '@/components/ui/animated-section'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CalendarRange, LineChart, Users2, Building2, CheckCircle2 } from 'lucide-react'

export function PremiumShowcase() {
  const features = [
    {
      icon: CalendarRange,
      title: 'Akıllı Randevu Yönetimi',
      desc: 'Çakışma önleme, bekleme listesi, eğitmen/oda ataması ve otomatik hatırlatmalar.',
      bullets: ['<PERSON>ak<PERSON>şma tespiti', 'Oda ve ekipman ataması', 'SMS/WhatsApp hatırlatma'],
    },
    {
      icon: Users2,
      title: 'Üyelik ve CRM',
      desc: 'Üyelik döngüsü, paketler ve iletişim akışlarını tek panelden yönetin.',
      bullets: ['Paket yönetimi', 'Otomatik yenileme', 'Segment ve etiketleme'],
    },
    {
      icon: Line<PERSON>hart,
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      desc: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, rande<PERSON> ve antrenör performans raporları ile karar verin.',
      bullets: ['K<PERSON> panoları', '<PERSON>iyot karşılaştırma', 'Dışa aktarma (CSV)'],
    },
    {
      icon: Building2,
      title: 'Çoklu Şube Operasyonu',
      desc: 'Merkezden tüm şubeleri yönetin, yetkilendirme ve raporlamayı konsolide edin.',
      bullets: ['Merkezi roller', 'Şube bazlı raporlar', 'Depo görünürlüğü'],
    },
  ]

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto mb-10 max-w-2xl text-center">
            <h2 className="text-balance text-3xl font-bold md:text-4xl">Yetkinlik Vitrini</h2>
            <p className="mt-3 text-muted-foreground">
              Operasyonun çekirdeğini güçlendiren modüller ve akıllı iş akışları.
            </p>
          </div>
        </AnimatedSection>

        <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-2">
          {features.map(({ icon: Icon, title, desc, bullets }) => (
            <AnimatedSection key={title} animation="fade-up">
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-primary/10 p-2">
                      <Icon className="size-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{title}</CardTitle>
                  </div>
                  <CardDescription>{desc}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="mt-2 space-y-2 text-sm text-muted-foreground">
                    {bullets.map((b) => (
                      <li key={b} className="flex items-center gap-2">
                        <CheckCircle2 className="size-4 text-primary" />
                        <span>{b}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </AnimatedSection>
          ))}
        </div>
      </div>
    </section>
  )
}
