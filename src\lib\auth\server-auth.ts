'use server';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { AuthUser } from '@supabase/supabase-js';
import { createAction} from '@/lib/actions/core/core';
import { cache } from 'react';

/**
 * Server component'lerde kullanılmak üzere optimize edilmiş auth utilities
 */

export interface AuthResult {
  user: AuthUser | null;
  roles: string[];
}

/**
 * Middleware tarafından set edilen user bilgisini al
 * Performance için middleware'den gelen bilgiyi kullan
 */
export async function getAuthenticatedUser(): Promise<AuthUser | null> {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
}

/**
 * Kullanıcının rollerini getir (createAction pattern ile optimize edilmiş)
 */
export const getUserRoles = cache(async (): Promise<string[]> => {
  const result = await createAction(
    async (_, supabase, userId) => {
      // 1) user_roles VIEW'undan flag'leri çek ve rollerin tamamını türet
      try {
        const { data: flags, error: roleErr } = await (supabase as any)
          .from('user_roles')
          .select('is_member,is_trainer,is_gym_manager,is_company_manager')
          .eq('profile_id', userId)
          .maybeSingle();

        if (!roleErr && flags) {
          const roles: string[] = [];
          if (flags.is_member) roles.push('member');
          if (flags.is_trainer) roles.push('trainer');
          if (flags.is_gym_manager) roles.push('gym_manager');
          if (flags.is_company_manager) roles.push('company_manager');
          if (roles.length > 0) return roles;
        }
      } catch {}

      // 2) Eski yönteme geri düş (çoklu tablo kontrolü)
      const roles: string[] = [];

      const [memberCheck, trainerCheck, companyCheck, gymManagerCheck] = await Promise.all([
        supabase
          .from('member_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('trainer_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('companies')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
        supabase
          .from('gyms')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
      ]);

      if (memberCheck.data) roles.push('member');
      if (trainerCheck.data) roles.push('trainer');
      if (companyCheck.data) roles.push('company_manager');
      if (gymManagerCheck.data) roles.push('gym_manager');
      return roles;
    },
  );

  return result.success ? result.data! : [];
});

/**
 * Role kontrolü ile birlikte auth kontrolü
 */
export async function requireRole(requiredRole: string): Promise<AuthResult> {
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect('/auth/login');
  }

  const roles = await getUserRoles();

  if (!roles.includes(requiredRole)) {
    console.warn('rol bulunamadı:', requiredRole);
    const qs = new URLSearchParams({
      message: `Bu sayfaya erişmek için ${requiredRole} rolü gerekiyor`,
      toast: 'error',
    }).toString();
    redirect(`/onboarding?${qs}`);
  }

  return { user, roles };
}



// Antrenör yetki tipleri
export interface TrainerPermissions {
  members: {
    read: boolean;
    create: boolean;
    update: boolean;
  };
  packages: {
    create: boolean;
    update: boolean;
  };
  appointments: {
    read: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
}

/**
 * Check manager active subscription
 */
export async function checkManagerActiveSubscription(): Promise<boolean> {
  const result = await createAction<boolean>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('user_roles')
        .select('company_status')
        .eq('profile_id', userId)
        .eq('company_status', 'active')
        .maybeSingle();

      return !error && !!data;
    }
  );

  return result.success ? (result.data ?? false) : false;
}

