'use server';

import { createAction } from '@/lib/actions/core/core';

import { z } from 'zod';
import {
  TrainerPermissionLevel,
  getPermissionLevelDefinition,
  getPermissionLevel,
} from '@/lib/constants/trainer-permission-levels';
import { ApiResponse } from '@/types/global/api';

// Antrenör yetki tipleri
export interface TrainerPermissions {
  members: {
    read: boolean;
    create: boolean;
    update: boolean;
  };
  packages: {
    create: boolean;
    update: boolean;
  };
  appointments: {
    read: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
}

export interface GymTrainerWithPermissions {
  id: string;
  gym_id: string;
  trainer_profile_id: string;
  status: string;
  permissions: TrainerPermissions;
  permission_level?: TrainerPermissionLevel;
  trainer: {
    full_name: string;
    email: string;
    specialization?: string;
    certification_level?: string;
    experience_years?: number;
  };
}

// Validation schemas
const permissionsSchema = z.object({
  members: z.object({
    read: z.boolean(),
    create: z.boolean(),
    update: z.boolean(),
  }),
  packages: z.object({
    create: z.boolean(),
    update: z.boolean(),
  }),
  appointments: z.object({
    read: z.boolean(),
    create: z.boolean(),
    delete: z.boolean(),
    update: z.boolean(),
  }),
});

const updateTrainerPermissionsSchema = z.object({
  gymId: z.uuid(),
  trainerId: z.uuid(),
  permissions: permissionsSchema,
});

const updateTrainerPermissionLevelSchema = z.object({
  gymId: z.uuid(),
  trainerId: z.uuid(),
  level: z.enum(['basic', 'standard', 'advanced']),
});

/**
 * Salon antrenörlerini ve yetkilerini getirir (Manager için)
 */
export async function getGymTrainersWithPermissions(
  gymId: string
): Promise<ApiResponse<GymTrainerWithPermissions[]>> {
  return createAction<GymTrainerWithPermissions[]>(
    async (_, supabase, _userId) => {
      const { data: gymTrainers, error: trainersError } = await supabase
        .from('gym_trainers')
        .select(
          `
            id,
            gym_id,
            trainer_profile_id,
            status,
            created_at
          `
        )
        .eq('gym_id', gymId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

    if (trainersError) {
      throw new Error(`Antrenörler getirilemedi: ${trainersError.message}`);
    }

    if (!gymTrainers || gymTrainers.length === 0) {
      return [];
    }

    // Her trainer için permissions'ları al - artık gym_trainers tablosundan
    const trainersWithPermissions = [];
    for (const trainer of gymTrainers) {
      // gym_trainers tablosundan permissions'ı al
      const { data: trainerWithPermissions, error: permError } = await supabase
        .from('gym_trainers')
        .select('permissions')
        .eq('id', trainer.id)
        .single();

      if (permError) {
        throw new Error(`Yetki bilgileri getirilemedi: ${permError.message}`);
      }

      const trainerPermissions = trainerWithPermissions.permissions as TrainerPermissions;
      const trainerWithPerms = {
        ...trainer,
        permissions: trainerPermissions,
        permission_level: getPermissionLevel(trainerPermissions),
      };
      trainersWithPermissions.push(trainerWithPerms);
    }

    // Antrenör profile bilgilerini getir
    const trainerIds = trainersWithPermissions.map(t => t.trainer_profile_id);
    const { data: trainerProfiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name, email')
      .in('id', trainerIds);

    if (profilesError) {
      throw new Error(
        `Antrenör profilleri getirilemedi: ${profilesError.message}`
      );
    }

    // Antrenör detaylarını getir
    const { data: trainerDetails, error: detailsError } = await supabase
      .from('trainer_details')
      .select(
        'profile_id, specialization, certification_level, experience_years'
      )
      .in('profile_id', trainerIds)

    if (detailsError) {
      throw new Error(
        `Antrenör detayları getirilemedi: ${detailsError.message}`
      );
    }

    // Verileri birleştir
    const profilesMap = new Map(trainerProfiles?.map(p => [p.id, p]) || []);
    const detailsMap = new Map(
      trainerDetails?.map(d => [d.profile_id, d]) || []
    );

    const result: GymTrainerWithPermissions[] = trainersWithPermissions.map(
      gt => {
        const profile = profilesMap.get(gt.trainer_profile_id);
        const details = detailsMap.get(gt.trainer_profile_id);

        return {
          id: gt.id,
          gym_id: gt.gym_id,
          trainer_profile_id: gt.trainer_profile_id,
          status: gt.status,
          created_at: gt.created_at,
          permissions: gt.permissions,
          permission_level: gt.permission_level,
          trainer: {
            full_name: profile?.full_name || 'Bilinmeyen',
            email: profile?.email || '',
            specialization: details?.specialization,
            certification_level: details?.certification_level,
            experience_years: details?.experience_years,
          },
        };
      }
    );

    return result;
  },
  {
    requireAuth: true,
  });
}

/**
 * Antrenör yetkilerini günceller (Manager için)
 */
export async function updateTrainerPermissions(
  data: z.infer<typeof updateTrainerPermissionsSchema>
): Promise<ApiResponse<void>> {
  // Validation
  const validatedData = updateTrainerPermissionsSchema.parse(data);
  const { gymId, trainerId, permissions } = validatedData;

  return createAction<void>(
    async (_, supabase, _userId) => {
      // Antrenör yetkilerini güncelle - gym_trainers tablosunda
      const { error: updateError } = await supabase
        .from('gym_trainers')
        .update({ permissions })
        .eq('id', trainerId);

      if (updateError) {
        throw new Error(`Yetki güncellemesi başarısız: ${updateError.message}`);
      }
    },
    {
      requireAuth: true,
      revalidatePaths: [
        `/dashboard/gym/${gymId}/trainer-permissions`,
      ],
    }
  );
}

/**
 * Antrenör yetki seviyesini günceller (Manager için) - Basitleştirilmiş versiyon
 */
export async function updateTrainerPermissionLevel(
  data: z.infer<typeof updateTrainerPermissionLevelSchema>
): Promise<ApiResponse<void>> {
  // Validation
  const validatedData = updateTrainerPermissionLevelSchema.parse(data);
  const { gymId, trainerId, level } = validatedData;

  return createAction<void>(
    async (_, supabase, _userId) => {
      // Seviye tanımından permissions'ı al
      const levelDefinition = getPermissionLevelDefinition(level);

      // Antrenör yetkilerini güncelle - gym_trainers tablosunda
      const { error: updateError } = await supabase
        .from('gym_trainers')
        .update({ permissions: levelDefinition.permissions })
        .eq('id', trainerId);

      if (updateError) {
        throw new Error(
          `Yetki seviyesi güncellemesi başarısız: ${updateError.message}`
        );
      }
    },
    {
      requireAuth: true,
      revalidatePaths: [
        `/dashboard/gym/${gymId}/trainer-permissions`,
      ],
    }
  );
}

/**
 * Antrenörün belirli bir gym'deki yetkilerini getirir
 */
export async function getTrainerPermissions(
  gymId: string,
  trainerId?: string
): Promise<ApiResponse<TrainerPermissions | null>> {
  return createAction<TrainerPermissions | null>(
    async (_, supabase, userId) => {
      const targetTrainerId = trainerId || userId;

      if (!targetTrainerId) {
        throw new Error('Antrenör ID gerekli');
      }

      // Gym trainer'ı ve permissions'ını al
      const { data: gymTrainer, error: trainerError } = await supabase
        .from('gym_trainers')
        .select('id, permissions')
        .eq('gym_id', gymId)
        .eq('trainer_profile_id', targetTrainerId)
        .eq('status', 'active')
        .single();

      if (trainerError) {
        if (trainerError.code === 'PGRST116') {
          return null; // Antrenör bu gym'de çalışmıyor
        }
        throw new Error(
          `Antrenör bilgileri getirilemedi: ${trainerError.message}`
        );
      }

      return gymTrainer.permissions as TrainerPermissions;
    },
    
  );
}
