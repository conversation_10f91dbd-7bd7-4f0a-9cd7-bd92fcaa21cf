'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Calendar, Users, Mail, ExternalLink, Phone, CalendarPlus } from 'lucide-react';
import { MemberStatusSelect } from './member-status-select';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';
import { MembershipWithMember } from '@/types/business/membership';

interface GymMembersTableProps {
  members: MembershipWithMember[];
  gymId: string;
}

// İsim kısaltması
function getInitials(fullName: string) {
  return fullName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function GymMembersTable({ members, gymId }: GymMembersTableProps) {
  if (members.length === 0) {
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle>Salon Üyeleri</CardTitle>
            <CardDescription>Bu salonda henüz üye bulunmuyor</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="py-8 text-center">
              <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <p className="text-muted-foreground text-lg font-medium">
                Henüz üye yok
              </p>
              <p className="text-muted-foreground text-sm">
                Üst kısımdaki &apos;Üye Ekle&apos; butonunu kullanarak ilk
                üyenizi ekleyebilirsiniz
              </p>
            </div>
          </CardContent>
        </Card>
      </>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Salon Üyeleri ({members.length})</CardTitle>
          <CardDescription>
            Salon üyelerinin listesi ve detayları
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Detay</TableHead>
                  <TableHead>Üye Bilgileri</TableHead>
                  <TableHead className="w-[140px]">Katılım Tarihi</TableHead>
                  <TableHead className="w-[140px]">Durum</TableHead>
                  <TableHead className="w-[120px]">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map(membership => {
                  const member = membership.member;
                  const memberName = member?.full_name || 'İsimsiz Üye';
                  const isGuest = member?.is_guest_account;
                  return (
                    <TableRow key={membership.id} className="hover:bg-muted/50">
                      {/* Detay Sütunu */}
                      <TableCell>
                        <Link
                          href={`/dashboard/gym/${gymId}/members/${membership.id}`}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span className="sr-only">
                              Üye detayını görüntüle
                            </span>
                          </Button>
                        </Link>
                      </TableCell>

                      {/* Üye Bilgileri Sütunu */}
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src="" alt={memberName} />
                            <AvatarFallback className="text-sm font-medium">
                              {getInitials(memberName)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <p className="truncate text-sm font-medium">
                                {memberName}
                              </p>
                              {isGuest && (
                                <Badge variant="secondary" className="text-xs">
                                  Misafir
                                </Badge>
                              )}
                            </div>
                            {isGuest
                              ? member?.email && (
                                  <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
                                    <Mail className="h-3 w-3" />
                                    <span className="truncate">
                                      {member.email}
                                    </span>
                                  </div>
                                )
                              : member?.phone_number && (
                                  <div className="text-muted-foreground mt-1 flex items-center gap-1 text-xs">
                                    <Phone className="h-3 w-3" />
                                    <span className="truncate">
                                      {member.phone_number}
                                    </span>
                                  </div>
                                )}
                          </div>
                        </div>
                      </TableCell>

                      {/* Katılım Tarihi Sütunu */}
                      <TableCell>
                        <div className="text-muted-foreground flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          <span>
                            {membership.created_at
                              ? formatDate(membership.created_at)
                              : '-'}
                          </span>
                        </div>
                      </TableCell>
                      {/* Durum Sütunu */}
                      <TableCell>
                        <MemberStatusSelect
                          membershipId={membership.id}
                          gymId={gymId}
                          initialStatus={membership.status}
                        />
                      </TableCell>

                      {/* İşlemler Sütunu */}
                      <TableCell>
                        {member && (
                          <Link
                            href={`/dashboard/gym/${gymId}/appointments/new?memberId=${member.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 gap-1"
                            >
                              <CalendarPlus className="h-3 w-3" />
                              <span className="hidden sm:inline">Randevu</span>
                            </Button>
                          </Link>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
