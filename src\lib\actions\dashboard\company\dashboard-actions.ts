'use server';

/**
 * Dashboard Actions - Manager Gym Functions
 *
 * Following Clean Code principles - this file contains only manager gym functions
 * Other dashboard functions are in specialized files:
 * - gym-analytics.ts: Analytics calculations
 * - gym-stats.ts: Basic statistics
 * - manager-overview.ts: Manager overview data
 * - financial-summary.ts: Financial calculations
 * - dashboard-types.ts: Type definitions
 */

import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import { Gyms, Profiles } from '@/types/database/tables';

/**
 * Şirketin sahip olduğu tüm salonları getirir
 * Following Clean Code principles - simple data fetching
 */
export async function getManagerGyms(): Promise<ApiResponse<(Gyms & { manager_profile: Profiles | null })[]>> {
  return await createAction<(Gyms & { manager_profile: Profiles | null })[]>(async (_, supabase, userId) => {
    // Önce şirketi bul
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('id')
      .eq('manager_profile_id', userId)
      .single();

    if (companyError || !companyData) {
      throw new Error('Şirket bulunamadı veya yetki yok.');
    }

    // Şirkete ait salonları ve yönetici profillerini getir
    const { data, error } = await supabase
      .from('gyms')
      .select(`
        *,
        manager_profile:profiles(*)
      `)
      .eq('company_id', companyData.id);

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  });
}

export async function getManagerGymNames(): Promise<
  ApiResponse<{ id: string; name: string }[]>
> {
  return await createAction<{ id: string; name: string }[]>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('gyms')
        .select(`
          id,
          name,
          company:companies!inner(
            id,
            manager_profile_id
          )
        `)
        .eq('company.manager_profile_id', userId);

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    }
  );
}
