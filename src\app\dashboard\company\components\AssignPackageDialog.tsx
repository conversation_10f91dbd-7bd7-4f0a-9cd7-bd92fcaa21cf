'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getGymPackages } from '@/lib/actions/all-actions';
import { assignPackageToMember } from '@/lib/actions/dashboard/company/gym-member-actions';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Calendar, Clock, DollarSign, Loader2, Package, Users, Timer } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Form validation schema
const assignPackageSchema = z.object({
  packageId: z.string().min(1, 'Lütfen bir paket seçin'),
  startDate: z.string().min(1, 'Başlangıç tarihi gereklidir'),
});

type AssignPackageFormValues = z.infer<typeof assignPackageSchema>;
export interface AssignPackageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  membershipId: string;
  memberName: string;
  gymId: string;
  onPackageAssigned: () => void;
}
// Package Assignment Types
export interface GymPackageForAssignment {
  id: string;
  name: string;
  package_type: string;
  duration_days: number | null;
  price: number;
  description: string | null;
  session_count: number | null;
  session_duration_minutes: number | null;
  max_participants: number | null;
}
export function AssignPackageDialog({
  isOpen,
  onClose,
  membershipId,
  memberName,
  gymId,
  onPackageAssigned,
}: AssignPackageDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [packages, setPackages] = useState<GymPackageForAssignment[]>([]);
  const [isLoadingPackages, setIsLoadingPackages] = useState(false);
  const [selectedPackage, setSelectedPackage] =
    useState<GymPackageForAssignment | null>(null);

  const form = useForm<AssignPackageFormValues>({
    resolver: zodResolver(assignPackageSchema),
    defaultValues: {
      packageId: '',
      startDate: format(new Date(), 'yyyy-MM-dd'),
    },
  });

  const watchedPackageId = form.watch('packageId');

  const loadPackages = useCallback(async () => {
    setIsLoadingPackages(true);
    try {
      const response = await getGymPackages(gymId);
      if (response.success) {
        // Transform the data to ensure package_type is never null
        const transformedPackages: GymPackageForAssignment[] = (
          response.data || []
        )
          .filter(pkg => pkg.package_type !== null) // Filter out packages with null package_type
          .map(pkg => ({
            id: pkg.id,
            name: pkg.name,
            package_type: pkg.package_type!, // We know it's not null due to filter above
            duration_days: pkg.duration_days,
            price: pkg.price,
            description: pkg.description,
            session_count: pkg.session_count,
            session_duration_minutes: pkg.session_duration_minutes,
            max_participants: pkg.max_participants,
          }));
        setPackages(transformedPackages);
      } else {
        toast.error(response.error || 'Paketler yüklenirken hata oluştu');
      }
    } catch (error) {
      console.error('Load packages error:', error);
      toast.error('Paketler yüklenirken hata oluştu');
    } finally {
      setIsLoadingPackages(false);
    }
  }, [gymId]);

  // Load packages when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadPackages();
    }
  }, [isOpen, loadPackages]);

  // Update selected package when packageId changes
  useEffect(() => {
    const pkg = packages.find(p => p.id === watchedPackageId);
    setSelectedPackage(pkg || null);
  }, [watchedPackageId, packages]);

  const handleSubmit = async (values: AssignPackageFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await assignPackageToMember(
        membershipId,
        values.packageId,
        values.startDate
      );

      if (response.success) {
        toast.success(
          `✅ Paket başarıyla atandı!\n📦 ${selectedPackage?.name}\n👤 ${memberName}`,
          {
            duration: 5000,
            style: { whiteSpace: 'pre-line' },
          }
        );
        onPackageAssigned();
        handleClose();
      } else {
        toast.error(response.error || 'Paket ataması yapılamadı');
      }
    } catch (error) {
      console.error('Assign package error:', error);
      toast.error('Paket ataması yapılırken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setSelectedPackage(null);
    onClose();
  };

  const getPackageTypeBadge = (type: string) => {
    const typeMap = {
      monthly: { label: 'Aylık', color: 'bg-blue-100 text-blue-800' },
      yearly: { label: 'Yıllık', color: 'bg-purple-100 text-purple-800' },
      daily: { label: 'Günlük', color: 'bg-orange-100 text-orange-800' },
      trial: { label: 'Deneme', color: 'bg-gray-100 text-gray-800' },
    };

    const typeInfo = typeMap[type as keyof typeof typeMap] || {
      label: type,
      color: 'bg-gray-100 text-gray-800',
    };

    return <Badge className={typeInfo.color}>{typeInfo.label}</Badge>;
  };

  const calculateEndDate = (startDate: string, durationDays: number | null) => {
    if (!durationDays) return null;
    const start = new Date(startDate);
    const end = new Date(start);
    end.setDate(end.getDate() + durationDays);
    return end;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Paket Ata - {memberName}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Package Selection */}
            <FormField
              control={form.control}
              name="packageId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paket Seçimi</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Atanacak paketi seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="w-[400px]">
                      {isLoadingPackages ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="ml-2">Paketler yükleniyor...</span>
                        </div>
                      ) : packages.length > 0 ? (
                        packages.map(pkg => (
                          <SelectItem key={pkg.id} value={pkg.id}>
                            <div className="flex items-center justify-between w-full">
                              <div className="flex flex-col">
                                <span className="font-medium">{pkg.name}</span>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                  <span>₺{pkg.price}</span>
                                  {(pkg.package_type === 'appointment_standard' ||
                                    pkg.package_type === 'appointment_vip') && pkg.session_count && (
                                    <>
                                      <span>•</span>
                                      <span>{pkg.session_count} seans</span>
                                      <span>•</span>
                                      <span>{pkg.session_duration_minutes}dk</span>
                                    </>
                                  )}
                                </div>
                              </div>
                              {getPackageTypeBadge(pkg.package_type)}
                            </div>
                          </SelectItem>
                        ))
                      ) : (
                        <div className="text-muted-foreground py-4 text-center text-sm">
                          Aktif paket bulunamadı
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Bu üyeye atanacak paketi seçin
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Start Date */}
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Başlangıç Tarihi</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    Paketin geçerlilik başlangıç tarihi
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Package Preview */}
            {selectedPackage && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Package className="h-5 w-5" />
                    {selectedPackage.name}
                  </CardTitle>
                  <CardDescription>
                    {selectedPackage.description ||
                      'Paket açıklaması bulunmuyor'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-medium">
                        ₺{selectedPackage.price}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getPackageTypeBadge(selectedPackage.package_type)}
                    </div>
                  </div>

                  {/* Randevu bazlı paketler için ek bilgiler */}
                  {(selectedPackage.package_type === 'appointment_standard' ||
                    selectedPackage.package_type === 'appointment_vip') && (
                    <div className="grid grid-cols-2 gap-4">
                      {selectedPackage.session_count && (
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-blue-600" />
                          <span>{selectedPackage.session_count} seans</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Timer className="h-4 w-4 text-orange-600" />
                        <span>{selectedPackage.session_duration_minutes} dk/seans</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-purple-600" />
                        <span>Max {selectedPackage.max_participants} kişi</span>
                      </div>
                    </div>
                  )}

                  {selectedPackage.duration_days && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <span>{selectedPackage.duration_days} gün geçerli</span>
                    </div>
                  )}

                  {form.watch('startDate') && selectedPackage.duration_days && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-purple-600" />
                      <span>
                        Bitiş:{' '}
                        {format(
                          calculateEndDate(
                            form.watch('startDate'),
                            selectedPackage.duration_days
                          )!,
                          'dd MMMM yyyy',
                          { locale: tr }
                        )}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isSubmitting || !selectedPackage}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Paket atanıyor...
                  </>
                ) : (
                  <>
                    <Package className="mr-2 h-4 w-4" />
                    Paketi Ata
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
