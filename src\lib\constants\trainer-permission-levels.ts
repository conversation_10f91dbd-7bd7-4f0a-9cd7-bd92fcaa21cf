import { TrainerPermissions } from '../auth/server-auth';

// Antrenör yetki seviyeleri
export type TrainerPermissionLevel = 'basic' | 'standard' | 'advanced';

// Seviye tanımları
export interface PermissionLevelDefinition {
  id: TrainerPermissionLevel;
  name: string;
  description: string;
  permissions: TrainerPermissions;
  features: string[];
  color: string;
  icon: string;
}

// Önceden tanımlanmış yetki seviyeleri
export const PERMISSION_LEVELS: Record<
  TrainerPermissionLevel,
  PermissionLevelDefinition
> = {
  basic: {
    id: 'basic',
    name: 'Temel Antrenör',
    description:
      '<PERSON><PERSON> görüntüleme ve ekleme, temel randevu görüntüleme yetkileri',
    permissions: {
      members: {
        read: true,
        create: true,
        update: false,
      },
      packages: {
        create: false,
        update: false,
      },
      appointments: {
        read: true,
        create: false,
        delete: false,
        update: false,
      },
    },
    features: [
      'Üye listesini gör<PERSON>e',
      '<PERSON><PERSON> ekleme',
      '<PERSON><PERSON>u listesini gör<PERSON>ntüleme',
    ],
    color: 'bg-blue-500',
    icon: 'User',
  },
  standard: {
    id: 'standard',
    name: '<PERSON><PERSON>nör',
    description: 'Üye yönetimi ve randevu oluşturma yetkileri',
    permissions: {
      members: {
        read: true,
        create: true,
        update: true,
      },
      packages: {
        create: true,
        update: false,
      },
      appointments: {
        read: true,
        create: true,
        delete: false,
        update: true,
      },
    },
    features: [
      'Tüm temel yetkiler',
      'Üye bilgilerini güncelleme',
      'Paket oluşturma',
      'Randevu oluşturma',
      'Randevu güncelleme',
    ],
    color: 'bg-green-500',
    icon: 'UserCheck',
  },
  advanced: {
    id: 'advanced',
    name: 'İleri Düzey Antrenör',
    description: 'Tam yetki - tüm işlemleri gerçekleştirebilir',
    permissions: {
      members: {
        read: true,
        create: true,
        update: true,
      },
      packages: {
        create: true,
        update: true,
      },
      appointments: {
        read: true,
        create: true,
        delete: true,
        update: true,
      },
    },
    features: [
      'Tüm standart yetkiler',
      'Paket düzenleme',
      'Randevu silme',
      'Tam yetki',
    ],
    color: 'bg-purple-500',
    icon: 'Crown',
  },
};

// Yardımcı fonksiyonlar
export function getPermissionLevel(
  permissions: TrainerPermissions
): TrainerPermissionLevel {
  // Permissions'ı seviyelerle karşılaştır
  for (const [level, definition] of Object.entries(PERMISSION_LEVELS)) {
    if (
      JSON.stringify(definition.permissions) === JSON.stringify(permissions)
    ) {
      return level as TrainerPermissionLevel;
    }
  }

  // Eşleşme yoksa, en yakın seviyeyi bul
  if (permissions.appointments.delete) {
    return 'advanced';
  } else if (permissions.members.update && permissions.appointments.create) {
    return 'standard';
  } else {
    return 'basic';
  }
}

export function getPermissionLevelDefinition(
  level: TrainerPermissionLevel
): PermissionLevelDefinition {
  return PERMISSION_LEVELS[level];
}

export function getAllPermissionLevels(): PermissionLevelDefinition[] {
  return Object.values(PERMISSION_LEVELS);
}

// Seviye karşılaştırma
export function isHigherLevel(
  level1: TrainerPermissionLevel,
  level2: TrainerPermissionLevel
): boolean {
  const levels = ['basic', 'standard', 'advanced'];
  return levels.indexOf(level1) > levels.indexOf(level2);
}

// Varsayılan seviye
export const DEFAULT_PERMISSION_LEVEL: TrainerPermissionLevel = 'basic';
