'use client';

import { ModernAuthInput } from './modern-auth-input';
import { SubmitButton } from './submit-button';
import { EnhancedAuthError } from './enhanced-auth-error';
import { AUTH_UI_TEXT } from '@/lib/constants/auth';

interface ForgotPasswordFormProps {
  onSubmit: (formData: FormData) => Promise<void>;
  error?: string;
  emailError?: string;
}

export function ForgotPasswordForm({
  onSubmit,
  error,
  emailError,
}: ForgotPasswordFormProps) {
  const handleSubmit = async (formData: FormData) => {
    await onSubmit(formData);
  };

  return (
    <div className="space-y-4">
      {/* Enhanced Error Message */}
      {error && (
        <EnhancedAuthError
          error={error}
          context={{
            action: 'reset_password',
            method: 'email',
            stage: 'initial',
          }}
          onRetry={() => window.location.reload()} // Bu durumda sayfa yenileme gere<PERSON>
        />
      )}

      <form action={handleSubmit} className="space-y-6">
        <ModernAuthInput
          name="email"
          type="email"
          label="E-posta Adresi"
          placeholder="E-posta adresinizi girin"
          required
          error={emailError}
        />

        <SubmitButton>{AUTH_UI_TEXT.SEND_RESET_LINK}</SubmitButton>
      </form>
    </div>
  );
}
