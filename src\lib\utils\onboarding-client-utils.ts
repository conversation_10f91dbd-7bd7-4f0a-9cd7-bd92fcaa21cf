/**
 * Şirket onboarding adımları
 */
export enum ManagerOnboardingStep {
  ROLE_SELECTION = 'role-selection',
  COMPANY_SETUP = 'company-setup',
  PAYMENT = 'payment',
  GYM_SETUP = 'gym-setup',
  COMPLETED = 'completed'
}

/**
 * Onboarding adımı için URL path'i döndürür
 */
export function getStepPath(step: ManagerOnboardingStep): string {
  switch (step) {
    case ManagerOnboardingStep.ROLE_SELECTION:
      return '/onboarding';
    case ManagerOnboardingStep.COMPANY_SETUP:
      return '/onboarding/company-setup';
    case ManagerOnboardingStep.PAYMENT:
      return '/onboarding';
    case ManagerOnboardingStep.GYM_SETUP:
      return '/dashboard/company/gym-setup';
    case ManagerOnboardingStep.COMPLETED:
      return '/dashboard/company';
    default:
      return '/onboarding';
  }
}

/**
 * Adım adı döndürür
 */
export function getStepName(step: ManagerOnboardingStep): string {
  switch (step) {
    case ManagerOnboardingStep.ROLE_SELECTION:
      return 'Rol Seçimi';
    case ManagerOnboardingStep.COMPANY_SETUP:
      return 'Şirket Kurulumu';
    case ManagerOnboardingStep.PAYMENT:
      return 'Ödeme';
    case ManagerOnboardingStep.GYM_SETUP:
      return 'İlk Şube Kurulumu';
    case ManagerOnboardingStep.COMPLETED:
      return 'Tamamlandı';
    default:
      return 'Bilinmeyen Adım';
  }
}

/**
 * Tüm onboarding adımlarını döndürür
 */
export function getAllSteps(): ManagerOnboardingStep[] {
  return [
    ManagerOnboardingStep.ROLE_SELECTION,
    ManagerOnboardingStep.COMPANY_SETUP,
    ManagerOnboardingStep.PAYMENT,
    ManagerOnboardingStep.GYM_SETUP,
    ManagerOnboardingStep.COMPLETED
  ];
}

/**
 * URL path'inden onboarding adımını belirler
 */
export function getStepFromPath(pathname: string): ManagerOnboardingStep | null {
  if (pathname === '/onboarding') {
    return ManagerOnboardingStep.ROLE_SELECTION;
  }
  if (pathname === '/onboarding/company-setup') {
    return ManagerOnboardingStep.COMPANY_SETUP;
  }
  // payment handled inside onboarding now
  if (pathname.startsWith('/dashboard/company/gym-setup')) {
    return ManagerOnboardingStep.GYM_SETUP;
  }
  return null;
}
