/**
 * Constants for user creation and management
 * Following Clean Code principles - centralized constants to avoid magic numbers
 */

// ============================================================================
// PASSWORD GENERATION CONSTANTS
// ============================================================================

export const PASSWORD_CONFIG = {
  MIN_RANDOM_SUFFIX: 10,
  MAX_RANDOM_SUFFIX: 99,
  MIN_LENGTH: 6,
} as const;

// ============================================================================
// TRAINER CODE GENERATION CONSTANTS
// ============================================================================

export const TRAINER_CODE_CONFIG = {
  PREFIX: 'TR',
  TIMESTAMP_SLICE_LENGTH: 8,
  RANDOM_PADDING_LENGTH: 5,
  RANDOM_MAX: 100000,
} as const;

// ============================================================================
// MEMBER CODE GENERATION CONSTANTS
// ============================================================================

export const MEMBER_CODE_CONFIG = {
  PREFIX: 'MB',
  TIMESTAMP_SLICE_LENGTH: 8,
  RANDOM_PADDING_LENGTH: 5,
  RANDOM_MAX: 100000,
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const ERROR_MESSAGES = {
  // Auth errors
  USER_CREATION_FAILED:
    'Kullanıcı hesabı oluşturulamadı. Lütfen bilgileri kontrol edip tekrar deneyin',
  PROFILE_CREATION_FAILED:
    'Kullanıcı profili oluşturulamadı. Sistem yöneticisi ile iletişime geçin',
  MEMBER_DETAILS_CREATION_FAILED:
    'Üye detayları kaydedilemedi. Lütfen tekrar deneyin',
  TRAINER_DETAILS_CREATION_FAILED:
    'Antrenör bilgileri kaydedilemedi. Lütfen tekrar deneyin',
  GYM_MEMBERSHIP_CREATION_FAILED:
    'Salon üyeliği oluşturulamadı. Lütfen tekrar deneyin',

  // Validation errors
  EMAIL_OR_PHONE_REQUIRED:
    'Misafir üye değilse e-posta veya telefon numarasından en az biri gereklidir',
  ADMIN_CLIENT_MISSING: 'Sistem hatası: Yönetici erişimi bulunamadı',

  // Rollback context messages
  ROLLBACK_USER_CREATION: 'kullanıcı oluşturma işlemi geri alındı',
  ROLLBACK_PROFILE_CREATION: 'profil oluşturma hatası - işlem geri alındı',
  ROLLBACK_MEMBER_DETAILS_CREATION: 'üye detayları hatası - işlem geri alındı',
  ROLLBACK_GYM_MEMBERSHIP_CREATION: 'salon üyeliği hatası - işlem geri alındı',

  // User-friendly rollback messages
  ROLLBACK_NOTIFICATION:
    'İşlem sırasında hata oluştu. Tüm değişiklikler geri alındı',
  ROLLBACK_RETRY_MESSAGE: 'Lütfen bilgileri kontrol edip tekrar deneyin',
} as const;

// ============================================================================
// USER TYPES
// ============================================================================

export const USER_TYPES = {
  GUEST: 'guest',
  REGULAR: 'regular',
  TRAINER: 'trainer',
} as const;

// ============================================================================
// DATABASE FIELD DEFAULTS
// ============================================================================

export const DATABASE_DEFAULTS = {
  TRAINER_STATUS: 'active',
  MEMBERSHIP_STATUS: 'active',
  EMPTY_STRING: '',
  NULL_VALUE: null,
} as const;

// ============================================================================
// VALIDATION RULES
// ============================================================================

export const VALIDATION_RULES = {
  FULL_NAME_MIN_LENGTH: 2,
  FULL_NAME_MAX_LENGTH: 100,
  AGE_MIN: 13,
  AGE_MAX: 120,
} as const;
