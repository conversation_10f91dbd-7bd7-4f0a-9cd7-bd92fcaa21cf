'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import {
  Loader2,
  Plus,
  X,
  Clock,
  ChevronRight,
  User,
  Dumbbell,
  CalendarDays,
  CheckCircle,
  ArrowLeft,
  Users
} from 'lucide-react';
import { GymMembershipPackages, GymPackages } from '@/types/database/tables';
import { TrainerWithProfile, createAppointment, addAppointmentParticipant, checkTrainerAvailability, findExistingGroupAppointment } from '@/lib/actions/dashboard/company/appointment-actions';
import { getMemberPackages } from '@/lib/actions/all-actions';
import { MembershipWithMember } from '@/types/business/membership';
import { AppointmentSuccessScreen } from './appointment-success-screen';

// Wizard Step Types
type WizardStep = 'member' | 'trainer-package' | 'appointments' | 'summary';

interface WizardStepConfig {
  id: WizardStep;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const WIZARD_STEPS: WizardStepConfig[] = [
  {
    id: 'member',
    title: 'Üye Seçimi',
    description: 'Randevu alacak üyeyi seçin',
    icon: User,
  },
  {
    id: 'trainer-package',
    title: 'Antrenör & Paket',
    description: 'Antrenör ve paket seçimi yapın',
    icon: Dumbbell,
  },
  {
    id: 'appointments',
    title: 'Randevu Tarihleri',
    description: 'Tarih ve saat belirleyin',
    icon: CalendarDays,
  },
  {
    id: 'summary',
    title: 'Özet & Onay',
    description: 'Bilgileri kontrol edin ve onaylayın',
    icon: CheckCircle,
  },
];

interface AppointmentFormProps {
  gymId: string;
  members: MembershipWithMember[];
  trainers: TrainerWithProfile[];
  preSelectedMemberId?: string;
}

interface SelectedMemberData {
  member: MembershipWithMember['member'];
  membershipId: string;
  packages: (GymMembershipPackages & { gym_package?: GymPackages })[];
}

interface AppointmentData {
  id: string;
  date: string;
  time: string;
  packageId: string;
  sessionNumber: number;
  hasConflict?: boolean;
  conflictMessage?: string;
  canJoinGroup?: boolean;
  existingAppointmentId?: string;
}

export function AppointmentForm({
  gymId,
  members,
  trainers,
  preSelectedMemberId,
}: AppointmentFormProps) {

  // Wizard State
  const [currentStep, setCurrentStep] = useState<WizardStep>('member');
  const [completedSteps, setCompletedSteps] = useState<Set<WizardStep>>(new Set());

  // Form Data State
  const [selectedMemberData, setSelectedMemberData] =
    useState<SelectedMemberData | null>(null);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [selectedTrainer, setSelectedTrainer] = useState<string>('');
  const [selectedPackage, setSelectedPackage] = useState<string>('');
  const [appointments, setAppointments] = useState<AppointmentData[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [createdAppointmentCount, setCreatedAppointmentCount] = useState(0);

  // Wizard Navigation Functions
  const getCurrentStepIndex = () => WIZARD_STEPS.findIndex(step => step.id === currentStep);

  const canGoNext = () => {
    switch (currentStep) {
      case 'member':
        return selectedMemberData !== null;
      case 'trainer-package':
        return selectedTrainer && selectedPackage;
      case 'appointments':
        return appointments.length > 0 && appointments.every(apt => apt.date && apt.time);
      case 'summary':
        return true;
      default:
        return false;
    }
  };

  const canGoPrevious = () => getCurrentStepIndex() > 0;

  const goToNextStep = () => {
    if (!canGoNext()) return;

    const currentIndex = getCurrentStepIndex();
    if (currentIndex < WIZARD_STEPS.length - 1) {
      const newCompletedSteps = new Set(completedSteps);
      newCompletedSteps.add(currentStep);
      setCompletedSteps(newCompletedSteps);
      setCurrentStep(WIZARD_STEPS[currentIndex + 1].id);
    }
  };

  const goToPreviousStep = () => {
    if (!canGoPrevious()) return;

    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(WIZARD_STEPS[currentIndex - 1].id);
    }
  };

  const goToStep = (step: WizardStep) => {
    const stepIndex = WIZARD_STEPS.findIndex(s => s.id === step);
    const currentIndex = getCurrentStepIndex();

    // Can only go to previous steps or next step if current is completed
    if (stepIndex < currentIndex || (stepIndex === currentIndex + 1 && canGoNext())) {
      setCurrentStep(step);
    }
  };

  const handleMemberSelect = useCallback(async (memberId: string) => {
    const membershipData = members.find(m => m.member?.id === memberId);
    if (!membershipData || !membershipData.member) {
      return;
    }

    setLoadingPackages(true);
    try {
      const packagesResult = await getMemberPackages(gymId, membershipData.member.id);
      
      if (packagesResult.success && packagesResult.data) {
        setSelectedMemberData({
          member: membershipData.member,
          membershipId: membershipData.id,
          packages: packagesResult.data,
        });
        
        // Mark member step as completed
        setCompletedSteps(prev => new Set([...prev, 'member']));
        
        // Auto-advance to next step
        if (currentStep === 'member') {
          setCurrentStep('trainer-package');
        }
      } else {
        toast.error('Üye paketleri yüklenirken hata oluştu');
      }
    } catch (error) {
      console.error('Error loading member packages:', error);
      toast.error('Üye paketleri yüklenirken hata oluştu');
    } finally {
      setLoadingPackages(false);
    }
  }, [gymId, members, currentStep]);

  // Otomatik üye seçimi
  useEffect(() => {
    if (preSelectedMemberId && members.length > 0) {
      handleMemberSelect(preSelectedMemberId);
    }
  }, [preSelectedMemberId, members, handleMemberSelect]);

  const handleTrainerSelect = (trainerId: string) => {
    setSelectedTrainer(trainerId);
  };

  const handlePackageSelect = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const addAppointment = () => {
    const newAppointment: AppointmentData = {
      id: Date.now().toString(),
      date: '',
      time: '',
      packageId: selectedPackage,
      sessionNumber: 1,
    };
    setAppointments([...appointments, newAppointment]);
  };

  const removeAppointment = (id: string) => {
    setAppointments(appointments.filter(apt => apt.id !== id));
  };

  const updateAppointment = async (id: string, field: keyof AppointmentData, value: string | number) => {
    const updatedAppointments = appointments.map(apt =>
      apt.id === id ? { ...apt, [field]: value } : apt
    );
    setAppointments(updatedAppointments);

    // Check for conflicts when date or time changes
    if ((field === 'date' || field === 'time') && selectedTrainer) {
      const appointment = updatedAppointments.find(apt => apt.id === id);
      if (appointment && appointment.date && appointment.time) {
        await checkAppointmentConflict(appointment);
      }
    }
  };

  const checkAppointmentConflict = async (appointment: AppointmentData) => {
    if (!selectedTrainer || !appointment.date || !appointment.time) return;

    const packageData = getSelectedPackageData();
    if (!packageData?.gym_package) return;

    try {
      const appointmentDateTime = new Date(`${appointment.date}T${appointment.time}:00`);
      if (isNaN(appointmentDateTime.getTime())) return;

      // First, check for existing group appointments
      const existingGroupResult = await findExistingGroupAppointment(
        selectedTrainer,
        appointmentDateTime.toISOString(),
        gymId,
        packageData.gym_package.package_type || 'appointment_standard'
      );

      if (existingGroupResult.success && existingGroupResult.data) {
        // Found existing group appointment that can accommodate more participants
        setAppointments(prev => prev.map(apt => {
          if (apt.id === appointment.id) {
            return {
              ...apt,
              hasConflict: false,
              canJoinGroup: true,
              existingAppointmentId: existingGroupResult.data!.id,
              conflictMessage: 'Bu saatte grup randevusu var, katılabilirsiniz'
            };
          }
          return apt;
        }));
        return;
      }

      // If no existing group appointment, check for conflicts
      const availabilityResult = await checkTrainerAvailability(
        selectedTrainer,
        appointmentDateTime.toISOString(),
        packageData.gym_package.session_duration_minutes || 60,
        gymId,
      );

      if (availabilityResult.success) {
        const { available, conflictingAppointments } = availabilityResult.data!;

        setAppointments(prev => prev.map(apt => {
          if (apt.id === appointment.id) {
            if (!available && conflictingAppointments.length > 0) {
              const conflictTimes = conflictingAppointments.map(apt =>
                new Date(apt.appointment_date).toLocaleTimeString('tr-TR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })
              ).join(', ');

              return {
                ...apt,
                hasConflict: true,
                conflictMessage: `Çakışan randevular: ${conflictTimes}`,
                canJoinGroup: false
              };
            } else {
              return {
                ...apt,
                hasConflict: false,
                canJoinGroup: false,
                conflictMessage: undefined,
                existingAppointmentId: undefined
              };
            }
          }
          return apt;
        }));
      }
    } catch (error) {
      console.error('Conflict check error:', error);
      // Show error to user
      setAppointments(prev => prev.map(apt => {
        if (apt.id === appointment.id) {
          return {
            ...apt,
            hasConflict: true,
            conflictMessage: error instanceof Error ? error.message : 'Randevu kontrolü başarısız',
            canJoinGroup: false
          };
        }
        return apt;
      }));
    }
  };

  const getSelectedPackageData = () => {
    return selectedMemberData?.packages.find(pkg => pkg.id === selectedPackage);
  };

  const handleSubmit = async () => {
    if (!selectedMemberData || !selectedTrainer || !selectedPackage || appointments.length === 0) {
      toast.error('Lütfen tüm alanları doldurun ve en az bir randevu ekleyin');
      return;
    }

    const packageData = getSelectedPackageData();
    if (!packageData?.gym_package) {
      toast.error('Seçilen paket bilgisi bulunamadı');
      return;
    }

    // Tarih ve saat kontrolü
    const invalidAppointments = appointments.filter(apt => !apt.date || !apt.time);
    if (invalidAppointments.length > 0) {
      toast.error('Lütfen tüm randevular için tarih ve saat seçin');
      return;
    }

    // Çakışma kontrolü
    const conflictedAppointments = appointments.filter(apt => apt.hasConflict && !apt.canJoinGroup);
    if (conflictedAppointments.length > 0) {
      toast.error('Çakışan randevular var. Lütfen farklı saatler seçin.');
      return;
    }

    setIsSubmitting(true);
    try {
      for (const appointment of appointments) {
        // Tarih ve saat formatını kontrol et
        if (!appointment.date || !appointment.time) {
          throw new Error('Lütfen tüm randevular için tarih ve saat seçin');
        }

        // ISO formatında tarih oluştur
        const appointmentDateTime = new Date(`${appointment.date}T${appointment.time}:00`);
        if (isNaN(appointmentDateTime.getTime())) {
          throw new Error('Geçersiz tarih veya saat formatı');
        }

        let appointmentId: string;

        // Mevcut grup randevusuna katılma durumu kontrolü
        if (appointment.canJoinGroup && appointment.existingAppointmentId) {
          // Var olan randevuya katılım - sadece participant eklenir
          appointmentId = appointment.existingAppointmentId;
          toast.info('Mevcut grup randevusuna katılıyorsunuz');
        } else {
          // Yeni randevu oluşturma - hem appointment hem participant eklenir
          const appointmentFormData = new FormData();
          appointmentFormData.append('gym_id', gymId);
          appointmentFormData.append('appointment_date', appointmentDateTime.toISOString());
          appointmentFormData.append('appointment_type', packageData.gym_package.package_type || 'appointment_standard');
          appointmentFormData.append('max_participants', packageData.gym_package.max_participants?.toString() || '1');
          appointmentFormData.append('trainer_profile_id', selectedTrainer);
          appointmentFormData.append('gym_package_id', packageData.gym_package.id);

          const appointmentResult = await createAppointment(
            appointmentFormData,
            gymId
          );

          if (!appointmentResult.success) {
            throw new Error(appointmentResult.error || 'Randevu oluşturulamadı');
          }

          appointmentId = appointmentResult.data!.id;
          toast.success('Yeni randevu oluşturuldu');
        }

        // Katılımcı ekle
        const participantFormData = new FormData();
        participantFormData.append('appointment_id', appointmentId);
        participantFormData.append('gym_membership_package_id', selectedPackage);
        const participantResult = await addAppointmentParticipant(participantFormData);

        if (!participantResult.success) {
          throw new Error(participantResult.error || 'Katılımcı eklenemedi');
        }
      }

      // Başarı ekranını göster
      setCreatedAppointmentCount(appointments.length);
      setShowSuccessScreen(true);
    } catch (error) {
      console.error('Error creating appointments:', error);
      toast.error(error instanceof Error ? error.message : 'Randevular oluşturulurken hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render wizard header with progress
  const renderWizardHeader = () => {
    const currentIndex = getCurrentStepIndex();
    const progress = ((currentIndex + 1) / WIZARD_STEPS.length) * 100;

    return (
      <div className="mb-8">
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              Adım {currentIndex + 1} / {WIZARD_STEPS.length}
            </span>
            <span className="text-sm font-medium text-muted-foreground">
              %{Math.round(progress)} tamamlandı
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step Navigation */}
        <div className="flex items-center justify-between">
          {WIZARD_STEPS.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted = completedSteps.has(step.id);
            const isAccessible = index <= currentIndex || isCompleted;
            const Icon = step.icon;

            return (
              <div key={step.id} className="flex flex-col items-center flex-1">
                <button
                  onClick={() => isAccessible && goToStep(step.id)}
                  disabled={!isAccessible}
                  className={`
                    relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200
                    ${isActive
                      ? 'border-primary bg-primary text-primary-foreground shadow-lg scale-110'
                      : isCompleted
                        ? 'border-green-500 bg-green-500 text-white hover:scale-105'
                        : isAccessible
                          ? 'border-muted-foreground bg-background text-muted-foreground hover:border-primary hover:text-primary'
                          : 'border-muted bg-muted text-muted-foreground cursor-not-allowed'
                    }
                  `}
                >
                  <Icon className="w-5 h-5" />
                </button>
                <div className="mt-2 text-center">
                  <div className={`text-sm font-medium ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 max-w-24">
                    {step.description}
                  </div>
                </div>
                {index < WIZARD_STEPS.length - 1 && (
                  <div className={`absolute top-6 left-full w-full h-0.5 -translate-y-1/2 ${
                    isCompleted ? 'bg-green-500' : 'bg-muted'
                  }`} style={{ zIndex: -1 }} />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 'member':
        return renderMemberSelection();
      case 'trainer-package':
        return renderTrainerPackageSelection();
      case 'appointments':
        return renderAppointmentSelection();
      case 'summary':
        return renderSummary();
      default:
        return null;
    }
  };

  // Render navigation buttons
  const renderNavigation = () => (
    <div className="flex items-center justify-between border-t pt-6">
      <Button
        variant="outline"
        onClick={goToPreviousStep}
        className="flex items-center gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        {canGoPrevious() && 'Önceki Adım'}
      </Button>

      {currentStep !== 'summary' ? (
        <Button
          onClick={goToNextStep}
          disabled={!canGoNext()}
          className="flex items-center gap-2"
        >
          Sonraki Adım
          <ChevronRight className="h-4 w-4" />
        </Button>
      ) : (
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !canGoNext()}
          className="flex min-w-[140px] items-center gap-2"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Oluşturuluyor...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4" />
              Randevuları Oluştur
            </>
          )}
        </Button>
      )}
    </div>
  );

  // Step 1: Member Selection
  const renderMemberSelection = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Üye Seçimi</h2>
        <p className="text-muted-foreground">Randevu alacak üyeyi seçin</p>
      </div>

      <div className="space-y-4">
        <Label htmlFor="member" className="text-base font-medium">Üye Seçin</Label>
        <Select
          onValueChange={handleMemberSelect}
          disabled={loadingPackages}
          value={selectedMemberData?.member?.id || ''}
        >
          <SelectTrigger className="h-12">
            <SelectValue
              placeholder={loadingPackages ? 'Yükleniyor...' : 'Üye seçin'}
            />
          </SelectTrigger>
          <SelectContent>
            {members
              .filter(membership => membership.member)
              .map(membership => (
                <SelectItem
                  key={membership.member!.id}
                  value={membership.member!.id}
                >
                  <div className="flex items-center gap-3 py-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={membership.member!.avatar_url || undefined}
                      />
                      <AvatarFallback>
                        {membership.member!.full_name.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{membership.member!.full_name}</div>
                      <div className="text-sm text-muted-foreground">
                        {membership.member!.email}
                      </div>
                    </div>
                  </div>
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        {loadingPackages && (
          <div className="text-muted-foreground flex items-center gap-2 text-sm">
            <Loader2 className="h-4 w-4 animate-spin" />
            Paketler yükleniyor...
          </div>
        )}
      </div>

      {/* Selected Member Info */}
      {selectedMemberData && selectedMemberData.member && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800 animate-card-hover">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={selectedMemberData.member.avatar_url || undefined} />
                <AvatarFallback className="text-lg">
                  {selectedMemberData.member.full_name.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-xl font-bold">{selectedMemberData.member.full_name}</h3>
                <p className="text-muted-foreground">{selectedMemberData.member.email}</p>
                <Badge variant="secondary" className="mt-1">
                  {selectedMemberData.packages.length} aktif paket
                </Badge>
              </div>
            </div>

            <div className="grid gap-3">
              {selectedMemberData.packages.map(pkg => (
                <div
                  key={pkg.id}
                  className="bg-background/80 backdrop-blur-sm rounded-lg border p-4 flex items-center justify-between transition-all duration-200 hover:bg-background/90"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse-ring"></div>
                    <div>
                      <div className="font-medium">{pkg.gym_package?.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {pkg.gym_package?.package_type === 'appointment_vip' ? 'VIP Paket' : 'Standart Paket'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-lg">{pkg.remaining_sessions}</div>
                    <div className="text-sm text-muted-foreground">seans kaldı</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Step 2: Trainer & Package Selection
  const renderTrainerPackageSelection = () => (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Antrenör & Paket Seçimi</h2>
        <p className="text-muted-foreground">Antrenör ve paket seçimi yapın</p>
      </div>

      {/* Trainer Selection */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Antrenör Seçin</Label>
        <div className="grid gap-4 md:grid-cols-2">
          {trainers.map(trainer => (
            <Card
              key={trainer.id}
              className={`cursor-pointer animate-card-hover ${
                selectedTrainer === trainer.id
                  ? 'ring-2 ring-primary bg-primary/5 shadow-lg'
                  : 'hover:bg-muted/50'
              }`}
              onClick={() => handleTrainerSelect(trainer.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={trainer.avatar_url || undefined} />
                    <AvatarFallback>
                      {trainer.full_name?.slice(0, 2).toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold">{trainer.full_name}</h3>
                    <p className="text-sm text-muted-foreground">{trainer.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Dumbbell className="w-4 h-4 text-primary" />
                      <span className="text-sm">Antrenör</span>
                    </div>
                  </div>
                  {selectedTrainer === trainer.id && (
                    <CheckCircle className="w-6 h-6 text-primary animate-success-bounce" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Package Selection */}
      {selectedTrainer && selectedMemberData && (
        <div className="space-y-4 animate-step-slide-in">
          <Label className="text-base font-medium">Paket Seçin</Label>
          <div className="grid gap-4">
            {selectedMemberData.packages.map(pkg => (
              <Card
                key={pkg.id}
                className={`cursor-pointer animate-card-hover ${
                  selectedPackage === pkg.id
                    ? 'ring-2 ring-primary bg-primary/5 shadow-lg'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => handlePackageSelect(pkg.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`w-4 h-4 rounded-full transition-all duration-300 ${
                        pkg.gym_package?.package_type === 'appointment_vip'
                          ? 'bg-yellow-500 shadow-lg shadow-yellow-500/50'
                          : 'bg-blue-500 shadow-lg shadow-blue-500/50'
                      }`}></div>
                      <div>
                        <h3 className="font-semibold">{pkg.gym_package?.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {pkg.gym_package?.package_type === 'appointment_vip' ? 'VIP Paket' : 'Standart Paket'}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-sm">
                          <div className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            <span>Max {pkg.gym_package?.max_participants} kişi</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{pkg.gym_package?.session_duration_minutes} dk</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">{pkg.remaining_sessions}</div>
                      <div className="text-sm text-muted-foreground">seans kaldı</div>
                      {selectedPackage === pkg.id && (
                        <CheckCircle className="w-6 h-6 text-primary mt-2 ml-auto animate-success-bounce" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // Step 3: Appointment Selection
  const renderAppointmentSelection = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Randevu Tarihleri</h2>
        <p className="text-muted-foreground">Tarih ve saat belirleyin</p>
      </div>

      <div className="flex justify-between items-center">
        <Button onClick={addAppointment} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Randevu Ekle
        </Button>
      </div>

      {appointments.length === 0 ? (
        <Card className="border-dashed border-2">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CalendarDays className="w-16 h-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Henüz randevu eklenmedi</h3>
            <p className="text-muted-foreground text-center mb-4">
              Yukarıdaki &quot;Randevu Ekle&quot; butonuna tıklayarak randevu ekleyin
            </p>
            <Button onClick={addAppointment} variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              İlk Randevuyu Ekle
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {appointments.map((appointment, index) => {
            const borderColor = appointment.hasConflict
              ? (appointment.canJoinGroup ? 'border-l-yellow-500' : 'border-l-red-500')
              : 'border-l-green-500';

            return (
              <Card key={appointment.id} className={`border-l-4 ${borderColor} transition-all duration-200 hover:shadow-md`}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold">Randevu {index + 1}</h4>
                        {appointment.hasConflict && (
                          <Badge variant={appointment.canJoinGroup ? "secondary" : "destructive"} className="mt-1">
                            {appointment.canJoinGroup ? 'Grup Katılımı Mümkün' : 'Çakışma Var'}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAppointment(appointment.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Conflict Message */}
                  {appointment.conflictMessage && (
                    <div className={`mb-4 p-4 rounded-lg border ${
                      appointment.canJoinGroup
                        ? 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-950/20 dark:text-yellow-200 dark:border-yellow-800'
                        : 'bg-red-50 text-red-800 border-red-200 dark:bg-red-950/20 dark:text-red-200 dark:border-red-800'
                    }`}>
                      <div className="flex items-start gap-2">
                        {appointment.canJoinGroup ? (
                          <Users className="w-5 h-5 mt-0.5" />
                        ) : (
                          <X className="w-5 h-5 mt-0.5" />
                        )}
                        <div>
                          <p className="font-medium mb-1">
                            {appointment.canJoinGroup ? 'Grup Randevusuna Katılım' : 'Çakışma Tespit Edildi'}
                          </p>
                          <p className="text-sm">{appointment.conflictMessage}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`date-${appointment.id}`} className="text-sm font-medium">Tarih</Label>
                      <Input
                        id={`date-${appointment.id}`}
                        type="date"
                        value={appointment.date}
                        onChange={(e) => updateAppointment(appointment.id, 'date', e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                        className="h-10"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`time-${appointment.id}`} className="text-sm font-medium">Saat</Label>
                      <Input
                        id={`time-${appointment.id}`}
                        type="time"
                        value={appointment.time}
                        onChange={(e) => updateAppointment(appointment.id, 'time', e.target.value)}
                        className="h-10"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );

  // Step 4: Summary
  const renderSummary = () => {
    const selectedPackageData = selectedMemberData?.packages.find(pkg => pkg.id === selectedPackage);
    const selectedTrainerData = trainers.find(trainer => trainer.id === selectedTrainer);

    return (
      <div className="space-y-6">
        <div className="mb-8 text-center">
          <h2 className="mb-2 text-2xl font-bold">Özet & Onay</h2>
          <p className="text-muted-foreground">
            Bilgileri kontrol edin ve onaylayın
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Member Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Üye Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedMemberData?.member && (
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={selectedMemberData.member.avatar_url || undefined}
                    />
                    <AvatarFallback>
                      {selectedMemberData.member.full_name
                        .slice(0, 2)
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">
                      {selectedMemberData.member.full_name}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {selectedMemberData.member.email}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Trainer Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Dumbbell className="h-5 w-5" />
                Antrenör Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedTrainerData && (
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={selectedTrainerData.avatar_url || undefined}
                    />
                    <AvatarFallback>
                      {selectedTrainerData.full_name?.slice(0, 2).toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">
                      {selectedTrainerData.full_name}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {selectedTrainerData.email}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Package Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Paket Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedPackageData?.gym_package && (
              <div className="bg-muted/50 flex items-center justify-between rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`h-4 w-4 rounded-full ${
                      selectedPackageData.gym_package.package_type ===
                      'appointment_vip'
                        ? 'bg-yellow-500'
                        : 'bg-blue-500'
                    }`}
                  ></div>
                  <div>
                    <h3 className="font-semibold">
                      {selectedPackageData.gym_package.name}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {selectedPackageData.gym_package.package_type ===
                      'appointment_vip'
                        ? 'VIP Paket'
                        : 'Standart Paket'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold">
                    {selectedPackageData.remaining_sessions}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    seans kaldı
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Appointments Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Randevu Özeti ({appointments.length} randevu)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {appointments.map((appointment, index) => (
                <div
                  key={appointment.id}
                  className="bg-muted/50 flex items-center justify-between rounded-lg p-3"
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 flex h-6 w-6 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-bold">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium">
                        {appointment.date &&
                          new Date(appointment.date).toLocaleDateString(
                            'tr-TR',
                            {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                            }
                          )}
                      </div>
                      <div className="text-muted-foreground text-sm">
                        Saat: {appointment.time} • Seans:{' '}
                        {appointment.sessionNumber}
                      </div>
                    </div>
                  </div>
                  {appointment.hasConflict && (
                    <Badge
                      variant={
                        appointment.canJoinGroup ? 'secondary' : 'destructive'
                      }
                    >
                      {appointment.canJoinGroup ? 'Grup' : 'Çakışma'}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Final Confirmation */}
        <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 dark:border-green-800 dark:from-green-950/20 dark:to-emerald-950/20">
          <CardContent className="p-6 text-center">
            <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-600" />
            <h3 className="mb-2 text-lg font-semibold">
              Randevular Oluşturulmaya Hazır
            </h3>
            <p className="text-muted-foreground">
              {appointments.length} randevu oluşturulacak. Onaylamak için
              &quot;Randevuları Oluştur&quot; butonuna tıklayın.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Başarı ekranını göster
  if (showSuccessScreen) {
    return (
      <AppointmentSuccessScreen
        gymId={gymId}
        appointmentCount={createdAppointmentCount}
      />
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {renderWizardHeader()}

      {/* Step Content */}
      <Card className="min-h-[400px] transition-all duration-300 hover:shadow-lg">
        <CardContent className="p-8">
          <div className="animate-step-slide-in">
            {renderStepContent()}
          </div>
        </CardContent>
      </Card>

      {renderNavigation()}
    </div>
  );
}
