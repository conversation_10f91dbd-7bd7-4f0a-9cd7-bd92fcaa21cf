/**
 * Gym business domain types
 *
 * Extracted from findGym actions and gym-related components
 */

import type { Gyms, GymPackages } from '../database/tables';

/**
 * Gym card data for search results and listings
 */
export interface GymCardData {
  slug: string | null;
  name: string;
  city: string | null;
  district: string | null;
  logo_url: string | null;
  cover_image_url: string | null;
  average_rating: number | null;
  review_count: number;
  relevance_score?: number;
  company?: {
    logo_url: string | null;
    name: string;
  };
}

/**
 * Gym search filters
 */
export interface GymSearchFilters {
  query?: string;
  city?: string;
  district?: string;
  features?: string[];
  page?: number;
  limit?: number;
  sortBy?: 'rating' | 'newest' | 'popular' | 'relevance';
  minRating?: number;
  minReviewCount?: number;
  searchType?: 'basic' | 'fulltext' | 'similarity';
}

/**
 * Gym search result with metadata
 */
export interface GymSearchResult {
  gyms: GymCardData[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
  searchMeta?: {
    searchType: string;
    processingTime: number;
    suggestions?: string[];
  };
}

/**
 * Trainer data for gym details
 */
export interface TrainerData {
  profile_id: string;
  full_name: string;
  specialization?: string;
  certification_level?: string;
  experience_years?: number;
  bio?: string;
}

/**
 * Review data for gym details
 */
export interface ReviewData {
  id: string;
  rating: number | null;
  comment: string | null;
  created_at: string | null;
  member: {
    full_name: string;
  };
}

/**
 * Membership status for gym details
 */
export interface MembershipStatus {
  hasActiveMembership: boolean;
  isGymOwner: boolean;
  requestSent: boolean;
  pendingInvitation: any | null; // GymInvitation type
}

/**
 * Gym detail data with related information
 */
export interface GymDetailData extends Gyms {
  packages?: GymPackages[];
  trainers?: TrainerData[];
  reviews?: ReviewData[];
  membershipStatus?: MembershipStatus;
}

/**
 * Gym statistics
 */
export interface GymStats {
  totalGyms: number;
  totalCities: number;
  totalDistricts: number;
  averageRating: number;
}

/**
 * Gym revenue data for analytics
 */
export interface GymRevenueData {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  totalMembers: number;
  activeMembers: number;
  averageRevenuePerMember: number;
  monthlyData: {
    month: string;
    revenue: number;
    memberCount: number;
  }[];
  packageRevenue: {
    packageName: string;
    revenue: number;
    memberCount: number;
    percentage: number;
  }[];
}

/**
 * Gym member with details for management
 */
export interface GymMemberWithDetails {
  id: string;
  memberId: string;
  fullName: string;
  email: string;
  age?: number;
  status: 'active' | 'passive';
  joinDate: string;
  created_at: string;
  approvedAt?: string | null;
  lastActivity?: string | null;
  user?: {
    avatar_url?: string | null;
    name?: string;
    surname?: string;
    email: string;
  };
  currentPackage?: {
    id: string;
    name: string;
    endDate: string;
    status: 'active' | 'expired';
    purchasePrice: number;
  };
  memberDetails?: {
    height_cm?: number;
    weight_kg?: number;
    gender?: string;
    fitness_goal?: string;
    age?: number;
  };
}

/**
 * Update member data interface
 */
export interface UpdateMemberData {
  fullName?: string;
  email?: string;
  age?: number;
  status?: 'active' | 'passive';
}
